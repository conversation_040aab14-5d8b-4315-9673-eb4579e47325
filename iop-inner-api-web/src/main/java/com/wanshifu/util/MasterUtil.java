package com.wanshifu.util;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.iop.inner.api.domains.enums.MasterOrderLabelEnum;
import com.wanshifu.iop.inner.api.domains.enums.OrderPropertyEnum;
import com.wanshifu.master.innerapi.domains.request.order.info.BatchOrderLabelRqt;
import com.wanshifu.master.innerapi.domains.response.order.BatchOrderLabelResp;
import com.wanshifu.master.innerapi.service.api.order.OrderMessageApi;
import com.wanshifu.order.offer.api.NormalOrderListApi;
import com.wanshifu.order.offer.domains.api.request.CommonBranchGetMasterTagReq;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date Created in 2023/5/12 10:04
 */
@Slf4j
@Component
public class MasterUtil {
    @Resource
    private OrderMessageApi orderMessageApi;
    @Resource
    private NormalOrderListApi normalOrderListApi;

    /**
     * 查询单个订单标签-旧
     * @param orderId
     * @return
     */
    public List<String> queryMasterSingleOrderTagList(Long orderId) {
        if(ObjectUtils.isEmpty(orderId)){
            return null;
        }
        BatchOrderLabelRqt rqt=new BatchOrderLabelRqt();
        rqt.setOrderIdList(Arrays.asList(orderId));
        try {
            BatchOrderLabelResp resp = orderMessageApi.batchOrderLabel(rqt);
            if(ObjectUtils.isEmpty(resp)|| CollectionUtils.isEmpty(resp.getList())){
                return null;
            }
            List<BatchOrderLabelResp.LabelVo>  labelRespList=resp.getList();
            Optional<BatchOrderLabelResp.LabelVo> labelOptional=labelRespList.stream().filter(f->orderId.equals(f.getOrderId())).findFirst();
            if(labelOptional.isPresent()){
                return labelOptional.get().getOrderLabelList();
            }
        }catch (Exception e){
            log.error("查询师傅标签接口/serveInfo/batchOrderLabel异常:orderId:{},error:{}",orderId,e.getMessage());
            throw new BusException("查询师傅标签接口/serveInfo/batchOrderLabel失败,error:{}",e.getMessage());
        }
        return null;
    }

    /**
     * 查询单个订单标签-新,旧接口是订单维度,新接口时订单+师傅维度,符合师傅端最新业务逻辑
     * @param orderIds
     * @param masterId
     * @return
     */
    public List<OrderExclusiveTagResp> getMasterOrderTagList(List<Long> orderIds, Long masterId) {
        if(CollectionUtils.isEmpty(orderIds) || Objects.isNull(masterId)){
            return null;
        }
        CommonBranchGetMasterTagReq tagReq = new CommonBranchGetMasterTagReq();
        tagReq.setOrderIds(orderIds);
        tagReq.setMasterId(masterId);
        try {
            List<OrderExclusiveTagResp> orderExclusiveTagResps = normalOrderListApi.commonBranchGetMasterTag(tagReq);
            log.info("查询师傅订单标签接口,tagReq={}，commonBranchGetMasterTag返回结果={}",JSONObject.toJSONString(tagReq),JSONObject.toJSONString(orderExclusiveTagResps));
            return orderExclusiveTagResps;
        }catch (Exception e){
            log.error("查询师傅订单标签接口/normalOrder/list/commonBranchGetMasterTag异常:orderId:{},masterId:{},error:{}", JSONObject.toJSONString(orderIds),masterId,e);
            throw new BusException("查询师傅订单标签接口/normalOrder/list/commonBranchGetMasterTag失败,error:{}",e.getMessage());
        }
    }

    /**
     * 构建师傅其他订单限制条件 ljf 要不要改
     * @return
     */
    public List<String> buildMasterOtherOrderFilterCondition(boolean isOrderPackage,Long orderId, Long masterId){
        List<String> result = new ArrayList<>();
        if(isOrderPackage){
            result.add(ActivityConstant.IS_ORDER_PACKAGE);
        }
        List<OrderExclusiveTagResp> masterOrderTagList = this.getMasterOrderTagList(Arrays.asList(orderId), masterId);
        if(CollectionUtils.isEmpty(masterOrderTagList)){
            return result;
        }
        for(OrderExclusiveTagResp tagResp : masterOrderTagList){
            if(MasterOrderLabelEnum.MASTER_SHOP.label.equals(tagResp.getTagType())){
                //一级标签是师傅店铺
                result.add(ActivityConstant.IS_ORDER_SHOP);
            } else if(MasterOrderLabelEnum.EXCLUSIVE.label.equals(tagResp.getTagType()) && MasterOrderLabelEnum.CONTRACT.label.equals(tagResp.getTagName())){
                //一级标签是专属且二级标签是合约
                result.add(ActivityConstant.IS_ORDER_CONTRACT);
            }
        }
        return result;
    }

//    /**
//     * 构建师傅订单属性条件
//     * @return
//     */
//    public HashMap<String,Boolean> buildMasterPropertyMap(boolean isOrderPackage,Long orderId, Long masterId){
//        HashMap<String,Boolean> result = new HashMap<>();
//        if(isOrderPackage){
//            result.put(ActivityConstant.IS_ORDER_PACKAGE,true);
//        } else {
//            result.put(ActivityConstant.IS_ORDER_PACKAGE,false);
//        }
//        List<OrderExclusiveTagResp> masterOrderTagList = this.getMasterOrderTagList(orderId, masterId);
//        if(CollectionUtils.isEmpty(masterOrderTagList)){
//            return result;
//        }
//        for(OrderExclusiveTagResp tagResp : masterOrderTagList){
//            result.put(ActivityConstant.IS_ORDER_SHOP,false);
//            result.put(ActivityConstant.IS_ORDER_CONTRACT,false);
//            if(MasterOrderLabelEnum.MASTER_SHOP.label.equals(tagResp.getTagType())){
//                //一级标签是师傅店铺
//                result.put(ActivityConstant.IS_ORDER_SHOP,true);
//                result.put(ActivityConstant.IS_ORDER_CONTRACT,false);
//            } else if(MasterOrderLabelEnum.EXCLUSIVE.label.equals(tagResp.getTagType()) && MasterOrderLabelEnum.CONTRACT.label.equals(tagResp.getTagName())){
//                //一级标签是专属且二级标签是合约
//                result.put(ActivityConstant.IS_ORDER_SHOP,false);
//                result.put(ActivityConstant.IS_ORDER_CONTRACT,true);
//            }
//        }
//        return result;
//    }

//    /**
//     * 构建师傅订单属性条件
//     * @return
//     */
//    public List<String> buildMasterPropertyList(boolean isOrderPackage,Long orderId, Long masterId){
//        List<String> result = new ArrayList<>();
//        if(isOrderPackage){
//            result.add(ActivityConstant.IS_ORDER_PACKAGE);
//        }
//        List<OrderExclusiveTagResp> masterOrderTagList = this.getMasterOrderTagList(orderId, masterId);
//        if(CollectionUtils.isEmpty(masterOrderTagList)){
//            return result;
//        }
//        for(OrderExclusiveTagResp tagResp : masterOrderTagList){
//            if(MasterOrderLabelEnum.MASTER_SHOP.label.equals(tagResp.getTagType())){
//                //一级标签是师傅店铺
//                result.add(ActivityConstant.IS_ORDER_SHOP);
//            } else if(MasterOrderLabelEnum.EXCLUSIVE.label.equals(tagResp.getTagType()) && MasterOrderLabelEnum.CONTRACT.label.equals(tagResp.getTagName())){
//                //一级标签是专属且二级标签是合约
//                result.add(ActivityConstant.IS_ORDER_CONTRACT);
//            }
//        }
//        return result;
//    }

    /**
     * 构建师傅订单属性条件
     * @return
     */
    public List<String> buildMasterPropertyList(boolean isOrderPackage,Long orderId, Long masterId){

        List<OrderExclusiveTagResp> masterOrderTagList = this.getMasterOrderTagList(Arrays.asList(orderId), masterId);
        return this.buildPropertyList(masterOrderTagList);
    }

    /**
     * 构建订单属性条件
     * @param orderExclusiveTagRespList
     * @return
     */
    public List<String> buildPropertyList(List<OrderExclusiveTagResp> orderExclusiveTagRespList){
        List<String> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(orderExclusiveTagRespList)){
            return result;
        }
        for(OrderExclusiveTagResp tagResp : orderExclusiveTagRespList){
            if(MasterOrderLabelEnum.MASTER_SHOP.label.equals(tagResp.getTagType())){
                //师傅店铺
                result.add(OrderPropertyEnum.IS_ORDER_SHOP.code);
            } else if(MasterOrderLabelEnum.AGREEMENT_MASTER.label.equals(tagResp.getTagType())){
                //一级标签是协议
                if(MasterOrderLabelEnum.EXCLUSIVE.label.equals(tagResp.getTagName())){
                    //协议师傅(专属):isOrderAgreementExclusive
                    result.add(OrderPropertyEnum.IS_ORDER_AGREEMENT_EXCLUSIVE.code);
                } else if(MasterOrderLabelEnum.CONTRACT.label.equals(tagResp.getTagName())){
                    //协议师傅(合约):isOrderAgreementContract
                    result.add(OrderPropertyEnum.IS_ORDER_AGREEMENT_CONTRACT.code);
                } else if(MasterOrderLabelEnum.BRAND.label.equals(tagResp.getTagName())){
                    //协议师傅(品牌)isOrderAgreementBrand
                    result.add(OrderPropertyEnum.IS_ORDER_AGREEMENT_BRAND.code);
                } else if(MasterOrderLabelEnum.NEW_CONTRACT.label.equals(tagResp.getTagName())){
                    //新合约师傅:isNewOrderContract
                    result.add(OrderPropertyEnum.IS_NEW_ORDER_CONTRACT.code);
                }
            } else if(MasterOrderLabelEnum.NEW_MODEL.label.equals(tagResp.getTagType())){
                //样板城市(样板城市)isOrderNewModel
                result.add(OrderPropertyEnum.IS_ORDER_NEW_MODEL.code);
            } else if(MasterOrderLabelEnum.PACKAGE_ORDER.label.equals(tagResp.getTagType())){
                //订单包订单
                result.add(OrderPropertyEnum.IS_ORDER_PACKAGE.code);
            } else if(MasterOrderLabelEnum.MONEY_SAVING_CARD.label.equals(tagResp.getTagType())){
                //团购订单
                result.add(OrderPropertyEnum.IS_ORDER_GROUP_BUY.code);
            } else if (MasterOrderLabelEnum.DEFINITE_OFFER_CONTAIN_MERCHANT_RULE.label.equals(tagResp.getTagType())){
                //报抢结合-报价订单
                result.add(OrderPropertyEnum.IS_ORDER_QUOTED_GRAB_QUOTED.code);
            } else if (MasterOrderLabelEnum.DEFINITE_ONLY_OFFER.label.equals(tagResp.getTagType())) {
                //一口价订单-纯报价
                result.add(OrderPropertyEnum.IS_ORDER_DEFINITE_ONLY_OFFER.code);
            } else if (MasterOrderLabelEnum.TOC_CORPORATE_SERVICES.label.equals(tagResp.getTagType())){
                //企服订单
                result.add(OrderPropertyEnum.IS_CORPORATE_SERVICES.code);
            } else if (MasterOrderLabelEnum.SKILL_TASK_ORDER_GRAB.label.equals(tagResp.getTagType())){
                //技能验证单
                result.add(OrderPropertyEnum.IS_SKILL_TASK_ORDER_GRAB.code);
            }
//            else if(MasterOrderLabelEnum.BZ_SHOW_LABEL.label.equals(tagResp.getTagType()))
//                //招募师傅:isOrderRecruit
//                result.add(OrderPropertyEnum.IS_ORDER_RECRUIT.code);
        }
        return result;
    }

}
