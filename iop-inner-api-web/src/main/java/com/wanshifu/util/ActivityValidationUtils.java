package com.wanshifu.util;

import com.wanshifu.constant.ActivityServiceConstants;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.iop.activity.domain.api.response.GetActivityDetailResp;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Utility class for activity-related validations
 * Centralizes validation logic to improve maintainability
 * 
 * <AUTHOR>
 */
public final class ActivityValidationUtils {

    private ActivityValidationUtils() {
        // Utility class
    }

    /**
     * Validate if app access is allowed
     * 
     * @param tokenApp app token
     * @param appEnable whether app is enabled
     * @return true if access is allowed
     */
    public static boolean isAppAccessAllowed(String tokenApp, Boolean appEnable) {
        boolean isApp = !StringUtils.isEmpty(tokenApp);
        return appEnable || !isApp;
    }

    /**
     * Validate activity state
     * 
     * @param activityDetail activity detail
     * @throws BusException if activity is canceled or expired
     */
    public static void validateActivityState(GetActivityDetailResp activityDetail) {
        String activityState = activityDetail.getActivityBaseInfo().getActivityState();
        if (ActivityServiceConstants.ACTIVITY_STATE_CANCELED.equals(activityState) || 
            ActivityServiceConstants.ACTIVITY_STATE_EXPIRY.equals(activityState)) {
            throw new BusException(
                ActivityServiceConstants.ERROR_GET_ACTIVITY_DETAIL_FAIL, 
                ActivityServiceConstants.MSG_ACTIVITY_CANCELED
            );
        }
    }

    /**
     * Check if user is logged in
     * 
     * @param userId user ID
     * @return true if user is logged in
     */
    public static boolean isUserLoggedIn(String userId) {
        return !StringUtils.isEmpty(userId);
    }

    /**
     * Check if activity has started
     * 
     * @param activityStartTime activity start time
     * @param activityEndTime activity end time
     * @return 1 if started, 0 if not
     */
    public static Integer checkActivityStart(Date activityStartTime, Date activityEndTime) {
        long activityStartStamp = activityStartTime.getTime() / ActivityServiceConstants.MILLISECONDS_PER_SECOND;
        long activityEndStamp = activityEndTime.getTime() / ActivityServiceConstants.MILLISECONDS_PER_SECOND;
        long nowStamp = System.currentTimeMillis() / ActivityServiceConstants.MILLISECONDS_PER_SECOND;

        return (nowStamp > activityStartStamp && nowStamp < activityEndStamp) ? 
            ActivityServiceConstants.DEFAULT_ACTIVITY_STARTED : 
            ActivityServiceConstants.DEFAULT_ACTIVITY_NOT_STARTED;
    }

    /**
     * Validate required parameters
     * 
     * @param paramName parameter name
     * @param paramValue parameter value
     * @throws IllegalArgumentException if parameter is null or empty
     */
    public static void validateRequiredParameter(String paramName, Object paramValue) {
        if (paramValue == null) {
            throw new IllegalArgumentException(paramName + " cannot be null");
        }
        if (paramValue instanceof String && StringUtils.isEmpty((String) paramValue)) {
            throw new IllegalArgumentException(paramName + " cannot be empty");
        }
        if (paramValue instanceof List && ((List<?>) paramValue).isEmpty()) {
            throw new IllegalArgumentException(paramName + " cannot be empty");
        }
    }

    /**
     * Validate user class
     * 
     * @param userClass user class
     * @throws IllegalArgumentException if user class is invalid
     */
    public static void validateUserClass(String userClass) {
        if (!ActivityServiceConstants.USER_CLASS_MERCHANT.equals(userClass) &&
            !ActivityServiceConstants.USER_CLASS_MASTER.equals(userClass) &&
            !ActivityServiceConstants.USER_CLASS_CUSTOMER.equals(userClass)) {
            throw new IllegalArgumentException("Invalid user class: " + userClass);
        }
    }

    /**
     * Validate activity ID
     * 
     * @param activityId activity ID
     * @throws IllegalArgumentException if activity ID is invalid
     */
    public static void validateActivityId(Long activityId) {
        if (activityId == null || activityId <= 0) {
            throw new IllegalArgumentException("Activity ID must be positive");
        }
    }

    /**
     * Validate user ID
     * 
     * @param userId user ID
     * @throws IllegalArgumentException if user ID is invalid
     */
    public static void validateUserId(Long userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("User ID must be positive");
        }
    }

    /**
     * Check if platform is app or H5
     * 
     * @param tokenApp app token
     * @param isH5 H5 flag
     * @return true if app or H5
     */
    public static boolean isAppOrH5(String tokenApp, Integer isH5) {
        return !StringUtils.isEmpty(tokenApp) || (isH5 != null && isH5 == 1);
    }

    /**
     * Check if reward is completed based on button status
     * 
     * @param buttonStatus button status
     * @return true if reward is completed
     */
    public static boolean isRewardCompleted(Integer buttonStatus) {
        return buttonStatus.equals(ActivityServiceConstants.BUTTON_STATUS_COLLECTED) ||
               buttonStatus.equals(ActivityServiceConstants.BUTTON_STATUS_COLLECTED_COMPLETE) ||
               buttonStatus.equals(4); // ActivityButtonStateEnum.CollectedCompleteALl.code
    }
}
