package com.wanshifu.sdk;

import com.wanshifu.iop.inner.api.domains.request.bigData.CustomerSingleGroupIdReq;
import com.wanshifu.iop.inner.api.domains.request.bigData.MasterBatchGroupIdReq;
import com.wanshifu.iop.inner.api.domains.request.bigData.MasterSingleGroupIdReq;
import com.wanshifu.iop.inner.api.domains.request.bigData.UserSingleGroupIdReq;
import com.wanshifu.iop.inner.api.domains.response.bigdata.CustomerGroupIdResp;
import com.wanshifu.iop.inner.api.domains.response.bigdata.MasterGroupIdResp;
import com.wanshifu.iop.inner.api.domains.response.bigdata.UserGroupIdResp;
import com.wanshifu.sdk.config.BigDataGroupApiDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import feign.Request;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(
        value = "bigdata-open-service",
        configuration = {BigDataGroupApiDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.bigData.goupId.url}"
//        url = "https://test-bigdata-open-service.wanshifu.com"
)
public interface BigDataGroupIdApi {

    /**
     * 师傅人群请求接口 - 批量
     */
    @PostMapping(value = "/dataApi/getList/getPersonaGroupIdsByMasterIds")
    List<MasterGroupIdResp> masterBatchGroupId(@RequestBody MasterBatchGroupIdReq req);

    /**
     * 师傅人群请求接口 - 批量
     */
    @PostMapping(value = "/dataApi/getList/getPersonaGroupIdsByCMasterIds")
    List<MasterGroupIdResp> tocMasterBatchGroupId(@RequestBody MasterBatchGroupIdReq req);


    /**
     * 师傅人群请求接口 - 单个
     */
    @PostMapping(value = "/dataApi/getOne/getPersonaGroupIdsByMasterId")
    MasterGroupIdResp masterSingleGroupId( @RequestBody MasterSingleGroupIdReq req);

    /**
     * 师傅人群请求接口 - 单个
     */
    @PostMapping(value = "/dataApi/getOne/getPersonaGroupIdsByCMasterId")
    MasterGroupIdResp tocMasterSingleGroupId( @RequestBody MasterSingleGroupIdReq req);


    /**
     * 商家人群请求接口 - 单个
     */
    @PostMapping(value = "/dataApi/getOne/getPersonaGroupIdsByUserId")
    UserGroupIdResp userSingleGroupId(@RequestBody UserSingleGroupIdReq req);


    /**
     * 家庭人群请求接口 - 单个
     */
    @PostMapping(value = "/dataApi/getOne/getPersonaGroupIdsByFamUserId")
    CustomerGroupIdResp customerSingleGroupId(@RequestBody CustomerSingleGroupIdReq req);

}
