package com.wanshifu.constant;

/**
 * Constants for ActivityService to replace magic strings and numbers
 * Improves code maintainability and reduces errors
 * 
 * <AUTHOR>
 */
public final class ActivityServiceConstants {

    private ActivityServiceConstants() {
        // Utility class
    }

    // Activity States
    public static final String ACTIVITY_STATE_CANCELED = "canceled";
    public static final String ACTIVITY_STATE_EXPIRY = "expiry";
    public static final String ACTIVITY_STATE_LAUNCHING = "launching";

    // Task Symbols
    public static final String TASK_SYMBOL_VISIT = "visit";
    public static final String TASK_SYMBOL_CREATE_ORDER = "create_order";
    public static final String TASK_SYMBOL_APPOINT_MASTER = "appoint_master";
    public static final String TASK_SYMBOL_ORDER_PAY = "order_pay";
    public static final String TASK_SYMBOL_ORDER_CHECK = "order_check";
    public static final String TASK_SYMBOL_MANUAL_RATE = "manual_rate";
    public static final String TASK_SYMBOL_CUSTOMER_VISIT = "customer_visit";

    // User Classes
    public static final String USER_CLASS_MERCHANT = "merchant";
    public static final String USER_CLASS_MASTER = "master";
    public static final String USER_CLASS_CUSTOMER = "customer";

    // Persona IDs
    public static final String PERSONA_ID_MERCHANT = "1";
    public static final String PERSONA_ID_MASTER_B = "2";
    public static final String PERSONA_ID_CUSTOMER = "3";
    public static final String PERSONA_ID_MASTER_C = "4";

    // Execute Types
    public static final String EXECUTE_TYPE_SINGLE = "single";
    public static final String EXECUTE_TYPE_CYCLE = "cycle";
    public static final String EXECUTE_TYPE_DAY = "day";
    public static final String EXECUTE_TYPE_WEEK = "week";
    public static final String EXECUTE_TYPE_MONTH = "month";

    // Platform Types
    public static final String PLATFORM_WEB = "web";
    public static final String PLATFORM_APP = "app";

    // Error Codes
    public static final String ERROR_GET_ACTIVITY_DETAIL_FAIL = "get_activity_detail_fail";
    public static final String ERROR_GET_LIST_FAIL = "getlist_fail";
    public static final String ERROR_BACK_FAIL = "back_fail";
    public static final String ERROR_SIGN_FAIL = "sign_fail";
    public static final String ERROR_ISSUE_FAIL = "issue_fail";
    public static final String ERROR_LANDING_FAIL = "landing_fail";
    public static final String ERROR_GET_POP_FAIL = "get_pop_fail";
    public static final String ERROR_DATA_PARSE_ERROR = "data_parse_data_error";

    // Error Messages
    public static final String MSG_ACTIVITY_CANCELED = "活动已取消";
    public static final String MSG_GET_ACTIVITY_LIST_FAIL = "获取活动列表失败";
    public static final String MSG_AD_CALLBACK_FAIL = "广告回传失败";
    public static final String MSG_LANDING_PAGE_RECORD_FAIL = "用户浏览落地页详情记录失败";
    public static final String MSG_GET_POPUP_AD_FAIL = "拉取弹窗广告失败";
    public static final String MSG_QUERY_ACTIVITY_INFO_ERROR = "查询活动信息异常";
    public static final String MSG_REQUEST_TIMEOUT = "请求超时";

    // Button Status Values
    public static final int BUTTON_STATUS_NOT_STARTED = 0;
    public static final int BUTTON_STATUS_IN_PROGRESS = 1;
    public static final int BUTTON_STATUS_COMPLETED = 2;
    public static final int BUTTON_STATUS_COLLECTED = 3;
    public static final int BUTTON_STATUS_COLLECTED_COMPLETE = 4;

    // Time Constants
    public static final long SECONDS_PER_MINUTE = 60L;
    public static final long MILLISECONDS_PER_SECOND = 1000L;

    // Default Values
    public static final int DEFAULT_CACHE_TIME = 10;
    public static final int DEFAULT_AWARDING_COUNT = 0;
    public static final int DEFAULT_ACTIVITY_NOT_STARTED = 0;
    public static final int DEFAULT_ACTIVITY_STARTED = 1;
    public static final int DEFAULT_REWARD_NOT_COMPLETED = 0;
    public static final int DEFAULT_REWARD_COMPLETED = 1;

    // Voucher Types
    public static final String VOUCHER_TYPE_MONEY_OFF = "money_off";
    public static final String VOUCHER_TYPE_DISCOUNT = "discount";

    // Order Types
    public static final String ORDER_TYPE_MASTER = "master";
    public static final String ORDER_TYPE_ENTERPRISE = "enterprise";
    public static final String ORDER_TYPE_USER = "user";

    // Order Sources
    public static final String ORDER_SOURCE_SITE = "site";
    public static final String ORDER_SOURCE_ENTERPRISE_SYSTEM = "enterprise_system";
    public static final String ORDER_SOURCE_WEIXIN = "weixin";
    public static final String ORDER_SOURCE_IKEA = "ikea";
    public static final String ORDER_SOURCE_THIRDPART = "thirdpart";
    public static final String ORDER_SOURCE_APPLET = "applet";
    public static final String ORDER_SOURCE_IOS_ANDROID = "ios,android";

    // Appoint Methods
    public static final String APPOINT_METHOD_NORMAL = "normal";
    public static final String APPOINT_METHOD_OPEN = "open";
    public static final String APPOINT_METHOD_DEFINITE_PRICE = "definite_price";
    public static final String APPOINT_METHOD_ADVANCE_PAYMENT = "advance_payment";

    // Chinese Translations
    public static final String CHINESE_MASTER_ORDER = "师傅订单";
    public static final String CHINESE_ENTERPRISE_ORDER = "总包订单";
    public static final String CHINESE_NO_LIMIT = "不限";
    public static final String CHINESE_MERCHANT_WEB_BACKEND = "商家web后台";
    public static final String CHINESE_APP = "APP";
    public static final String CHINESE_THIRD_PARTY = "第三方";
    public static final String CHINESE_PRICE_BIDDING = "报价招标";
    public static final String CHINESE_DIRECT_ASSIGN = "直接指派";
    public static final String CHINESE_FIXED_PRICE = "一口价";
    public static final String CHINESE_SINGLE_PRODUCT = "单个商品";
    public static final String CHINESE_PACKAGE_PRODUCT = "套装商品（商品数量大于等于2）";

    // Time Format Patterns
    public static final String TIME_FORMAT_HH_MM = "HH:mm";
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

    // Week Days
    public static final String MONDAY = "周一";
    public static final String TUESDAY = "周二";
    public static final String WEDNESDAY = "周三";
    public static final String THURSDAY = "周四";
    public static final String FRIDAY = "周五";
    public static final String SATURDAY = "周六";
    public static final String SUNDAY = "周日";
}
