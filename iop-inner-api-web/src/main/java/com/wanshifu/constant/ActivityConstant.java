package com.wanshifu.constant;

import com.google.common.collect.Lists;
import com.wanshifu.iop.activity.domain.enums.ActivityStateEnum;
import com.wanshifu.iop.inner.api.domains.enums.TaskSymbolEnum;

import com.wanshifu.master.innerapi.domains.enums.account.AccountTypeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author:<PERSON><PERSON><PERSON>@wanshifu.com
 * @create:2022-05-24 15:14:00
 * @Description ：活动公共变量
 **/
public class ActivityConstant {
  /***
   * 活动任务类型--
   */
  public static List<String> ActivitymbolEnumListStr = Lists.newArrayList(
          TaskSymbolEnum.CREATE_ORDER.taskSymbol,
          TaskSymbolEnum.APPOINT_MASTER.taskSymbol,
          TaskSymbolEnum.ORDER_PAY.taskSymbol,
          TaskSymbolEnum.ORDER_CHECK.taskSymbol,
          TaskSymbolEnum.MANUAL_RATE.taskSymbol
  );
  /**
   * 充值类型：在线充值:online  线下转账:offline
   */
  public static final String RECHARGE_ONLINE = "online";
  public static final String RECHARGE_OFFLINE = "offline";

  public static final String RESERVATION_INFO_3_LIST_TXT = "（限%s公里内签到有奖）";
  public static final String RESERVATION_INFO_4_LIST_TXT = "，按用户期望上门时间预约额外奖<>%s";
  public static final String RESERVATION_INFO_5_LIST_TXT = "，按用户期望上门时间预约额外奖励<>现金红包";

  /**

   * 现金券类型：money_off:满减-默认 、discount: 折扣
   */
  public static final String VOUCHER_MONEY_OFF = "money_off";
  public static final String VOUCHER_DISCOUNT = "discount";

  /*
   *领奖校验标志:0-不需要校验，1-待校验，2-校验通过 3-校验不通过
   */
  public static final int REWARD_CHECK_ZERO = 0;
  public static final int REWARD_CHECK_ONE = 1;
  public static final int REWARD_CHECK_TWO = 2;
  public static final int REWARD_CHECK_THREE = 3;
  /**
   * 师傅订单详情奖励提醒文案
   */
  public static final String showTitle1="已获得%s奖励资格，%s后奖励<>%s</>";
  public static final String showTitle2="已获得%s奖励资格，%s后<>佣金全返</>";
  public static final String showTitle3="已获得%s奖励资格，%s后奖励<>现金红包</>";
  public static final String showTitle4="已获得%s奖励资格，%s后奖励<>%s积分</>";
  public static final String showTitle5="指派后%s分钟内预约，";
  public static final String showTitle6="预约时间范围内签到，";
  public static final String showTitle7="服务获得用户5星带图好评，%s后奖励<>%s%s</>，好评不少于10字；（家庭订单好评，非商家店铺好评）";
  public static final String showTitle8="服务获得用户5星带图好评，%s后奖励<>%s</>，好评不少于10字；（家庭订单好评，非商家店铺好评）";
  public static final String showTitle9_desc="服务获得用户5星带图好评，好评不少于10字，好评后发放。（家庭订单好评，非商家店铺好评）";
  public static final String showTitle10="服务获得<>%s</>（家庭订单好评，非商家店铺好评）";

  /**
   * 任务类型
   */
  public static final String quoted = "quoted";
  public static final String appointed = "appointed";
  public static final String serve_complete = "serve_complete";
  /**
   * 任务类型-默认文案z
   */
  public static final String quoted_default = "报价指定次数";
  public static final String appointed_default = "被指派指定次数";
  public static final String appointed_default_1 = "指派指定次数";
  public static final String serve_complete_default = "完工指定次数";
  public static final String text_task_default = "指定次数";

  /**
   * 零时零分
   */
  public static final String day_start = "00:00:00";
  /**
   * 结束时间
   */
  public static final String day_end = "23:59:59";

  /**
   * 执行频率提醒文案--single
   * */
  public static final String single_text1="活动期间最多可领取1次";

  /**
   * 执行频率提醒文案--cycle
   * */
  public static final String cycle_text1="活动期间最多可领取%s次";
  /**
   * 执行频率提醒文案--每天
   * */
  public static final String day_text1="每天完成任务可领取，每天封顶%s次";
  public static final String day_text2="每天%s到%s内完成任务可领取，每天封顶%s次";

  /**
   * 来源(客户端)[ikea:宜家]
   * */
  public static final String IKEA="ikea";
  public static final String IKEA_NAME="宜家订单";

  /**
   * 来源(客户端)[ikea:宜家]
   * 宜家：ikea 宜家（微信小程序）
   * */
  public static final String IKEA_IOP="ikea";
  /**
   * 来源(客户端)[商家订单：merchant 商家订单（含企业网站、企业iOS、企业安卓）]
   * */
  public static final String MERCHANT="merchant";
  public static final String MERCHANT_NAME="商家订单";
  public static final String MERCHANT_NAME_INVITE="商家订单（含企业网站、企业iOS、企业安卓、第三方）";

  /**
   * 来源(客户端)[商家订单：merchant 商家订单（含企业网站、企业iOS、企业安卓）]
   * */
  public static final String MERCHANT_IOP="site,ios,android,thirdpart";
  /**
   * 来源(客户端)数组[商家订单：merchant 商家订单（含企业网站、企业iOS、企业安卓）]
   * */
  public static final List<String> MERCHANT_IOP_ARRAY= Arrays.asList("site","ios","android","thirdpart");
  /**
   * 来源(客户端)[家庭订单：family 含家庭小程序、家庭iOS、家庭安卓、家庭H5）]
   * 商家订单：site 网站；ios IOS; android Android;
   * */
  public static final String FAMILY="family";
  public static final String FAMILY_NAME="家庭订单";
  public static final String FAMILY_NAME_INVITE="家庭订单（含家庭小程序、家庭iOS、家庭安卓、家庭H5）";

  /**
   * 来源(客户端)[家庭订单：family 含家庭小程序、家庭iOS、家庭安卓、家庭H5）]
   * applet:小程序; toc_h5:家庭H5页面; toc_ios：家庭IOS；toc_android：家庭安卓)
   * */
  public static final String FAMILY_IOP="applet,toc_h5,toc_ios,toc_android";
  /**
   * 是，展示，真
   * 正
   * */
  public static final int YES_TRUE = 1;
  /**
   * 否，隐藏，假
   * 负
   * */
  public static final int NO_FALSE = 0;

  /**
   * 图片缓存值格式
   */
  public static final String PICTURE_URL_CACHE_VALUE="{\"aid\":%s,\"url\":\"%s\"}";

  /**
   * isweb
   * 正
   * */
  public static final Integer IS_WEB=2;
  /**
   * isweb
   * 正
   * */
  public static final Integer IS_APP=1;

  /**
   * 手机号正则校验规则
   * */
  public static final String phone_match ="^[1][3,4,5,6,7,8,9][0-9]{9}$";
  /**
   * 隐私政策
   * 正
   * */
  public static final String IS_PRIVACY_POLICY="getPrivacyPolicy";

  /**
   * 注册  类型
   * 正
   * */
  public static final String SYSTEM_TYPE="register";

  /**
   * 大数据默认传值
   * 正
   * */
  public static final Long BIG_DATA_DEFAULT=-1L;
  /**
   * 邀请码长度控制
   * 正
   * */
  public static final int INVITE_CODE_LENGTH=5;
  /**
   * 邀请码   类型分隔符
   * 正
   * */
  public static final String INVITE_CODE_STR="_invite_";
  /**
   * 落地页 唯一码 前缀
   * 正
   * */
  public static final String LANDING_PAGE_PREFIX="p_";

  /**
   * 复姓集合
   */
  public static final String[] bai = { "欧阳", "太史", "上官", "端木", "司马", "东方", "独孤", "南宫", "万俟", "闻人", "夏侯", "诸葛", "尉迟", "公羊", "赫连", "澹台",
          "皇甫", "宗政", "濮阳", "公冶", "太叔", "申屠", "公孙", "慕容", "仲孙", "钟离", "长孙", "宇文", "司徒", "鲜于", "司空", "闾丘", "子车",
          "亓官", "司寇", "巫马", "公西", "颛孙", "壤驷", "公良", "漆雕", "乐正", "宰父", "谷梁", "拓跋", "夹谷", "轩辕", "令狐", "段干", "百里",
          "呼延", "东郭", "南门", "羊舌", "微生", "公户", "公玉", "公仪", "梁丘", "公仲", "公上", "公门", "公山", "公坚", "左丘", "公伯", "西门",
          "公祖", "第五", "公乘", "贯丘", "公皙", "南荣", "东里", "东宫", "仲长", "子书", "子桑", "即墨", "达奚", "褚师", "吴铭" };
  /**
   * crm-使用，营销推广
   */
  public static final String CRM_IOC = "IOC";

  /**
   * user
   */
  public static final String USER_CLASS = "user";

  /**
   * master
   */
  public static final String MASTER_CLASS = "master";

  public static final String REPLACE_STR = "{$}";
  /**
   * 落地页组建数据，默认活动id匹配 格式
   */
  public static final String landingPage_Json_regex="\"relationActivityId\":(.*?),";//别忘了使用非贪婪模式！

  /**
   * 其他过滤条件，--------------------------------------------------------------------
   */
  public static final String IS_ORDER_PACKAGE = "isOrderPackage";
  public static final String IS_ORDER_PACKAGE_NAME = "订单包内的订单";
  public static final String NOT_IS_ORDER_PACKAGE = "not_isOrderPackage";
  /**
   * 是否为合约师傅订单
   */
  public static final String IS_ORDER_CONTRACT = "isOrderContract";
  public static final String IS_ORDER_CONTRACT_NAME = "合约师傅订单";
  /**
   * 是否是师傅店铺订单
   * */
  public static final String IS_ORDER_SHOP = "isOrderShop";
  public static final String IS_ORDER_SHOP_NAME = "师傅店铺订单";
  public static final String NOT_IS_ORDER_SHOP = "not_isOrderShop";
  /**
   * 是否是协议师傅(专属)订单
   * */
  public static final String IS_ORDER_AGREEMENT_EXCLUSIVE = "isOrderAgreementExclusive";
  public static final String IS_ORDER_AGREEMENT_EXCLUSIVE_NAME = "协议师傅(专属)订单";
  /**
   * 是否是协议师傅(合约)订单
   * */
  public static final String IS_ORDER_AGREEMENT_CONTRACT = "isOrderAgreementContract";
  public static final String IS_ORDER_AGREEMENT_CONTRACT_NAME = "协议师傅(合约)订单";
  /**
   * 是否是协议师傅(品牌)订单
   * */
  public static final String IS_ORDER_AGREEMENT_BRAND = "isOrderAgreementBrand";
  public static final String IS_ORDER_AGREEMENT_BRAND_NAME = "协议师傅(品牌)订单";
  /**
   * 是否是样板城市(样板城市)订单
   * */
  public static final String IS_ORDER_NEW_MODEL = "isOrderNewModel";
  public static final String IS_ORDER_NEW_MODEL_NAME = "样板城市(样板城市)订单";
  /**
   * 招募师傅(招募)订单
   * */
  public static final String IS_ORDER_RECRUIT = "isOrderRecruit";
  public static final String IS_ORDER_RECRUIT_NAME = "招募师傅(招募)订单";

  /**
   * 推广页面表单提交用户侧，注册类型：8
   * */
  public static final Integer regType = 8;

  /**
   * success
   */
  public static final String SUCCESS = "success";
  /**
   * activity_limit_fail
   */
  public static final String ACTIVITY_LIMIT_FAIL = "activity_limit_fail";
  /**
   * activity_goods_all_limit_fail
   */
  public static final String ACTIVITY_GOODS_ALL_LIMIT_FAIL = "activity_goods_all_limit_fail";

  /**
   * 大数据人群接口
   * */
  public static final String APPID = "1";

  /**
   * aid
   * */
  public static final String AID = "aid";
  /**
   * 用户类型
   */
  public static final String ACCOUNT_TYPE_USER = AccountTypeEnum.USER.code;
  public static final int CONFIGSERVERCATEGORY_STATE = 1;

  /**
   * 不限
   */
  public static final String NOT_LIMIT_CN = "不限";

  /**
   * 家庭被邀请人落地页小程序固定路径
   * */
  public static final String PATH = "pages/ioc/orderCashback";

  /**
   * 家庭被邀请人落地页H5跳转到小程序中转页地址
   * */
  public static final String PATH_H5 = "https://jiating-oss.wanshifu.com/wxapp/h5/********/index.html";

  /**
   * 小程序参数
   * */
  public static final String PATH_CODE = "shareCode=%s&activityId=%s";

  /**
   * 小程序参数 没有活动id的情况
   * */
  public static final String PATH_CODE_NOT_ACTIVITYID = "shareCode=%s";

  /**
   * 家庭小程序默认头像
   * */
  public static final String DEFAULT_HEAD_IMG = "https://qncdn.wanshifu.com/WeChatMiniProgram/icon_164.png";


  /**
   * 家庭小程序码
   * */
  public static final String CUSTOMER_APPLET = "customer_applet";

  /**
   * 家庭小程序
   * */
  public static final String CUSTOMER_LINK = "customer_link";

  /**
   * 订单异常情况
   * */
  public static final String ORDER_CLOSE = "订单已关闭";

  /**
   * 订单完成，小于50元
   * */
  public static final String ORDER_COMPLETE_LESS = "订单实付金额低于%s元";

  /**
   *任务是否已经完成 completed://已完成，incomplete:未完成
   * */
  public static final String COMPLETED = "completed";
  public static final String INCOMPLETE = "incomplete";

  /**
   *奖励领取状态，received：已发放，unclaimed:未发放
   * */
  public static final String RECEIVED = "received";
  public static final String UNCLAIMED = "unclaimed";
  public static final String DISTANCE_REWARD_TXT = "（限%s公里内%s有奖）";

  public static final String RESERVE_CUSTOMER_EXT_INFO = "指派后%s分钟内预约，";
  public static final String RESERVE_CUSTOMER_EXT_INFO_1 = "指派后30分钟内预约，";
  public static final String SERVE_SIGN_EXT_INFO = "预约时间范围内签到，";
  public static final String QUOTED_EXT_INFO = "限时报价，";


  public static final String MASTER_COMMON_CONTRACT_NAME = "%s师傅获得%s%s";
  /**
   *是否拆封，WRAPPED--未拆封  OPENED--已拆封
   */
  public static final String BONUS_WRAPPED = "WRAPPED";
  public static final String BONUS_OPENED = "OPENED";
  /**
   * 传入的固定值 extraType
   */
  public static final String REWARD_GIVE_ID = "reward_give_id";
  /**
   * 还差xx文案:例如下单还差1单可领奖
   */
  public static final String NEED_REWARD_TEXT_TEMPLATE = "%s还差%s可领奖";
  /**
   * 完成xx文案:
   */
  public static final String FINISH_REWARD_TEXT_TEMPLATE = "恭喜你完成了%s%s目标";

  /**
   * 任务待完成礼盒图片
   */
  public static final String TASK_NO_FINISH_GIFT_IMG_URL = "https://qncdn.wanshifu.com/202310/7c5fd8a5125e6eeee1047c986937beab";
  /**
   * 任务已完成礼盒图片
   */
  public static final String TASK_FINISHED_GIFT_IMG_URL = "https://qncdn.wanshifu.com/202310/4fe66ebeb42e8df513e3db6fa6163e83";

  /**
   * 奖励全部领完
   */
  public static final String ALL_COMPLETE_STATE_TEXT = "奖励已全部领完";
  public static final String ALL_COMPLETE_STATE_TEXT_1 = "充值福利已全部领完";

  /**
   * 传入的固定值 extraType
   */
  public static final String commissionAmountRate = "commissionAmountRate";


  /**
   * 领券错误提示 活动已终止，已取消
   */
  public static final String FREE_VOUCHER_GET_ERROR_TIP_ACTIVITY_END = "抱歉，活动已取消，请关注下一次活动~";
  /**
   * 领券错误提示 优惠已抢光
   */
  public static final String FREE_VOUCHER_GET_ERROR_TIP_SOLD_OUT_1 = "手慢啦，已抢光~";
  /**
   * 领券错误提示 无活动参与资格
   */
  public static final String FREE_VOUCHER_GET_ERROR_TIP_HAS_NO_QUALIFICATION = "无活动参与资格";

  /**
   * 领券状态 领取失败
   */
  public static final Integer FREE_VOUCHER_GET_STATUS_FAIL = 0;

  /**
   * 领券失败 需重新拉取
   */
  public static final Integer VOUCHER_GET_STATUS_NEED_REPULL =2;

  /**
   * 领券状态 无资格
   */
  public static final Integer FREE_VOUCHER_GET_STATUS_HAS_NO_QUALIFICATION = 4;

  /**
   * 领券状态 活动结束
   */
  public static final Integer FREE_VOUCHER_GET_STATUS_ACTIVITY_END = 5;

  /**
   * 登录状态提示
   */
  public static final String FREE_VOUCHER_GET_STATUS_HAS_NO_LOGIN = "未登录,请先登录";
  /**
   * 领券异常
   */
  public static final String FREE_VOUCHER_GET_STATUS_EXCEPTION = "领券异常,请联系系统管理员";

  /**
   * 用户类型 家庭用户
   */
  public static final String USER_CLASS_CUSTOMER = "customer";
  /**
   * 用户类型 家庭用户
   */
  public static final String ACTIVITY_REWARD_TIME_TEXT = "领奖时间:%s-%s";
  public static final String ACTIVITY_REWARD_TIME_TEXT_END = "领奖时间截至：%s";
}