package com.wanshifu.constant;

/**
 * @author: wang<PERSON><PERSON>@wshifu.com
 * @date: 2022/4/12 22:50
 * @description:
 */
public class CacheKeyConstant {
    /**
     * 用户人群缓存，避免多次请求大数据接口
     */
    public static String USER_GROUP_KEY = "iopInnerApi:userGroupId:userId:%s";

    /**
     * 用户人群缓存，避免多次请求大数据接口
     */
    public static String USER_GROUP_KEY_V2 = "iopInnerApi:userGroupIdV2:userId:%s";

    /**
     * 师傅所属事业群
     * */
    public static String MASTER_BELONG_BUSINESS_GROUP = "iopInnerApi:masterBusinessGroup:masterId:%s";

    /**
     * 登陆信息redis缓存
     * */
    public static String USER_LOGIN_INFO_KEY = "user-web-api:wsf_user_token:%s";

    /**
     * 师傅登录信息缓存
     * */
    public static String MASTER_LOGIN_INFO_KEY = "masterInformationService:signature:%s";
    /**
     * 用户拉取非弹窗广告缓存，避免频繁请求接口
     */
    public static String USER_PULL_AD_POSITION_KEY = "iopInnerApi:userClass:%s:port:%s:pullAdPosition:%s:userId:%s:V3";
    /**
     * 师傅批量拉取非弹窗广告缓存，避免频繁请求接口
     */
    public static String BATCH_PULL_AD_POSITION_KEY = "iopInnerApi:userClass:%s:port:%s:pullAdPosition:%s:userId:%s:V1";
    /**
     * 付费报名
     * */
    public static String APPLY_CONDIT_KEY = "iopInnerApi:apply:activityId:%s:masterId:%s";

    /**
     * redis图片缓存key
     */
    public static String PICTURE_URL_KEY = "iopInnerApi:pictureUrl:aid:%s";
    /**
     * 现金券缓存key
     */
    public static String VOUCHER_REWARD_KEY = "iopInnerApi:voucherReward:extraId:%s";
    /**
     * 现金券缓存key---v2
     */
    public static String VOUCHER_REWARD_KEY_V2 = "iopInnerApi:voucherReward:extraIdV2:%s";
    /**
     * 券包缓存key
     */
    public static String VOUCHER_PACK_KEY = "iopInnerApi:voucherPackReward:extraId:%s";

    /**
     * 现金券/券包缓存key
     */
    public static String VOUCHER_OR_PACK_REWARD_KEY = "iopInnerApi:voucherOrPackReward:extraId:%s:rewardSymbol:%s";


    /**
     * 手机号验证码 缓存 限时 一分钟
     */
    public static final String PHONE_SENCMESSAGE_KEY = "iopActivityService:sendMessageCode:phone:%s";
    /**
     * 手机号验证码 缓存 限时 一分钟---商家落地页注册
     */
    public static final String PHONE_SENCMESSAGE_LANDING_PAGE_KEY = "iopActivityService:sendMessageCode:landingPage:phone:%s";
    /**
     * 邀请人集合页-我的奖励，已领取
     */
    public static final String INVITER_MYREWARD_HAS_KEY = "iopActivityService:inviter:hasReward:%s";
    /**
     * 邀请人集合页-我的奖励，待解锁
     */
    public static final String INVITER_MYREWARD_WAIT_KEY = "iopActivityService:inviter:waitReward:%s";
    /**
     * 现金券缓存
     */
    public static final String ACTIVITY_REWARD_VOUCHER = "iopActivityService:reward:voucher:%s";
    /**
     * 现金券缓存时间（秒）
     */
    public static final Integer ACTIVITY_REWARD_VOUCHER_TIME = 600;
    /**
     * 券包缓存
     */
    public static final String ACTIVITY_REWARD_VOUCHER_PACK = "iopActivityService:reward:voucherPack:%s";
    /**
     * 现金券包缓存时间（秒）
     */
    public static final Integer ACTIVITY_REWARD_VOUCHER_PACK_TIME = 600;
    /**
     * 活动内服务缓存
     */
    public static final String ACTIVITY_PAGE_SERVE = "iopInnerApi:ActivityId:%s";
    /**
     * 活动内服务缓存  落地页使用
     */
    public static final String ACTIVITY_PAGE_SERVE_1 = "iopInnerApi:ActivityId:%s:ActivityTaskId:%s:businessLineId:%s";
    /**
     * 活动服务缓存时间
     */
    public static final Integer ACTIVITY_PAGE_SERVE_TIME = 1200;
    /**
     * 缓存时间（秒）一分钟
     */
    public static final Integer ONE_MINUTE_SECOND = 60;
    /**
     * 缓存时间（秒）一分钟
     */
    public static final Integer ONE_HOUR_SECOND = 60 * 60;


    /**
     * 隐私政策对象缓存，避免多次查询--师傅邀请活动
     */
    public static String PRIVACY_POLICY_MASTER_INVITE = "iopInnerApi:master_invite_task_new:getPrivacyPolicy:%s";
    /**
     * 隐私政策对象缓存，避免多次查询--师傅邀请活动
     */
    public static String INVITER_COLLECT_PAGE_MYREWARDINFO = "iopInnerApi:InviterCollectPageMyRewardInfo:myRewardInfo:userId:%s：activityId:%s";
    /**
     * 1天时间戳
     */
    public static int ONE_DAY_SECOND = 60 * 60 * 24;

    /**
     * 现金券缓存 时间
     */
    public static final Integer VOUCHER_TIME = 3600 * 2;
    /**
     * 现金券缓存 时间
     */
    public static final String VOUCHER_CACHE_KEY = "iopInnerApi:voucher:%s";



    /**
     * 单位（毫秒）
     * */
    public static final Integer SECOND_UNIT = 1000;

    /**
     * abTest实验组缓存
     * */
    public static final String ABTEST_CACHE_KEY = "iopInnerApi:abtest:%s:%s";
    /**
     * 数据库地址名称信息缓存:4级+5级
     */
    public static final String FOUR_AND_FIVE_ADDRESS_CACHE_KEY = "iopInnerApi:addressName:fourAndFive";


    /**
     * 家庭用户登陆信息
     * */
    public static final String CUSTOMER_USER_LOGIN_KEY = "sysm:token:%s";

    /**
     * 家庭订单返现，生成邀请码缓存
     * */
    public static final String CUSTOMER_USER_SHARECODE_KEY = "customer:shareCode:activityId:%s:userId:%s";

    /**
     * 家庭订单返现，生成邀请码缓存
     * */
    public static final Integer CUSTOMER_USER_SHARECODE_TIME = 1800;


    /**
     * 家庭订单返现，获取待解锁缓存
     * */
    public static final String CUSTOMER_USER_WAIT_REWARD_KEY = "customer:waitReward:activityId:%s:userId:%s";

    /**
     * 家庭订单返现，获取待解锁缓存 时间
     * */
    public static final Integer CUSTOMER_USER_WAIT_REWARD_TIME = 86400;

    /**
     * 活动下的抽奖转盘类型-弹幕
     * */
    public static String ACTIVITY_PRIZEWHEELID_KEY = "iopInnerApi:LotteryTurntableBarrage:activityId:%s:prizeWheelId:%s";
    /**
     * 师傅开屏广告位 --缓存优先级
     * */
    public static final String PRIORITY_FOR_PULLING_ADS_KEY = "iopInnerApi:priorityForPullingAds:userClass:%s:launchPort:%s:priorityAdPositionSymbol:%s";

}
