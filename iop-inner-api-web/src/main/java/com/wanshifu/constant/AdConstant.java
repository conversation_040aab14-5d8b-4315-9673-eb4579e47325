package com.wanshifu.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 广告相关静态类
 *
 * <AUTHOR>
 * @date Created in 2023/10/7 19:49
 */

public class AdConstant {
    /**
     * 商家广告需要校验奖励上限广告位
     */
    public static final List<String> MERCHANT_CHECK_UPPER_LIMIT_POSITION_SYMBOLS = Arrays.asList("wallet_icon_text", "homepage_recharge_button_text", "paypage_text", "per_centre_recharge_button_text");
    /**
     * 商家广告需要校验奖励服务保障金广告位
     */
    public static final List<String> MERCHANT_CHECK_SECURITY_FUND_POSITION_SYMBOLS = Arrays.asList("paypage_text");

    /**
     * 达到上限无需取消保护期广告位
     * 订单列表顶部大banner：order_top_banner
     * 发布订单成功页广告位(多状态)：pub_order_success_page_v2
     */
    public static final List<String> NOT_CHECK_AD_HELP_UPPER_LIMIT = Arrays.asList("order_top_banner","pub_order_success_page_v2");
    /**
     * 弹窗广告需要校验奖励上限广告位
     */
    public static final List<String> CHECK_POP_UPPER_LIMIT_POSITION_SYMBOLS = Arrays.asList("popup_web", "popup_app");
    /**
     * 需要特殊处理家庭首页中部轮播广告位的app版本
     */
    public static final List<String> SPECIAL_SIZE_APP_VERSION_LIST = Arrays.asList("1.25.4", "1.25.5");
    /**
     * 家庭首页中部轮播广告位
     */
    public static final String HOMEPAGE_MIDDLE_CIRCLE_APP = "homepage_middle_circle_app";
    /**
     * 家庭首页中部轮播广告位(大尺寸)
     */
    public static final String HOMEPAGE_MIDDLE_CIRCLE2_APP = "homepage_middle_circle2_app";
    /**
     * 师傅报价列表顶部多状态广告位
     */
    public static final String QUOTED_TOP_BANNER = "quoted_top_banner";
    /**
     * 家庭-首页领券广告位
     */
    public static final List<String> CUSTOMER_RECEIVE_VOUCHER_AD_SYMBOL_LIST = Arrays.asList("homepage_receive_voucher_app", "homepage_receive_voucher_applet","homepage_receive_voucher_abbreviation_applet","homepage_receive_voucher_abbreviation_app");
    public static final String AD_MULTI_CHOICE_ONE = "multiChoiceOne";
    /**
     * 尾货信息流广告位
     */
    public static final List<String> WEIHUO_INFO_FLOW_AD_SYMBOL_LIST = Arrays.asList("quoted_price_seventh_info_flow_single", "quoted_price_seventh_info_flow_two", "order_detail_recommend_first_info_flow");
    /**
     * 附件类型-图片
     */
    public static String ATTACHMENT_TYPE_IMAGE = "image";
    /**
     * 信息的类型-尾货订单
     */
    public static String ORDER_TYPE_WH = "wh";
    /**
     * 数据源类型-尾货订单
     */
    public static String MATERIAL_SOURCE_TYPE_WEIHUO = "weihuo";
    /**
     * 数据源获取方式：auto-自动，hand-手动
     */
    public static String MATERIAL_GET_TYPE_AUTO = "auto";
    /**
     * 广告需要返回额外出参的广告位列表配置
     * master_app_open_screen 师傅开屏广告位
     */
    public static final List<String> AD_HOT_START_PARAM_AD_SYMBOL_LIST = Arrays.asList("master_app_open_screen");

}
