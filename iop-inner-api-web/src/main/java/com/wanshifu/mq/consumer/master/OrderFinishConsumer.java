package com.wanshifu.mq.consumer.master;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.InterfaceFeign.MasterInterface;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.activity.domain.api.request.UpdateBatchByActivityTaskRqt;
import com.wanshifu.iop.activity.domain.api.request.business.ActivityTaskRunRqt;
import com.wanshifu.iop.activity.domain.bo.MasterOrderLimitObjectBo;
import com.wanshifu.iop.activity.domain.bo.OrderInfoBo;
import com.wanshifu.iop.activity.domain.enums.TaskTypeEnum;
import com.wanshifu.iop.activity.service.api.ActivityBusinessServiceApi;
import com.wanshifu.iop.inner.api.domains.bo.OrderBaseBo;
import com.wanshifu.iop.inner.api.domains.bo.OrderFilterConditionBo;
import com.wanshifu.iop.inner.api.domains.bo.mq.master.MasterOrderFinishMqBo;
import com.wanshifu.iop.inner.api.domains.bo.mq.master.MasterOrderFinishMqBo.MasterCommissionBo;
import com.wanshifu.iop.inner.api.domains.bo.mq.master.QuotationOrderMqBo;
import com.wanshifu.iop.inner.api.domains.enums.UserTypeApiEnum;
import com.wanshifu.manager.FilterConditionManager;
import com.wanshifu.master.innerapi.domains.request.order.info.GetOrderReserveShamRqt;
import com.wanshifu.master.innerapi.domains.request.order.push.BatchGetPushDistanceRqt;
import com.wanshifu.master.innerapi.domains.response.ocs.BatchGetPushDistanceResp;
import com.wanshifu.master.innerapi.domains.response.order.GetOrderReserveShamResp;
import com.wanshifu.master.innerapi.service.api.order.OrderMessageApi;
import com.wanshifu.mq.config.MqConst;
import com.wanshifu.util.BigDataAliYunUtils;
import com.wanshifu.util.CoverUtils;

import java.text.ParseException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import com.wanshifu.util.DateHelper;
import com.wanshifu.util.MasterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * 交易完成任务消息订阅
 *
 * @Author: <EMAIL>
 * @Date: 2022-04-28 10:52
 * @Description:
 */
@Slf4j
@Topic("${wanshifu.rocketMQ.common.order.general.topic}")
@Component
public class OrderFinishConsumer {

    @Resource
    private BigDataAliYunUtils bigDataAliYunUtils;
    @Resource
    private CoverUtils coverUtils;
    @Resource
    private ActivityBusinessServiceApi activityBusinessServiceApi;
    @Resource
    private OrderMessageApi orderMessageApi;
    @Resource
    private MasterInterface masterInterface;
    @Resource
    private FilterConditionManager filterConditionManager;
    @Resource
    private MasterUtil masterUtil;


    /*** 交易完成任务执行
     *
     * @Description：
     * @param: activityTaskRunRqt
     * @return：
     */
    @Tag(value = MqConst.ORDER_FINISH_TAG, desc = "订单成交任务执行")
    public Action taskRun(@Validated @MessageBody MasterOrderFinishMqBo orderFinishMqBo) throws ParseException {
        ActivityTaskRunRqt activityTaskRunRqt = createOrderMqBoMethod(orderFinishMqBo);
        log.info("订单成交任务,activityTaskRunRqt:"+JSONObject.toJSONString(activityTaskRunRqt));

        UpdateBatchByActivityTaskRqt updateBatchByActivityTaskRqt = createUpdateBatchByActivityTaskRqt(orderFinishMqBo);
        log.info("订单成交任务,updateBatchByActivityTaskRqt:"+JSONObject.toJSONString(updateBatchByActivityTaskRqt));

        try {
            int runResult = activityBusinessServiceApi.taskRun(activityTaskRunRqt);
            int updateBatchRewardGiveQualificationState = activityBusinessServiceApi.updateBatchRewardGiveQualificationState(updateBatchByActivityTaskRqt);
            //奖励节点校验更新 任务执行结果
            if (runResult > 0 && updateBatchRewardGiveQualificationState > 0) {
                return Action.CommitMessage;
            }
        } catch (Exception exception) {
            log.error("订单成交任务执行失败", exception);
            exception.printStackTrace();
        }

        return Action.ReconsumeLater;
    }

    /***
     * @Description：创建订单成交入参
     * @param: orderFinishMqBo
     * @return：
     */
    private ActivityTaskRunRqt createOrderMqBoMethod(MasterOrderFinishMqBo orderFinishMqBo) throws ParseException {
        ActivityTaskRunRqt activityTaskRunRqt = new ActivityTaskRunRqt();

        BeanUtils.copyProperties(orderFinishMqBo, activityTaskRunRqt);
        activityTaskRunRqt.setTaskSymbol(TaskTypeEnum.ORDER_FINISH.symbol);
        activityTaskRunRqt.setUserClass(UserTypeApiEnum.MASTER.type);
        activityTaskRunRqt.setExecuteTime(orderFinishMqBo.getExecuteTime());
        activityTaskRunRqt.setOrderCreateTime(orderFinishMqBo.getOrderCreateTime());
        activityTaskRunRqt.setExtraNo(orderFinishMqBo.getExtraId().toString());


        OrderInfoBo orderInfoBo = new OrderInfoBo();
        BeanUtils.copyProperties(orderFinishMqBo.getOrderBase(), orderInfoBo);
        //转换appointMethod
        orderInfoBo.setAppointMethod(coverUtils.coverAppointMethod(orderFinishMqBo.getAppointType()));
        Map<String, Object> ruleVariableMap = filterConditionManager.buildMasterOrderVariable(orderInfoBo);
        ruleVariableMap.put("userId",orderFinishMqBo.getUserId());
        ruleVariableMap.put("groupGoodNum",orderFinishMqBo.getGroupGoodNum());
//        ruleVariableMap.put("orderTime",orderFinishMqBo.getOrderPushTime().getTime());
        ruleVariableMap.put("fromAccountType",orderFinishMqBo.getOrderBase().getAccountType());
        //调用师傅接口，查询订单标签属性
        List<String> propertyList = masterUtil.buildMasterPropertyList(orderFinishMqBo.isOrderPackage(), orderFinishMqBo.getOrderBase().getOrderId(), orderFinishMqBo.getUserId());
        ruleVariableMap.put("propertyList",propertyList);
//        ruleVariableMap.put("offerNum", this.getOfferNum(quotationOrderMqBo));
//        ruleVariableMap.put("userCrowIds", userGroupIds);
//        ruleVariableMap.put("distanceValue",masterOrderLimitObjectBo.getDistanceValue());
        //执行时间
//        ruleVariableMap.put("currentDateTime", Objects.nonNull(orderFinishMqBo.getExecuteTime()) ? orderFinishMqBo.getExecuteTime().getTime() : null);
//        ruleVariableMap.put("orderCreateTime", Objects.nonNull(orderFinishMqBo.getOrderCreateTime()) ? orderFinishMqBo.getOrderCreateTime().getTime() : null);
//        ruleVariableMap.put("orderCreateAbsoluteTime", Objects.nonNull(orderFinishMqBo.getOrderCreateTime()) ? DateHelper.getTimeOfDay(orderFinishMqBo.getOrderCreateTime()) : null);
//        ruleVariableMap.put("skillRelatedState",masterOrderLimitObjectBo.getSkillRelatedState());

        activityTaskRunRqt.setRuleVariableMap(ruleVariableMap);

        return activityTaskRunRqt;
    }

    /***
     * @Description：构建bean
     * @param: quotationOrderMqBo
     * @return：
     */
    private UpdateBatchByActivityTaskRqt createUpdateBatchByActivityTaskRqt(MasterOrderFinishMqBo orderFinishMqBo) {
        UpdateBatchByActivityTaskRqt updateBatchByActivityTaskRqt = new UpdateBatchByActivityTaskRqt();
        OrderBaseBo orderBaseBo = orderFinishMqBo.getOrderBase();

        updateBatchByActivityTaskRqt.setTaskSymbol(TaskTypeEnum.ORDER_FINISH.symbol);
        updateBatchByActivityTaskRqt.setUserClass(UserTypeApiEnum.MASTER.type);
        updateBatchByActivityTaskRqt.setUserId(orderFinishMqBo.getUserId());
        updateBatchByActivityTaskRqt.setOrderStatus(orderBaseBo.getOrderStatus());
        updateBatchByActivityTaskRqt.setGlobalOrderTraceId(orderBaseBo.getGlobalOrderTraceId());
        updateBatchByActivityTaskRqt.setExecuteTime(orderFinishMqBo.getExecuteTime());
        //下单方账号类型
        updateBatchByActivityTaskRqt.setFromAccountType(orderBaseBo.getAccountType());
        //下单方订单id
        updateBatchByActivityTaskRqt.setThirdOrderId(orderFinishMqBo.getThirdOrderId());
        //预约开始时间
        updateBatchByActivityTaskRqt.setReserveStartTime(orderFinishMqBo.getReserveStartTime());
        //预约结束时间
        updateBatchByActivityTaskRqt.setReserveEndTime(orderFinishMqBo.getReserveEndTime());
        //期望上门时间
        updateBatchByActivityTaskRqt.setExpectDoorInStartDate(orderFinishMqBo.getExpectDoorInStartDate());
        //期望上门时间
        updateBatchByActivityTaskRqt.setExpectDoorInEndDate(orderFinishMqBo.getExpectDoorInEndDate());
        //虚假预约标记,预留接口
        boolean isFalsityReserve = false;
        try{
            isFalsityReserve = this.getFalsityReserve(orderFinishMqBo);
        }catch (Exception e){
            log.warn(e.getMessage());
        }
        updateBatchByActivityTaskRqt.setFalsityReserve(isFalsityReserve);
        return updateBatchByActivityTaskRqt;
    }

    private boolean getFalsityReserve(MasterOrderFinishMqBo orderFinishMqBo) {
        GetOrderReserveShamRqt getOrderReserveShamRqt = new GetOrderReserveShamRqt();
        getOrderReserveShamRqt.setOrderId(orderFinishMqBo.getExtraId());
        getOrderReserveShamRqt.setMasterId(orderFinishMqBo.getUserId());
        GetOrderReserveShamResp getOrderReserveShamResp = orderMessageApi.getOrderReserveSham(getOrderReserveShamRqt);
        return getOrderReserveShamResp.getIsReserveSham();
    }

    /**
     * 创建
     * @param orderFinishMqBo
     * @return
     */
    private OrderFilterConditionBo createOrderFilterConditionBo(MasterOrderFinishMqBo orderFinishMqBo) {
        OrderFilterConditionBo orderFilterConditionBo = new OrderFilterConditionBo();
        orderFilterConditionBo.setOrderPackage(orderFinishMqBo.isOrderPackage());

        return orderFilterConditionBo;
    }
}
