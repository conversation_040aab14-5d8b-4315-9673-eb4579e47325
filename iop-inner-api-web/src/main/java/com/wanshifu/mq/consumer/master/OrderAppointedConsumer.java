package com.wanshifu.mq.consumer.master;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.MessageBody;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Tag;
import com.wanshifu.framework.rocketmq.conusume.dispatcher.annotaion.Topic;
import com.wanshifu.iop.activity.domain.api.request.UpdateBatchByActivityTaskRqt;
import com.wanshifu.iop.activity.domain.api.request.business.ActivityTaskRunRqt;
import com.wanshifu.iop.activity.domain.bo.OrderInfoBo;
import com.wanshifu.iop.activity.domain.enums.TaskTypeEnum;
import com.wanshifu.iop.activity.service.api.ActivityBusinessServiceApi;
import com.wanshifu.iop.inner.api.domains.bo.OrderBaseBo;
import com.wanshifu.iop.inner.api.domains.bo.OrderFilterConditionBo;
import com.wanshifu.iop.inner.api.domains.bo.mq.master.MasterAppointedMqBo;
import com.wanshifu.iop.inner.api.domains.enums.MasterOrderLabelEnum;
import com.wanshifu.iop.inner.api.domains.enums.UserTypeApiEnum;
import com.wanshifu.manager.FilterConditionManager;
import com.wanshifu.mq.config.MqConst;
import com.wanshifu.util.ActivityUtil;
import com.wanshifu.util.BigDataAliYunUtils;
import com.wanshifu.util.CoverUtils;
import com.wanshifu.util.MasterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;

/**
 * 订单被指派任务执行消息订阅
 *
 * @Author: <EMAIL>
 * @Date: 2022-04-28 10:52
 * @Description:
 */
@Slf4j
@Topic("${wanshifu.rocketMQ.common.order.general.topic}")
@Component
public class OrderAppointedConsumer {
    @Resource
    private BigDataAliYunUtils bigDataAliYunUtils;
    @Resource
    private CoverUtils coverUtils;
    @Resource
    private ActivityBusinessServiceApi activityBusinessServiceApi;
    @Resource
    private MasterUtil masterUtil;
    @Resource
    private FilterConditionManager filterConditionManager;

    /*** 订单被指派任务执行
     *
     * @Description：
     * @param: activityTaskRunRqt
     * @return：
     */
    @Tag(value = MqConst.MASTER_ORDER_APPOINTED, desc = "师傅被指派更新任务状态")
    public Action taskRun(@Validated @MessageBody MasterAppointedMqBo masterAppointedMqBo) throws ParseException {
        UpdateBatchByActivityTaskRqt updateBatchByActivityTaskRqt = createUpdateBatchByActivityTaskRqt(masterAppointedMqBo);

        ActivityTaskRunRqt activityTaskRunRqt = createOrderMqBoMethod(masterAppointedMqBo);

        try {
            int taskRun = activityBusinessServiceApi.taskRun(activityTaskRunRqt);

            int updateBatchRewardGiveQualificationState = activityBusinessServiceApi.updateBatchRewardGiveQualificationState(updateBatchByActivityTaskRqt);
            //奖励节点校验更新 任务执行结果
            if (taskRun > 0 && updateBatchRewardGiveQualificationState > 0) {
                return Action.CommitMessage;
            }
        } catch (Exception exception) {
            log.error("师傅被指派更新任务状态,error={}", exception.getMessage());
            exception.printStackTrace();
        }

        return Action.ReconsumeLater;
    }

    /***
     * @Description：创建bean
     * @param: createOrderMqBo
     * @return：
     * @param masterAppointedMqBo
     */
    private ActivityTaskRunRqt createOrderMqBoMethod(MasterAppointedMqBo masterAppointedMqBo) throws ParseException {
        ActivityTaskRunRqt activityTaskRunRqt = new ActivityTaskRunRqt();

        BeanUtils.copyProperties(masterAppointedMqBo, activityTaskRunRqt);
        activityTaskRunRqt.setTaskSymbol(TaskTypeEnum.APPOINTED.symbol);
        activityTaskRunRqt.setUserClass(UserTypeApiEnum.MASTER.type);
        activityTaskRunRqt.setExecuteTime(masterAppointedMqBo.getExecuteTime());
        activityTaskRunRqt.setOrderCreateTime(masterAppointedMqBo.getOrderCreateTime());
        activityTaskRunRqt.setOrderTime(masterAppointedMqBo.getHireTime());
        //调用师傅接口，查询订单标签属性
        List<String> propertyList = masterUtil.buildMasterPropertyList(masterAppointedMqBo.isOrderPackage(), masterAppointedMqBo.getOrderBase().getOrderId(), masterAppointedMqBo.getUserId());
        activityTaskRunRqt.setOtherOrderFilterCondition(propertyList);
        OrderInfoBo orderInfoBo = new OrderInfoBo();
        BeanUtils.copyProperties(masterAppointedMqBo.getOrderBase(), orderInfoBo);
        orderInfoBo.setAppointMethod(coverUtils.coverAppointMethod(masterAppointedMqBo.getAppointType()));
        activityTaskRunRqt.setUserCrowIds(bigDataAliYunUtils.getUserGroupIds(String.valueOf(masterAppointedMqBo.getOrderBase().getAccountId()), "1", "1"));
        activityTaskRunRqt.setExtraNo(masterAppointedMqBo.getExtraId().toString());
        activityTaskRunRqt.setOrderInfo(orderInfoBo);
        activityTaskRunRqt.setFromAccountType(masterAppointedMqBo.getOrderBase().getAccountType());

        Map<String, Object> ruleVariableMap = filterConditionManager.buildMasterOrderVariable(orderInfoBo);
        ruleVariableMap.put("groupGoodNum",masterAppointedMqBo.getGroupGoodNum());
        ruleVariableMap.put("fromAccountType",masterAppointedMqBo.getOrderBase().getAccountType());
        ruleVariableMap.put("orderTime", Objects.nonNull(masterAppointedMqBo.getHireTime()) ? masterAppointedMqBo.getHireTime().getTime() : null);
        ruleVariableMap.put("currentDateTime",Objects.nonNull(masterAppointedMqBo.getExecuteTime()) ? masterAppointedMqBo.getExecuteTime().getTime() : null);
        ruleVariableMap.put("propertyList",propertyList);

        activityTaskRunRqt.setRuleVariableMap(ruleVariableMap);

        return activityTaskRunRqt;
    }

    /***
     * @Description：构建bean
     * @param: quotationOrderMqBo
     * @return：
     */
    private UpdateBatchByActivityTaskRqt createUpdateBatchByActivityTaskRqt(MasterAppointedMqBo masterAppointedMqBo) {
        UpdateBatchByActivityTaskRqt updateBatchByActivityTaskRqt = new UpdateBatchByActivityTaskRqt();
        OrderBaseBo orderBaseBo = masterAppointedMqBo.getOrderBase();
        updateBatchByActivityTaskRqt.setTaskSymbol(TaskTypeEnum.APPOINTED.symbol);
        updateBatchByActivityTaskRqt.setExecuteTime(masterAppointedMqBo.getExecuteTime());
        updateBatchByActivityTaskRqt.setUserClass(UserTypeApiEnum.MASTER.type);
        updateBatchByActivityTaskRqt.setUserId(masterAppointedMqBo.getUserId());
        updateBatchByActivityTaskRqt.setOrderStatus(orderBaseBo.getOrderStatus());
        updateBatchByActivityTaskRqt.setGlobalOrderTraceId(orderBaseBo.getGlobalOrderTraceId());
        return updateBatchByActivityTaskRqt;
    }

    /**
     * 设置订单标签信息
     *
     * @param masterAppointedMqBo
     * @param orderTagList
     * @return
     */
    private OrderFilterConditionBo createOrderFilterConditionBo(MasterAppointedMqBo masterAppointedMqBo, List<String> orderTagList) {
        OrderFilterConditionBo orderFilterConditionBo = new OrderFilterConditionBo();
        orderFilterConditionBo.setOrderPackage(masterAppointedMqBo.isOrderPackage());
        if(CollectionUtils.isEmpty(orderTagList)){
            return orderFilterConditionBo;
        }
        for(String orderTag : orderTagList){
            if(MasterOrderLabelEnum.CONTRACT.label.equals(orderTag)){
                orderFilterConditionBo.setOrderContract(true);
            } else if(MasterOrderLabelEnum.MASTER_SHOP.label.equals(orderTag)){
                orderFilterConditionBo.setOrderShop(true);
            }
        }
        return orderFilterConditionBo;
    }
}
