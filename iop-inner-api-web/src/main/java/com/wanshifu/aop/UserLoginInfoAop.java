package com.wanshifu.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.bo.LoginUserInfoBo;
import com.wanshifu.constant.CommonConstant;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.web.utils.RequestUtil;
import com.wanshifu.iop.inner.api.domains.enums.UserTypeApiEnum;
import com.wanshifu.iop.inner.api.domains.vo.resp.CustomerUserInfoWebDtoResp;
import com.wanshifu.iop.inner.api.domains.vo.resp.MasterInfoDtoResp;
import com.wanshifu.iop.inner.api.domains.vo.resp.UserInfoWebDtoResp;
import com.wanshifu.util.RedisExUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/7/4 9:21
 * @Desc 获取用户信息
 * 师傅用户共用
 */
@Aspect
@Component
public class UserLoginInfoAop {

    @Resource
    public RedisExUtil redisExUtil;

    /**
     * 是否允许app访问
     */
    @Value("${wanshifu.app.enable}")
    private Boolean appEnable;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Pointcut("@annotation(com.wanshifu.annotation.UserLoginInfo)")
    public void myPointcut() {

    }

    @Before("myPointcut()")
    public void mylogger(JoinPoint pjp) throws Throwable {

        Object[] arr = pjp.getArgs();

        Cookie[] cookies = httpServletRequest.getCookies();
        String token = null;

        //判断是否是web访问
        Integer isWeb = 1;
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("wsf_user_token".equals(cookie.getName())) {
                    token = cookie.getValue();
                    isWeb = 2;
                }
            }
        }

        //师傅token
        String masterToken = httpServletRequest.getHeader("signature");

        String signature = null;
        Object signatureObj = JSON.parseObject(JSON.toJSONString(arr[0])).get("signature");
        if (!ObjectUtils.isEmpty(signatureObj)) {
            //业务请求参数签名
            signature = signatureObj.toString();
        }

        //tokenApp
        String tokenApp = httpServletRequest.getHeader("appToken");

        //家庭token
        String customerToken = httpServletRequest.getHeader("token");

        //是否是app
        boolean isApp = com.wanshifu.framework.utils.StringUtils.isNotEmpty(tokenApp);
        if (!appEnable && isApp) {
            throw new BusinessException("缺少token");
        }

        String userClass;
        String tmpToken;
        if (StringUtils.isEmpty(masterToken) && StringUtils.isEmpty(signature) && StringUtils.isEmpty(customerToken)) {
            //商家
            userClass = UserTypeApiEnum.MERCHANT.type;
            tmpToken = StringUtils.isEmpty(token) ? tokenApp : token;
        } else {
            //师傅
            userClass = UserTypeApiEnum.MASTER.type;
            tmpToken = StringUtils.isEmpty(masterToken) ? signature : masterToken;
        }

        if (!StringUtils.isEmpty(customerToken)) {
            //家庭用户
            tmpToken = customerToken;
            userClass = UserTypeApiEnum.CUSTOMER.type;
        }

        LoginUserInfoBo userLoginInfo = this.getUserLoginInfo(tmpToken, userClass);

        Long userId = userLoginInfo.getUserId();
        String name = userLoginInfo.getName();
        String phone = userLoginInfo.getPhone();

        //父类属性必须是public修饰
        Field[] declaredFields = arr[0].getClass().getFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            if ("userId".equals(declaredField.getName())) {
                declaredField.set(arr[0], userId);
            }
            if ("masterName".equals(declaredField.getName())) {
                declaredField.set(arr[0], name);
            }
            if ("phone".equals(declaredField.getName())) {
                declaredField.set(arr[0], phone);
            }

            if ("token".equals(declaredField.getName())) {
                declaredField.set(arr[0], token);
            }
            if ("tokenApp".equals(declaredField.getName())) {
                declaredField.set(arr[0], tokenApp);
            }
            if ("isWeb".equals(declaredField.getName())) {
                declaredField.set(arr[0], isWeb);
            }
            if ("ip".equals(declaredField.getName())) {
                String ip = RequestUtil.getRemoteAddr(httpServletRequest);
                declaredField.set(arr[0], ip);
            }
            if ("client".equals(declaredField.getName())) {
                String client = this.getPort(getClientDefaultApplet());
                declaredField.set(arr[0], client);
            }
            if ("deviceId".equals(declaredField.getName())) {
                String deviceId = this.getDeviceId();
                declaredField.set(arr[0], deviceId);
            }
        }
    }

    public LoginUserInfoBo getUserLoginInfo(String token, String userClass) {

        LoginUserInfoBo result = new LoginUserInfoBo();

        UserTypeApiEnum userTypeApiEnum = UserTypeApiEnum.fromType(userClass);

        String valueBykey = redisExUtil.getValueBykey(token, userTypeApiEnum.type);
        JSONObject userInfo = JSONObject.parseObject(valueBykey);

        switch (userTypeApiEnum) {
            case MERCHANT:
                UserInfoWebDtoResp userInfoWebDtoResp = JSONObject.toJavaObject(userInfo, UserInfoWebDtoResp.class);
                result.setUserId(userInfoWebDtoResp.getUserId());
                result.setName("");
                result.setPhone("");
                return result;
            case MASTER:
                MasterInfoDtoResp masterInfoWebDtoResp = JSONObject.toJavaObject(userInfo, MasterInfoDtoResp.class);
                result.setUserId(masterInfoWebDtoResp.getMasterId());
                result.setName(masterInfoWebDtoResp.getMasterName());
                result.setPhone(masterInfoWebDtoResp.getPhone());
                return result;
            case CUSTOMER:
                CustomerUserInfoWebDtoResp customerUserInfoWebDtoResp = JSONObject.toJavaObject(userInfo, CustomerUserInfoWebDtoResp.class);
                result.setName(customerUserInfoWebDtoResp.getUser().getNickname());
                result.setUserId(customerUserInfoWebDtoResp.getUser().getUserId());
                result.setPhone(customerUserInfoWebDtoResp.getUser().getMobile());
                return result;
            default:
                return result;
        }
    }

    /**
     * getPort
     * @param origin
     * @return
     */
    private String getPort(String origin) {
        if ("web".equals(origin) || "WEB".equals(origin)) {
            return "PC";
        }
        if ("android".equals(origin) || "ANDROID".equals(origin) || "Android".equals(origin)){
            return "ANDROID";
        }
        if ("ios".equals(origin) || "IOS".equals(origin) || "iOS".equals(origin)){
            return "IOS";
        }
        if (Arrays.asList("toc_yipay", "yipay").contains(origin)){
            return "WAP";
        }
        if (Arrays.asList("wechat", "WECHAT", "weixin", "WEIXIN", "小程序", "h5").contains(origin)) {
            return "APPLET";
        }
        return origin;
    }

    /**
     * 用于传值给风控时获取端, 其他场景不要用
     * @return
     */
    public String getClientDefaultApplet() {
//        HttpServletRequest request = getRequest();
        Enumeration<?> enum1 = httpServletRequest.getHeaderNames();
        String client = "applet";
        while (enum1.hasMoreElements()) {
            String key = (String) enum1.nextElement();
            if (key.equalsIgnoreCase(CommonConstant.CLIENT)) {
                client = httpServletRequest.getHeader(CommonConstant.CLIENT);
            }
        }
        if (Objects.equals(client,"android") || Objects.equals(client,"ios") || Objects.equals(client,"applet")){
            // 转大写
            return client.toUpperCase();
        } else {
            return "APPLET";
        }
    }
    /**
     * 获取设备id
     * @return deviceId
     */
    public String getDeviceId() {
//        HttpServletRequest request = RequestContext.getRequest();
        Enumeration<?> enum1 = httpServletRequest.getHeaderNames();
        String deviceId = null;
        while (enum1.hasMoreElements()) {
            String key = (String) enum1.nextElement();
            if (key.equalsIgnoreCase(CommonConstant.DEVICE_ID)) {
                deviceId = httpServletRequest.getHeader(CommonConstant.DEVICE_ID);
//                String uri = request.getRequestURI();
//                log.info("uri:"+ uri + " deviceId:" + deviceId);
            }
        }
        return deviceId;
    }


}
