package com.wanshifu.bo;

import com.wanshifu.iop.inner.api.domains.bo.RewardDetailInfoBo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2024/12/31 14:56
 */
@Data
public class CustomerActivityBaseBo {
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动描述
     */
    private String activityDescription;

    /**
     * 活动开始时间
     */
    private Date activityStartTime;

    /**
     * 活动结束时间
     */
    private Date activityEndTime;

    /**
     * 活动投放开始时间
     */
    private Date launchStartTime;

    /**
     * 活动投放结束时间
     */
    private Date launchEndTime;
    /**
     * 用户分类[master:师傅,customer:客户,merchant:商家]
     */
    private String userClass;

    /**
     * 活动模型[cash_subsidy:现金补贴,prize_task:有奖任务]
     */
    private String activityModel;

    /**
     * 活动报名方式[auto:自动报名，manual：手动报名]
     */
    private String applyMethod;

    /**
     * 活动报名开始时间
     */
    private Date applyStartTime;

    /**
     * 活动报名结束时间
     */
    private Date applyEndTime;

    /**
     * 奖励发放方式[auto:自动发放,manual:手动领取]
     */
    private String rewardGiveMethod;
    /**
     * 奖励领取开始时间
     */
    private Date rewardGiveStartTime;

    /**
     * 奖励领取结束时间
     */
    private Date rewardGiveEndTime;
    /**
     * 是否报名 1：已报名 0：未报名
     */
    private Integer applyState;
    /**
     * 报名时间
     */
    private Date applyTime;
    /**
     * 奖励领取状态 1：已领取 0：未领取
     */
    private Integer rewardGiveState;
    /**
     * 扩展id：优惠券id，券包id
     */
    private Long rewardExtraId;
    /**
     * 奖励类型：voucher-优惠券，voucher_pack-券包
     */
    private String rewardType;
    /**
     * 具体奖励信息
     */
    private List<RewardDetailInfoBo> rewardInfo;

//    @Data
//    public static class RewardInfoBo {
//        /**
//         * voucher:优惠券
//         */
//        private String rewardType;
//        /**
//         * 优惠券类型：money_off-满减,discount-折扣
//         */
//        private String rewardExtraType;
//        /**
//         * 优惠券id
//         */
//        private Long rewardExtraId;
//        /**
//         * 优惠券名称
//         */
//        private String rewardExtraName;
//        /**
//         * 最低门槛，例如 满100元
//         */
//        private BigDecimal minTradeAmount;
//        /**
//         * 优惠券最大面额
//         */
//        private BigDecimal rewardMaxValue;
//        /**
//         * 折扣系数：9.5折，折扣券才有--暂时用不到，家庭没有折扣券
//         */
//        private BigDecimal discount;
//        /**
//         * 奖励数量：优惠券张数
//         */
//        private Integer rewardNum;
//    }

}
