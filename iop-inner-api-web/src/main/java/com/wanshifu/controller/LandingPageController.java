package com.wanshifu.controller;

import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.request.landingPage.*;
import com.wanshifu.iop.inner.api.domains.request.master.GetDetailAggregationBannerApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryBarrageListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryTurntableLandingPageDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.response.CustomOrderListTagApiResp;
import com.wanshifu.iop.inner.api.domains.response.landingpage.*;
import com.wanshifu.iop.inner.api.domains.response.master.GetDetailAggregationBannerApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryBarrageListApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryTurntableLandingPageDetailApiResp;
import com.wanshifu.iop.inner.api.service.api.LandingPageServiceApi;
import com.wanshifu.service.LandingPageService;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import java.util.List;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 *
 * @author:<EMAIL>
 * @create:2022-08-23 18:29:31
 * @Description ：
 **/
@RestController
@RequestMapping("landingPage")
public class LandingPageController implements LandingPageServiceApi {
  @Resource
  public LandingPageService landingPageService;

  @Deprecated
  @Override
  @PostMapping("/master/getPrizeTasklandingPage")
  public MasterPrizeTaskApiResp getPrizeTasklandingPage(@Valid @RequestBody LandingPageRqt landingPageRqt) {
      return landingPageService.getPrizeTasklandingPage(landingPageRqt);
  }

  @Deprecated
  @Override
  @UserLoginInfo
  @PostMapping("/master/customOrderListTag")
  public CustomOrderListTagApiResp customOrderListTag(@Valid @RequestBody CustomOrderTagRqt customOrderTagRqt) {
      return landingPageService.customOrderListTag(customOrderTagRqt);
  }

  @Deprecated
  @Override
  @PostMapping("/master/getActivityModelById")
  public String getActivityModelById(@Valid @RequestBody ActivityDetailApiRqt activityDetailApiRqt) {
    return landingPageService.getActivityModelById(activityDetailApiRqt);
  }

  @Deprecated
  @Override
  @PostMapping("/master/getOrderTaskLandingPage")
  public MasterOrderTaskApiResp getOrderTaskLandingPage(@Valid @RequestBody MasterOrderLandingApiPageRqt masterOrderLandingPageRqt)
          throws ParseException {
    return landingPageService.getMasterOrderTaskLandingPage(masterOrderLandingPageRqt);
  }

  @Deprecated
  @Override
  @PostMapping("/merchant/getPrizeTaskLandingPage")
  public MetchantPrizeTaskApiResp getPrizeTaskLandingPage(@Valid @RequestBody MetchantPrizeLandingPageRqt metchantPrizeLandingPageRqt)
          throws ParseException {
    return landingPageService.getMerchantPrizeTaskLandingPage(metchantPrizeLandingPageRqt);
  }

  @Deprecated
  @Override
  @PostMapping("/merchant/getRechargeTaskLandingPage")
  public MerchantRechargeTaskApiResp getRechargeTaskLandingPage(@Valid @RequestBody
          MerchantRechargeTaskLandingPageRqt merchantRechargeTaskLandingPageRqt) throws ParseException {
    return landingPageService.getMerchantRechargeTaskLandingPage(merchantRechargeTaskLandingPageRqt);
  }

  /**
   * 商家后台多状态广告位数据-专用
   * */
  @Override
  @UserLoginInfo
  @PostMapping("merchant/getMerchantActivityDetailAd")
  public MerchantActivityDetailAdApiResp getMerchantActivityDetailAd(@Valid @RequestBody GetActivityDetailApiRqt getActivityDetailRqt) {
    return landingPageService.getMerchantActivityDetailAd(getActivityDetailRqt);
  }
  /**
   * 商家后台多状态广告位数据-order_top_banner专用
   * */
  @Override
  @UserLoginInfo
  @PostMapping("merchant/getMerchantActivityDetailBannerAd")
  public MerchantActivityDetailBannerAdApiResp getMerchantActivityDetailBannerAd(@Valid @RequestBody GetActivityDetailBannerApiRqt getActivityDetailRqt) {
    return landingPageService.getMerchantActivityDetailBannerAd(getActivityDetailRqt);
  }
  /**
   * 商家后台多状态广告位数据-通用
   * */
  @Override
  @UserLoginInfo
  @PostMapping("merchant/getMerchantActivityDetailMultiStatusAd")
  public MerchantActivityDetailMultiStatusAdApiResp getMerchantActivityDetailMultiStatusAd(@Valid @RequestBody MerchantActivityDetailMultiStatusAdApiRqt getActivityDetailRqt) {
    return landingPageService.getMerchantActivityDetailMultiStatusAd(getActivityDetailRqt);
  }

  /**
   * 师傅有奖任务进入落地页之前的预请求接口
   * */
  @UserLoginInfo
  @PostMapping("master/prizeTask/pre")
  public PrizeTaskPreResp getPrizeTaskPre(@Valid @RequestBody PrizeTaskPreRqt prizeTaskPreRqt){
      return landingPageService.getPrizeTaskPre(prizeTaskPreRqt);
  }
  /**
   * 发送短信验证码
   */
  @Override
  @PostMapping("/master/sendMessageCode")
  public boolean sendMessageCode(@Valid @RequestBody SendMessageCodeRqt sendMessageCodeRqt){
    return landingPageService.sendMessageCode(sendMessageCodeRqt);
  }

  /**
   * 发送短信验证码
   */
  @Override
  @PostMapping("/checkPhoneAndSave")
  public Integer checkPhoneAndSave(@Valid @RequestBody CheckPhoneAndSaveRqt checkPhoneAndSaveRqt){
    return landingPageService.checkPhoneAndSave(checkPhoneAndSaveRqt);
  }

  /**
   * 获取隐私政策
   */
  @Override
  @PostMapping("/master/getPrivacyPolicy")
  public GetCurrentAgreementInfoApiResp getPrivacyPolicy(){
    return landingPageService.getPrivacyPolicy();
  }

  /**
   * 获取隐私政策
   */
  @Override
  @PostMapping("/master/setPhoneInsert")
  public boolean setPhoneInsert(@Valid @RequestBody SetPhoneInsertApiRqt setPhoneInsertRqt) {
    return landingPageService.setPhoneInsert(setPhoneInsertRqt);
  }

  @Override
  @PostMapping("/master/getInviterTaskLandingPageDetail")
  public MasterInviterTaskDetailApiResp getInviterTaskLandingPageDetail(@Valid @RequestBody MasterInviterTaskDetailApiRqt masterInviterTaskDetailApiRqt) throws ParseException {
    return landingPageService.getInviterTaskLandingPageDetail(masterInviterTaskDetailApiRqt);
  }
  @Override
  @PostMapping("/master/getInviterRewardInfoTaskPage")
  public MasterInviterTaskMyRewardApiResp getInviterRewardInfoTaskPage(@Valid @RequestBody MasterInviterTaskDetailMyRewardApiRqt masterInviterTaskDetailApiRqt) throws ParseException {
    return landingPageService.getInviterRewardInfoTaskPage(masterInviterTaskDetailApiRqt);
  }

  @Override
  @PostMapping("/master/getInviteeTaskOutLandingPageDetail")
  public MasterInviteeTaskOutDetailApiResp getInviteeTaskOutLandingPageDetail(@Valid @RequestBody MasterInviteeTaskOutDetailApiRqt masterInviteeTaskOutDetailApiRqt) throws ParseException {
    return landingPageService.getInviteeTaskOutLandingPageDetail(masterInviteeTaskOutDetailApiRqt);
  }

  @Override
  @PostMapping("/master/getInviteeTaskInLandingPageDetail")
  public MasterInviteeTaskInDetailApiResp getInviteeTaskInLandingPageDetail(@Valid @RequestBody MasterInviteeTaskInDetailApiRqt masterInviteeTaskInDetailApiRqt) throws ParseException {
    return landingPageService.getInviteeTaskInLandingPageDetail(masterInviteeTaskInDetailApiRqt);
  }

  /**
   * 师傅邀请活动-集合页
   */
  @Override
  @PostMapping("/master/getInviteTaskCollectPageDetail")
  public MasterInviteTaskCollectPageDetailApiResp getInviteTaskCollectPageDetail(@Valid @RequestBody MasterInviteTaskCollectPageDetailApiRqt masterInviteTaskCollectPageDetailApiRqt) throws ParseException{
    return landingPageService.getInviteTaskCollectPageDetail(masterInviteTaskCollectPageDetailApiRqt);
  }
  /**
   * 师傅邀请活动-集合页-- 我的奖励
   */
  @Override
  @PostMapping("/master/getCollectMyRewardData")
  public MasterInviteTaskCollectPageMyRwardInfoApiResp getCollectMyRewardData(@Valid @RequestBody MasterInviteTaskCollectPageMyRwardInfoApiRqt masterInviteTaskCollectPageMyRwardInfoApiRqt) throws ParseException {
    return landingPageService.getInviteTaskCollectPageMyRewardInfo(masterInviteTaskCollectPageMyRwardInfoApiRqt);
  }

  @Override
  @PostMapping("/master/getSettledState")
  public GetAccountStatusApiResp getSettledState(@Valid @RequestBody GetSettledStateRqt getSettledStateRqt) {
    return landingPageService.getSettledState(getSettledStateRqt);
  }
//  /**
//   * 获取活动单个任务订单限制
//   */
//  @Override
//  @PostMapping("/getActivityTaskOrderLimitInfo")
//  public GetActivityTaskOrderLimitApiResp getActivityTaskOrderLimitInfo(@Valid @RequestBody GetActivityTaskOrderLimitApiRqt getActivityTaskOrderLimitApiRqt){
//    return landingPageService.getActivityTaskOrderLimitInfo(getActivityTaskOrderLimitApiRqt);
//  }

  @Override
  @PostMapping("/master/getLotteryTurntableLandingPageDetail")
  public LotteryTurntableLandingPageDetailApiResp getLotteryTurntableLandingPageDetail(@Valid @RequestBody LotteryTurntableLandingPageDetailApiRqt rqt) {
    return landingPageService.getLotteryTurntableLandingPageDetail(rqt);
  }

  @Override
  @PostMapping("/master/getLotteryBarrageList")
  public List<LotteryBarrageListApiResp> getLotteryBarrageList(@Valid @RequestBody LotteryBarrageListApiRqt rqt){
    return landingPageService.getLotteryBarrageList(rqt);
  }

  @Override
  @PostMapping("/master/getDetailAggregationBanner")
  @FeignTimeout(connectTimeoutMillis = 1000,readTimeoutMillis = 1000)
  @UserLoginInfo
  public GetDetailAggregationBannerApiResp getDetailAggregationBanner(@Valid @RequestBody GetDetailAggregationBannerApiRqt rqt) {
    return landingPageService.getDetailAggregationBanner(rqt);
  }


}