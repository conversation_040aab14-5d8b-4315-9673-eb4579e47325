package com.wanshifu.controller;

import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.request.CompleteOrderApiRqt;
import com.wanshifu.iop.inner.api.domains.request.OrderDetailRewardRemindApiRqt;
import com.wanshifu.iop.inner.api.domains.request.OrderListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.*;
import com.wanshifu.iop.inner.api.domains.request.master.OfferInfoActivityApiReq;
import com.wanshifu.iop.inner.api.domains.request.master.OrderCheckInfoDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.OrderDetailRewardRemindBatchApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.OrderListIdListReq;
import com.wanshifu.iop.inner.api.domains.request.master.OrderRewardProgressReq;
import com.wanshifu.iop.inner.api.domains.request.master.OrderRewardTotalReq;
import com.wanshifu.iop.inner.api.domains.request.master.ReservationInfoReq;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.*;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.master.*;
import com.wanshifu.service.TocMasterActivityService;
import com.wanshifu.service.TocMasterSpecialService;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @date Created in 2022/7/1 14:32
 */
@RestController
@RequestMapping("toc/activity/master")
public class TocMasterSpecialController {
    @Resource
    private TocMasterSpecialService tocMasterSpecialService;

    @Resource
    private TocMasterActivityService tocMasterActivityService;

    /**
     * 获奖提醒弹窗
     *
     * @param masterRewardRemindApiRqt
     * @return
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/rewardRemind")
    public MasterRewardRemindApiResp rewardRemind(@Valid @RequestBody TocMasterRewardRemindApiRqt masterRewardRemindApiRqt) {
        return tocMasterSpecialService.rewardRemind(masterRewardRemindApiRqt);
    }

    /**
     * 师傅端活动中心待领奖数
     *
     * @param masterAwardingNumApiRqt
     * @return
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/awardingNum")
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    public MasterAwardingNumApiResp awardingNum(@Valid @RequestBody TocMasterAwardingNumApiRqt masterAwardingNumApiRqt) {
        return tocMasterSpecialService.awardingNum(masterAwardingNumApiRqt);
    }

    /**
     * 订单报价详情活动列表
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/orderInfoReward")
    public MasterOrderSummaryApiResp orderInfoReward(@Valid @RequestBody TocOfferInfoActivityListApiRqt offerInfoActivityListApiRqt) {
        return tocMasterSpecialService.orderInfoReward(offerInfoActivityListApiRqt);
    }

    /**
     * 订单报价详情活动列表
     */
    @UserLoginInfo
    @PostMapping("/orderInfoRewardTag")
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    public OfferOrderInfoApiResp orderInfoRewardTag(@Valid @RequestBody TocOfferInfoActivityApiReq req) {
        return tocMasterSpecialService.orderInfoRewardTag(req);
    }

    /**
     * 师傅奖励列表
     */
    @Deprecated
    //@Override
    @UserLoginInfo
    @PostMapping("/rewardList")
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    public SimplePageInfo<MasterRewardListApiResp> rewardList(@Valid @RequestBody TocMasterRewardListApiRqt masterRewardListApiRqt) {
        return tocMasterActivityService.rewardList(masterRewardListApiRqt);
    }

    /**
     * 抢单详情
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/grabInfoBt")
    public GrabOrderButtonApiResp grabInfoBt(@Valid @RequestBody TocOfferInfoActivityListApiRqt offerInfoActivityListApiRqt) {
        return tocMasterActivityService.grabInfoBt(offerInfoActivityListApiRqt);
    }

    /**
     * 抢单列表
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/orderListTag")
    public MasterOrderListResp orderListTag(@Valid @RequestBody TocOrderListApiRqt orderListApiRqt) {
        return tocMasterActivityService.orderListTag(orderListApiRqt);
    }

    /**
     * 完工详情页奖励提示
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/completeReward")
    public CompleteOrderApiResp completeReward(@Valid @RequestBody TocCompleteOrderApiRqt completeOrderApiRqt) {
        return tocMasterActivityService.completeReward(completeOrderApiRqt);
    }

    /**
     * 订单详情奖励提醒---已废弃
     *
     * @param orderDetailRewardRemindApiRqt
     * @return
     */
    @Deprecated
    //@Override
    @UserLoginInfo
    @PostMapping("/orderDetailRewardRemind")
    public OrderDetailRewardRemindApiResp orderDetailRewardRemind(@Valid @RequestBody TocOrderDetailRewardRemindApiRqt orderDetailRewardRemindApiRqt) {
        return tocMasterActivityService.orderDetailRewardRemind(orderDetailRewardRemindApiRqt);
    }

    /**
     * 订单详情奖励提醒
     *
     * @param req
     * @return
     */
    @UserLoginInfo
    @PostMapping("/orderDetailRewardNotice")
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    public OrderDetailRewardRemindBatchApiResp orderDetailRewardNotice(@Valid @RequestBody TocOrderDetailRewardRemindBatchApiRqt req) {
        return tocMasterActivityService.orderDetailRewardNotice(req);
    }

    /**
     * 获取待领取奖励和待解锁奖励数据
     *
     * @param masterAwardingNumApiRqt
     * @return
     */
    //@Override
    @PostMapping("/getAwardingNumAndUnLockNum")
    @FeignTimeout(connectTimeoutMillis = 1000, readTimeoutMillis = 1000)
    public AwardingNumAndUnLockNumResp getAwardingNumAndUnLockNum(@Valid @RequestBody TocMasterAwardingNumApiRqt masterAwardingNumApiRqt) {
        return tocMasterActivityService.getAwardingNumAndUnLockNum(masterAwardingNumApiRqt);
    }

    /**
     * 报价列表标签
     *
     * @param req
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/orderListSomeTag")
    @FeignTimeout(connectTimeoutMillis = 1000, readTimeoutMillis = 1000)
    public Map<String, OfferOrderListResp> orderListSomeTag(@Valid @RequestBody TocOrderListIdListReq req) {
        return tocMasterActivityService.orderListSomeTag(req);
    }

    /**
     * 报价列表--V2版本：增加奖励总额，图片标签
     */
    //@Override
    @UserLoginInfo
    @FeignTimeout(connectTimeoutMillis = 1000, readTimeoutMillis = 1000)
    @PostMapping("/orderListSomeTagV2")
    public Map<String, OfferOrderListV2Resp> orderListSomeTagV2(@Valid @RequestBody TocOrderListInfoListReq req) {
        return tocMasterActivityService.orderListSomeTagV2(req);
    }

    /**
     * 报价/订单详情页-活动奖励进度模块接口
     */
    //@Override
    @UserLoginInfo
    @FeignTimeout(connectTimeoutMillis = 1000, readTimeoutMillis = 1000)
    @PostMapping("/orderDetailRewardProgressModel")
    public OrderDetailRewardProgressResp orderDetailRewardProgressModel(@Valid @RequestBody TocOrderRewardProgressReq req) {
        return tocMasterActivityService.orderDetailRewardProgressModel(req);
    }

    /**
     * 获取单个订单活动奖励总金额接口:-报价专用-只获取红包金额
     */
    //@Override
    @UserLoginInfo
    @FeignTimeout(connectTimeoutMillis = 1000, readTimeoutMillis = 1000)
    @PostMapping("/orderRewardTotalAmount")
    public OrderRewardTotalResp orderRewardTotalAmount(@Valid @RequestBody TocOrderRewardTotalReq req) {
        return tocMasterActivityService.orderRewardTotalAmount(req);
    }

    /**
     * 预约、签到操作详情标签
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/reservationDetail")
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    public ReservationInfoApiResp reservationDetail(@Valid @RequestBody TocReservationInfoReq req) {
        return tocMasterActivityService.reservationDetail(req);
    }

    /**
     * 验收列表标签
     *
     * @param req
     * @return
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/orderCheckListSomeTag")
    @FeignTimeout(connectTimeoutMillis = 1000, readTimeoutMillis = 1000)
    public Map<String, OrderCheckListSomeTagApiResp> orderCheckListSomeTag(@Valid @RequestBody TocOrderCheckListSomeTagApiRqt req) {
        return tocMasterActivityService.orderCheckListSomeTag(req);
    }

    /**
     * 验收详情标签
     *
     * @param req
     * @return
     */
    //@Override
    @UserLoginInfo
    @PostMapping("/orderCheckInfoDetail")
    @FeignTimeout(connectTimeoutMillis = 500, readTimeoutMillis = 500)
    public OrderCheckInfoDetailApiResp orderCheckInfoDetail(@Valid @RequestBody TocOrderCheckInfoDetailApiRqt req) {
        return tocMasterActivityService.orderCheckInfoDetail(req);
    }

}
