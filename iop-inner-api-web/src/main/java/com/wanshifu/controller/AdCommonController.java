package com.wanshifu.controller;

import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.iop.inner.api.domains.request.GetAdSwitchRqt;
import com.wanshifu.iop.inner.api.domains.request.UpdateAdSwitchRqt;
import com.wanshifu.iop.inner.api.domains.request.ad.SaveUnInterestAdRqt;
import com.wanshifu.iop.inner.api.domains.response.GetAdSwitchResp;
import com.wanshifu.iop.inner.api.service.api.AdCommonServiceApi;
import com.wanshifu.service.AdCommonService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 广告公共接口
 *
 * <AUTHOR>
 * @date Created in 2025/4/9 10:40
 */
@RestController
@RequestMapping("ad/common")
public class AdCommonController implements AdCommonServiceApi {
    @Resource
    private AdCommonService adCommonService;

    /**
     * 保存不感兴趣广告接口
     */
    @Override
    @UserLoginInfo
    @PostMapping("/saveUnInterestAd")
    public Integer saveUnInterestAd(@Valid @RequestBody SaveUnInterestAdRqt rqt) {
        return adCommonService.saveUnInterestAd(rqt);
    }

    /**
     * 个性化广告开启/关闭
     * @param rqt
     * @return
     */
    @UserLoginInfo
    @PostMapping("/updatePersonalizationAdSwitch")
    public Integer updatePersonalizationAdSwitch(@Valid @RequestBody UpdateAdSwitchRqt rqt){
        return adCommonService.updatePersonalizationAdSwitch(rqt);
    }

    /**
     * 个性化广告详情
     * @return
     */
    @UserLoginInfo
    @PostMapping("/getPersonalizationAdSwitch")
    public GetAdSwitchResp getPersonalizationAdSwitch(@Valid @RequestBody GetAdSwitchRqt rqt){
        return adCommonService.getPersonalizationAdSwitch(rqt);
    }


}
