package com.wanshifu.controller;

import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.merchant.GetPayPageVoucherListApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MatchOrderActivityDetailApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MatchUserOrderListTagApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MerchantPublishOrderTagApiResp;
import com.wanshifu.iop.inner.api.service.api.ActivityServiceApi;
import com.wanshifu.service.ActivityService;
import com.wanshifu.service.CustomerService;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("activity/user")
@Slf4j
public class ActivityController implements ActivityServiceApi {

    @Resource
    public ActivityService activityService;

    @Resource
    public CustomerService customerService;


    @Override
    @UserLoginInfo
    @PostMapping("/getActivityDetail")
    public GetActivityDetailApiResp getActivityDetail(@Valid @RequestBody GetActivityDetailApiRqt getActivityDetailApiRqt) {
        return activityService.getActivityDetail(getActivityDetailApiRqt);
    }

    @Override
    @UserLoginInfo
    @PostMapping("/getList")
    public List<GetActivityListApiResp> getList(@Valid @RequestBody GetActivityListApiRqt getActivityListApiRqt) {
        return activityService.getList(getActivityListApiRqt);
    }

    /**
     * 弹窗结果回传
     */
    @Override
    @UserLoginInfo
    @PostMapping("/backDialogAd")
    public Integer backDialogAd(@Valid @RequestBody BackDialogAdRqt backDialogAdRqt) {
        return activityService.backDialogAd(backDialogAdRqt);
    }

    /**
     * 用户活动报名
     */
    @Override
    @UserLoginInfo
    @PostMapping("/apply")
    public Integer apply(@Valid @RequestBody ApplyActivityApiRqt applyActivityApiRqt) {
        return activityService.apply(applyActivityApiRqt);
    }

    /**
     * 用户活动报名
     */
    @Override
    @UserLoginInfo
    @PostMapping("/applyChoice")
    public Integer applyChoice(@Valid @RequestBody ActivityApplyApiChoiceRqt activityApplyApiChoiceRqt) {
        return activityService.applyChoice(activityApplyApiChoiceRqt);
    }

    /**
     * 用户活动报名-新
     */
    @Override
    @PostMapping("/applyChoiceNew")
    public Integer applyChoiceNew(@Valid @RequestBody ActivityApplyApiChoiceOfRqt activityApplyApiChoiceRqt) {
        return activityService.applyChoiceNew(activityApplyApiChoiceRqt);
    }
    /**
     * 家庭用户活动报名-新
     */
    @Override
    @PostMapping("/applyCustomer")
    public Integer applyCustomer(@Valid @RequestBody ActivityApplyCustomerApiRqt activityApplyCustomerRqt) {
        return activityService.applyCustomer(activityApplyCustomerRqt);
    }
    /**
     * 用户活动下发
     */
    @Override
    @UserLoginInfo
    @PostMapping("/issue")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public Integer issue(@Valid @RequestBody IssueApiRqt issueApiRqt) {
        return activityService.issue(issueApiRqt);
    }

    /**
     * 活动落地页详情浏览记录接口
     */
    @Override
    @UserLoginInfo
    @PostMapping("/actiLandingclickCollect")
    public Integer actiLandingclickCollect(@Valid @RequestBody EnterActivityDetailRqt enterActivityDetailRqt) {
        return activityService.actiLandingclickCollect(enterActivityDetailRqt);
    }

    /**
     * 家庭多状态广告点击记录接口
     */
    @Override
    @UserLoginInfo
    @PostMapping("/customerAdClickCollect")
    public Integer customerAdClickCollect(@Valid @RequestBody CustomerAdClickCollectRqt customerAdClickCollectRqt) {
        return activityService.customerAdClickCollect(customerAdClickCollectRqt);
    }

    /**
     * 获取弹窗广告
     */
    @UserLoginInfo
    @PostMapping("/getDialogAd")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public GetUserAdResp getDialogAd(@Valid @RequestBody GetUserPopAdRqt getUserPopAdRqt) {
        return activityService.getDialogAd(getUserPopAdRqt);
    }

    /**
     * 获取广告位广告（非弹窗）
     */
    @PostMapping("/getAdPosition")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public GetUserAdResp getAdPosition(@Valid @RequestBody GetUserAdRqt getUserAdRqt,
                                       @CookieValue(value = "wsf_user_token", required = false) String token,
                                       @RequestHeader(value = "appToken", required = false) String tokenApp,
                                       @RequestHeader(value = "signature", required = false) String signature
    ) {
        return activityService.getAdPosition(getUserAdRqt, token, tokenApp, signature);
    }

    /**
     * 获取家庭弹窗广告
     * @param customerAdRqt
     * @return
     */
    @PostMapping("/listCustomerPopAd")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public CustomerAdResp listCustomerPopAd(@Valid @RequestBody CustomerAdRqt customerAdRqt,
                                            @RequestHeader(value = "token", required = false) String token){
        CustomerAdResp customerAdResp = activityService.listCustomerPopAd(customerAdRqt,token);
        //20231121,方便排查问题,后期可删
//        log.info("获取家庭弹窗广告request:{},response:{}", JSON.toJSON(customerAdRqt), Objects.nonNull(customerAdResp) ? JSON.toJSON(customerAdResp) : null);
        return customerAdResp;
    }

    /**
     * 获取家庭广告位广告(非弹窗)
     * @param customerAdRqt
     * @return
     */
    @PostMapping("/listCustomerAdPositionAd")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public CustomerAdResp listCustomerAdPositionAd(@Valid @RequestBody CustomerAdRqt customerAdRqt,
                                                   @RequestHeader(value = "token", required = false) String token){
        return activityService.listCustomerAdPositionAd(customerAdRqt,token);
    }
    /**
     * 批量获取家庭广告位广告（非弹窗）
     * @param customerAdRqt
     * @return
     */
    @PostMapping("/batchGetCustomerAdPosition")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public CustomerAdResp batchGetCustomerAdPosition(@Valid @RequestBody BatchGetCustomerAdPositionApiRqt customerAdRqt,
                                                   @RequestHeader(value = "token", required = false) String token){
        return activityService.batchGetCustomerAdPosition(customerAdRqt,token);
    }
    /**
     * 批量获取广告位广告，非弹窗、非开屏广告
     */
    @Override
    @UserLoginInfo
    @PostMapping("/getAdPositionBatch")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public Map<String, GetUserAdResp> getAdPositionBatch(@Valid @RequestBody GetUserAdBatchRqt getUserAdBatchRqt) {
        return activityService.getAdPositionBatch(getUserAdBatchRqt);
    }
    /**
     * 获取活动中心列表广告
     */
    @PostMapping("/getListAd")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public GetUserAdResp getListAd(@Valid @RequestBody GetUserAdRqt getUserAdRqt,
                                       @CookieValue(value = "wsf_user_token", required = false) String token,
                                       @RequestHeader(value = "appToken", required = false) String tokenApp,
                                       @RequestHeader(value = "signature", required = false) String signature
    ) {
        return activityService.getListAd(getUserAdRqt, token, tokenApp, signature);
    }
    /**
     * 用户活动任务奖励领取
     *
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/taskGiveReward")
    @FeignTimeout(connectTimeoutMillis = 1500,readTimeoutMillis = 1500)
    public List<RewardGiveApiResp> taskGiveReward(@Valid @RequestBody ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt) {
        return activityService.taskGiveReward(activityTaskGiveRewardApiRqt);
    }
    /**
     * 多状态banner广告位专用批量领奖接口
     *
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/taskGiveRewardForMultiBannerAd")
    @FeignTimeout(connectTimeoutMillis = 1500,readTimeoutMillis = 1500)
    public List<RewardGiveBannerAdApiResp> taskGiveRewardForMultiBannerAd(@Valid @RequestBody RewardGiveBannerAdApiRqt rewardGiveBannerAdApiRqt) {
        return activityService.taskGiveRewardForMultiBannerAd(rewardGiveBannerAdApiRqt);
    }

    /**
     * 用户活动任务奖励领取--包装
     *
     * @return
     */
    @Override
    @PostMapping("/taskGiveRewardOf")
    @FeignTimeout(connectTimeoutMillis = 1500,readTimeoutMillis = 1500)
    public List<RewardGiveApiResp> taskGiveRewardOf(@Valid @RequestBody ActivityTaskGiveRewardApiOfRqt activityTaskGiveRewardApiRqt) {
        return activityService.taskGiveRewardOf(activityTaskGiveRewardApiRqt);
    }


    /**
     * 用户活动任务奖励领取
     *
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/singleTaskGiveReward")
    @FeignTimeout(connectTimeoutMillis = 1500,readTimeoutMillis = 1500)
    public RewardTypeApiResp singleTaskGiveReward(@Valid @RequestBody ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt) {
        return activityService.singleTaskGiveReward(activityTaskGiveRewardApiRqt);
    }

    /**
     * 获取活动目标任务还差次数/金额
     */
    @Override
    @UserLoginInfo
    @PostMapping("/getUserActivityTaskShort")
    public List<GetUserActivityTaskShortApiResp> getUserActivityTaskShort(@Valid @RequestBody GetActivityDetailApiRqt getActivityDetailApiRqt) {
        return activityService.getUserActivityTaskShort(getActivityDetailApiRqt);
    }

    /**
     * 商家充值页面优惠活动信息
     *
     * @param getRechargeDiscountActivityApiRqt
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/getRechargeDiscountInfo")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public GetRechargeDiscountActivityApiResp getRechargeDiscountInfo(@Valid @RequestBody GetRechargeDiscountActivityApiRqt getRechargeDiscountActivityApiRqt) {
        return activityService.getRechargeDiscountInfo(getRechargeDiscountActivityApiRqt);
    }
    /**
     * 商家支付页，返回待获取的优惠券列表id。
     *
     * @param getPayPageVoucherListApiRqt
     * @return
     */
    @Override
    @PostMapping("/getPayPageVoucherList")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public GetPayPageVoucherListApiResp getPayPageVoucherList(@Valid @RequestBody GetPayPageVoucherListApiRqt getPayPageVoucherListApiRqt) {
        return activityService.getPayPageVoucherList(getPayPageVoucherListApiRqt);
    }
    /**
     * 商家订单列表活动匹配标签
     */
    @Override
    @PostMapping("/matchUserOrderListTag")
    @FeignTimeout(connectTimeoutMillis = 1000,readTimeoutMillis = 1000)
    public MatchUserOrderListTagApiResp matchUserOrderListTag(@Valid @RequestBody MatchUserOrderListTagRqt matchUserOrderListTagRqt) {
        return activityService.matchUserOrderListTag(matchUserOrderListTagRqt);

    }

    /**
     * 商家订单详情
     */
    @Override
    @PostMapping("/matchOrderActivityDetail")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public MatchOrderActivityDetailApiResp matchOrderActivityDetail(@Valid @RequestBody MatchOrderActivityDetailApiRqt matchOrderActivityDetailRqt) {
        return activityService.matchOrderActivityDetail(matchOrderActivityDetailRqt);
    }

    @Override
    @UserLoginInfo
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public MerchantPublishOrderTagApiResp getPublishOrderTag(@Valid @RequestBody MerchantPublishOrderTagApiRqt merchantPublishOrderTagApiRqt) {
        return activityService.getPublishOrderTag(merchantPublishOrderTagApiRqt);
    }

    /**
     * 给大数据用
     * 判断指定实验是否有进行中的活动
     *
     * @param isUsedByTryIdRqt
     */
    @Override
    @PostMapping("/isUsedByTryId")
    public List<IsUsedByTryIdResp> isUsedByTryId(@Valid @RequestBody IsUsedByTryIdRqt isUsedByTryIdRqt) {
        return activityService.isUsedByTryId(isUsedByTryIdRqt);
    }


    /**
     * 获取是否有用户已报名的进行中且未达任何上限的充值活动
     * @param rqt
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/getUserRechargeActivity")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public GetUserRechargeActivityResp getUserRechargeActivity(@Valid @RequestBody GetUserRechargeActivityRqt rqt){
        return activityService.getUserRechargeActivity(rqt);
    }

    /**
     * 充值成功页获取用户的充值任务的奖励
     * @param rqt
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/getRechargeRewardList")
    public GetRechargeRewardResp getRechargeRewardList(@Valid @RequestBody GetRechargeRewardRqt rqt){
        return activityService.getRechargeRewardList(rqt);
    }


    /**
     * 钱包页面获取用户待领取的充值任务的多选一奖励
     * @param rqt
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/getRechargeChooseCombineRewardInfo")
    public RechargeChooseCombineResp getRechargeChooseCombineRewardInfo(@Valid @RequestBody RechargeChooseCombineRqt rqt){
        return activityService.getRechargeChooseCombineRewardInfo(rqt);
    }

    /**
     * 执行浏览指定页面任务
     * @param rqt
     * @return
     */
    @Override
    @UserLoginInfo
    @PostMapping("/runBrowsePageTask")
    public BrowsePageResp runBrowsePageTask(@Valid @RequestBody BrowsePageRqt rqt) {
        return activityService.runBrowsePageTask(rqt);
    }

    /**
     * 免费领券-领取优惠券
     */
    @Override
    @UserLoginInfo
    @PostMapping("/receiveVoucher")
    public ReceiveVoucherResp receiveVoucher(@Valid @RequestBody ReceiveVoucherRqt rqt,  @RequestHeader(value = "token")  String token) {
        return activityService.receiveVoucher(rqt, token);
    }

    /**
     * 获取家庭服务详情标签优惠券列表接口
     * @param rqt
     * @param token
     * @return
     */
    @Override
    @PostMapping("/getCustomerServeTagVoucherList")
    public List<GetCustomerServeTagVoucherListApiResp> getCustomerServeTagVoucherList(@Valid @RequestBody GetCustomerServeTagVoucherListApiRqt rqt){
        return activityService.getCustomerServeTagVoucherList(rqt);

    }


    /**
     * 领取优惠券V2(免费领券,会员权益领券)
     */
    @UserLoginInfo
    @PostMapping("/receiveVoucherV2")
    public ReceiveVoucherResp receiveVoucherV2(@Valid @RequestBody ReceiveVoucherV2Rqt rqt,  @RequestHeader(value = "token")  String token) {
        return activityService.receiveVoucherV2(rqt, token);
    }
}
