package com.wanshifu.controller;

import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocMasterActivityListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocReservationListApiReq;
import com.wanshifu.iop.inner.api.domains.response.MasterActivityListApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.ReservationListApiResp;
import com.wanshifu.service.TocMasterActivityService;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/3/5 9:26
 */
@RestController
@RequestMapping("toc/activity/master")
public class TocMasterActivityController {

    @Resource
    private TocMasterActivityService tocMasterActivityService;


    /**
     * 活动中心 活动列表
     */
    @Deprecated
    @UserLoginInfo
    @PostMapping("/list")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public MasterActivityListApiResp list(@Valid @RequestBody TocMasterActivityListApiRqt masterActivityListApiRqt) {
        return tocMasterActivityService.list(masterActivityListApiRqt);
    }

    /**
     * 待预约列表
     * */
    @UserLoginInfo
    @PostMapping("/reservationList")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public Map<String, ReservationListApiResp> reservationList(@Valid @RequestBody TocReservationListApiReq req){
        return tocMasterActivityService.reservationList(req);
    }




}
