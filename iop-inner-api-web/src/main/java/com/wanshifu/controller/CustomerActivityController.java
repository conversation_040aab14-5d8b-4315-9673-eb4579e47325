package com.wanshifu.controller;

import com.wanshifu.annotation.ShareExceptionHandle;
import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.activity.domain.api.response.out.GetSeckillTagResp;
import com.wanshifu.iop.inner.api.domains.request.customer.*;
import com.wanshifu.iop.inner.api.domains.response.customer.*;
import com.wanshifu.service.CustomerService;
import com.wanshifu.spring.cloud.fegin.ext.timeout.FeignTimeout;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: kexianyun
 * @time:2023/7/22 0022 11:18
 */
@RestController
@RequestMapping("customer")
public class CustomerActivityController {

    @Resource
    private CustomerService customerService;

    /**
     * 被邀请人落地页
     * */
    @PostMapping("/invitee/landingDetail")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public OrderRewardInviteeLandingPageApiResp inviteeLandingPage(@Valid @RequestBody OrderRewardInviteeLandingPageApiReq req,
                                                                   @RequestHeader(value = "token", required = false) String token) {
        return customerService.inviteeLandingPage(req, token);
    }

    /**
     * 邀请人落地页详情
     * */
    @PostMapping("/inviter/landingDetail")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public OrderRewardInviterLandingPageApiResp inviterLandingPage(@Valid @RequestBody OrderRewardInviterLandingPageApiReq req,
                                                                   @RequestHeader(value = "token", required = false) String token){
        return customerService.inviterLandingPage(req, token);
    }

    /**
     * 生成分享链接
     * */
    @UserLoginInfo
    @ShareExceptionHandle(note = "获取分享链接失败，请稍后重试")
    @PostMapping("/inviter/shareLink")
    @FeignTimeout(readTimeoutMillis = 1500,connectTimeoutMillis = 1500)
    public ShareLinkApiResp shareLink(@Valid @RequestBody ShareLinkApiReq req){
        return customerService.shareLink(req);
    }

    /**
     * 订单列表--家庭端有调用
     * */
    @UserLoginInfo
    @PostMapping("/inviter/orderList")
    @FeignTimeout(connectTimeoutMillis = 500,readTimeoutMillis = 500)
    public SimplePageInfo<OrderRewardInviterOrderListApiResp> orderList(@Valid @RequestBody OrderRewardInviterOrderListApiReq req){
        return customerService.orderList(req);
    }

    /**
     * 获取奖励数，待解锁奖励数--家庭端有调用
     */
    @UserLoginInfo
    @PostMapping("/inviter/rewardNum")
    @FeignTimeout(connectTimeoutMillis = 1000,readTimeoutMillis = 1000)
    public OrderRewardNumResp rewardNum(@Valid @RequestBody RewardNumReq req){
        return customerService.rewardNum(req);
    }

    /**
     * 获取秒杀活动标签信息
     * @param rqt
     * @return
     */
    @UserLoginInfo
    @PostMapping("/getSeckillTag")
    @FeignTimeout(connectTimeoutMillis = 1000,readTimeoutMillis = 1000)
    public GetSeckillTagResp getSeckillTag(@Valid @RequestBody GetSeckillTagInnerRqt rqt){
        return customerService.getSeckillTag(rqt);
    }
    /**
     * 获取家庭活动标签信息(目前只有秒杀以及拼团活动)
     * @param rqt
     * @return
     */
    @UserLoginInfo
    @PostMapping("/getCustomerActivityTag")
    @FeignTimeout(connectTimeoutMillis = 1000,readTimeoutMillis = 1000)
    public GetCustomerActivityTagApiResp getCustomerActivityTag(@Valid @RequestBody GetCustomerActivityTagInnerRqt rqt){
        return customerService.getCustomerActivityTag(rqt);
    }
    /**
     * 开团校验接口-商品详情页-拼团下单调用
     */
    @UserLoginInfo
    @PostMapping("/createTeamBuyCheckForOut")
    public CreateTeamBuyCheckOutApiResp createTeamBuyCheckForOut(@Valid @RequestBody CreateTeamBuyCheckOutApiRqt rqt){
        return customerService.createTeamBuyCheckForOut(rqt);
    }

}
