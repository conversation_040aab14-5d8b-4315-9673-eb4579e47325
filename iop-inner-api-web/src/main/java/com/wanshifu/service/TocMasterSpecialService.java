package com.wanshifu.service;

import com.wanshifu.iop.inner.api.domains.request.MasterAwardingNumApiRqt;
import com.wanshifu.iop.inner.api.domains.request.MasterRewardRemindApiRqt;
import com.wanshifu.iop.inner.api.domains.request.OfferInfoActivityListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.OfferInfoActivityApiReq;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocMasterAwardingNumApiRqt;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocMasterRewardRemindApiRqt;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocOfferInfoActivityApiReq;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocOfferInfoActivityListApiRqt;
import com.wanshifu.iop.inner.api.domains.response.MasterAwardingNumApiResp;
import com.wanshifu.iop.inner.api.domains.response.MasterOrderSummaryApiResp;
import com.wanshifu.iop.inner.api.domains.response.MasterRewardRemindApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.OfferOrderInfoApiResp;

/**
 * <AUTHOR>
 */
public interface TocMasterSpecialService {

    MasterRewardRemindApiResp rewardRemind(TocMasterRewardRemindApiRqt masterRewardRemindApiRqt);

    MasterAwardingNumApiResp awardingNum(TocMasterAwardingNumApiRqt token);

    /**
     * 订单报价详情活动列表
     */
    MasterOrderSummaryApiResp orderInfoReward(TocOfferInfoActivityListApiRqt offerInfoActivityListApiRqt) ;

    /**
     * 订单报价详情
     * */
    OfferOrderInfoApiResp orderInfoRewardTag(TocOfferInfoActivityApiReq req);
}
