package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.annotation.ExceptionHandle;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.constant.MasterOrderInfoActivityListConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.iop.activity.domain.api.request.master.MasterAwardingNumRqt;
import com.wanshifu.iop.activity.domain.api.request.master.MasterRewardRemindRqt;
import com.wanshifu.iop.activity.domain.api.request.master.OfferInfoActivityListRqt;
import com.wanshifu.iop.activity.domain.api.request.master.OfferInfoActivityOrderTaskListRqt;
import com.wanshifu.iop.activity.domain.api.response.master.*;
import com.wanshifu.iop.activity.domain.api.response.master.OfferInfoActivityOrderTaskListResp.ActivityReward;
import com.wanshifu.iop.activity.domain.bo.UserActivityTaskExecuteBatchBoV2;
import com.wanshifu.iop.activity.domain.enums.RewardGiveTypeEnum;
import com.wanshifu.iop.activity.domain.enums.RewardSymbol;
import com.wanshifu.iop.activity.domain.enums.RuleConfigSignEnum;
import com.wanshifu.iop.activity.domain.enums.TaskTypeEnum;
import com.wanshifu.iop.equity.domain.api.request.prizeWheelBusiness.ListRemainNumReq;
import com.wanshifu.iop.equity.domain.api.response.ListRemainNumResp;
import com.wanshifu.iop.inner.api.domains.enums.*;
import com.wanshifu.iop.inner.api.domains.request.MasterAwardingNumApiRqt;
import com.wanshifu.iop.inner.api.domains.request.MasterRewardRemindApiRqt;
import com.wanshifu.iop.inner.api.domains.request.OfferInfoActivityListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.OfferInfoActivityApiReq;
import com.wanshifu.iop.inner.api.domains.request.master.OrderInfoTemp;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocMasterAwardingNumApiRqt;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocMasterRewardRemindApiRqt;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocOfferInfoActivityApiReq;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.TocOfferInfoActivityListApiRqt;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.master.OfferOrderInfoApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.OrderOfferInfoListApiResp;
import com.wanshifu.iop.inner.api.domains.vo.resp.UserInfoWebDtoResp;
import com.wanshifu.master.innerapi.domains.request.ioc.CommonMasterIdRqt;
import com.wanshifu.master.innerapi.domains.response.ioc.WaitReceiveCountResp;
import com.wanshifu.master.innerapi.domains.response.ocs.BatchGetPushDistanceResp;
import com.wanshifu.master.innerapi.service.api.ioc.RewardApi;
import com.wanshifu.service.TocMasterSpecialService;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import com.wanshifu.util.ActivityUtil;
import com.wanshifu.util.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date Created in 2022/7/1 14:37
 */
@Service
@Slf4j
public class TocMasterSpecialServiceImpl extends AbstractService implements TocMasterSpecialService {
    /**
     * 师傅活动落地页地址
     */
    @Value("${wanshifu.master.landingPageUrl:www.wanshifu.com}")
    private String masterLandingPageUrl;
    @Resource
    private RewardApi rewardApi;

    @Override
    @ExceptionHandle(note = "获奖提醒弹窗接口异常")
    public MasterRewardRemindApiResp rewardRemind(TocMasterRewardRemindApiRqt masterRewardRemindApiRqt) {
        MasterRewardRemindRqt masterRewardRemindRqt = new MasterRewardRemindRqt();
        masterRewardRemindRqt.setGlobalOrderTraceId(masterRewardRemindApiRqt.getGlobalOrderTraceId());
        masterRewardRemindRqt.setUserId(masterRewardRemindApiRqt.getUserId());
        masterRewardRemindRqt.setUserClass(masterRewardRemindApiRqt.getUserClass());
        try {
            MasterRewardRemindResp resultResp = masterActivityBusinessServiceApi.rewardRemind(masterRewardRemindRqt);
            if (resultResp == null || resultResp.getActivityId() == null) {
                return new MasterRewardRemindApiResp();
            }
            MasterRewardRemindApiResp resp = new MasterRewardRemindApiResp();
            BeanUtils.copyProperties(resultResp, resp);
            if (resultResp.getExistLandingPage() != null && resultResp.getExistLandingPage() == 1) {
                resp.setLandingPageUrl(masterLandingPageUrl + "?activityId=" + resultResp.getActivityId());
            }
            return resp;
        } catch (ApiAccessException e) {
            log.error("调用平台活动服务异常：" + e.getMessage());
            return new MasterRewardRemindApiResp();
        }
    }

    @Override
    @ExceptionHandle(note = "师傅端活动中心待领奖数接口异常")
    public MasterAwardingNumApiResp awardingNum(TocMasterAwardingNumApiRqt masterAwardingNumApiRqt) {
        MasterAwardingNumRqt masterRewardRemindNumRqt = new MasterAwardingNumRqt();
        masterRewardRemindNumRqt.setUserId(masterAwardingNumApiRqt.getUserId());
        masterRewardRemindNumRqt.setUserClass(masterAwardingNumApiRqt.getUserClass());
        try {
            MasterAwardingNumApiResp resp = new MasterAwardingNumApiResp();

            //待领取的奖励数量（红包+积分）
            CommonMasterIdRqt commonMasterIdRqt = new CommonMasterIdRqt();
            commonMasterIdRqt.setMasterId(masterAwardingNumApiRqt.getUserId());
            WaitReceiveCountResp masterInteger= rewardApi.waitReceiveCount(commonMasterIdRqt);
            Integer masterSum = masterInteger.getBonusCount()+masterInteger.getIntegralCount();

            resp.setAwardingNum(masterSum);

            MasterAwardingNumResp resultResp = masterActivityBusinessServiceApi.activityCentreAwardingNum(masterRewardRemindNumRqt);
            if (resultResp == null) {
                return resp;
            }
            Integer awardingNum = resultResp.getAwardingNum();

            //移除抽奖机会已经到上限的库存
            if(awardingNum==0 || CollectionUtils.isEmpty(resultResp.getUserActivityTaskExecuteBatchBoV2List())){
                resp.setAwardingNum(masterSum);
                return resp;
            }
            List<Long> pIds = resultResp.getUserActivityTaskExecuteBatchBoV2List().stream().
                    filter(f -> (RewardSymbol.LOTTERY_TURNTABLE.code.equals(f.getRewardConfigId())) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).map(
                            UserActivityTaskExecuteBatchBoV2::getRewardExtraId).collect(
                            Collectors.toList());
            if(!CollectionUtils.isEmpty(pIds)){
                ListRemainNumReq listRemainNumReq = new ListRemainNumReq();
                listRemainNumReq.setPrizeWheelIds(pIds);
                List<ListRemainNumResp> resultRespList = prizeWheelBusinessServiceApi.listRemainNumByPrizeWheelIds(listRemainNumReq);
                List<Long> priIds = resultRespList.stream().map(ListRemainNumResp::getPrizeWheelId).collect(Collectors.toList());
                //筛选出 没有剩余库存的转盘信息
                if(!CollectionUtils.isEmpty(resultResp.getUserActivityTaskExecuteBatchBoV2List())){

                    Iterator<UserActivityTaskExecuteBatchBoV2> iter = resultResp.getUserActivityTaskExecuteBatchBoV2List().iterator();
                    while (iter.hasNext()) {
                        UserActivityTaskExecuteBatchBoV2 extraIdMap = iter.next();
                        if (RewardSymbol.LOTTERY_TURNTABLE.code.equals(extraIdMap.getRewardConfigId()) &&
                            !priIds.contains(extraIdMap.getRewardExtraId())) {
                            iter.remove();
                        }
                    }
                    awardingNum = resultResp.getUserActivityTaskExecuteBatchBoV2List().size();
                }
            }
            Integer sum = masterSum+awardingNum;
            resp.setAwardingNum(sum);
            return resp;
        } catch (Exception e) {
            log.error("调用平台活动服务异常：" + e.getMessage());
            return new MasterAwardingNumApiResp();
        }
    }

    /**
     * 组装orderInfoReward请求参数
     */
    private OfferInfoActivityListRqt returnOrderInfoRewardParm(TocOfferInfoActivityListApiRqt offerInfoActivityListApiRqt) {
        OfferInfoActivityListRqt offerInfoActivityListRqt = new OfferInfoActivityListRqt();
        offerInfoActivityListRqt.setBusinessLineId(offerInfoActivityListApiRqt.getBusinessLineId());
        offerInfoActivityListRqt.setCategoryId(offerInfoActivityListApiRqt.getCategoryId());
        offerInfoActivityListRqt.setCreateOrderTime(offerInfoActivityListApiRqt.getCreateOrderTime());
        offerInfoActivityListRqt.setFourthDivisionId(offerInfoActivityListApiRqt.getFourthDivisionId());
        offerInfoActivityListRqt.setThirdDivisionId(offerInfoActivityListApiRqt.getThirdDivisionId());
        offerInfoActivityListRqt.setFromAccountType(offerInfoActivityListApiRqt.getFromAccountType());
        offerInfoActivityListRqt.setGlobalOrderTraceId(offerInfoActivityListApiRqt.getGlobalOrderTraceId());
        offerInfoActivityListRqt.setGroupGoodNum(offerInfoActivityListApiRqt.getGroupGoodNum());
        offerInfoActivityListRqt.setOrderFrom(offerInfoActivityListApiRqt.getOrderFrom());
        offerInfoActivityListRqt.setOfferNum(offerInfoActivityListApiRqt.getOfferNum());
        offerInfoActivityListRqt.setOrderNo(offerInfoActivityListApiRqt.getOrderNo());
        offerInfoActivityListRqt.setPushOrderTime(offerInfoActivityListApiRqt.getPushOrderTime());
        offerInfoActivityListRqt.setServeType(offerInfoActivityListApiRqt.getServeType());
        offerInfoActivityListRqt.setServeTypeId(offerInfoActivityListApiRqt.getServeTypeId());
        offerInfoActivityListRqt.setServeLevel1Id(Long.valueOf(offerInfoActivityListApiRqt.getServeLevel1Ids()));
        return offerInfoActivityListRqt;
    }

    /**
     * 组装orderInfoReward请求参数
     */
    private OfferInfoActivityOrderTaskListRqt returnOrderInfoTagRewardParm(OrderInfoTemp offerInfoActivityListApiRqt,
            Long userId) {
        OfferInfoActivityOrderTaskListRqt offerInfoActivityListRqt = new OfferInfoActivityOrderTaskListRqt();
        offerInfoActivityListRqt.setBusinessLineId(offerInfoActivityListApiRqt.getBusinessLineId());
        offerInfoActivityListRqt.setCategoryId(offerInfoActivityListApiRqt.getCategoryId());
        offerInfoActivityListRqt.setCreateOrderTime(offerInfoActivityListApiRqt.getCreateOrderTime());
        offerInfoActivityListRqt.setFourthDivisionId(offerInfoActivityListApiRqt.getFourthDivisionId());
        offerInfoActivityListRqt.setThirdDivisionId(offerInfoActivityListApiRqt.getThirdDivisionId());
        offerInfoActivityListRqt.setFromAccountType(offerInfoActivityListApiRqt.getFromAccountType());
        offerInfoActivityListRqt.setGlobalOrderTraceId(offerInfoActivityListApiRqt.getGlobalOrderTraceId());
        offerInfoActivityListRqt.setGroupGoodNum(offerInfoActivityListApiRqt.getGroupGoodNum());
        offerInfoActivityListRqt.setOrderFrom(offerInfoActivityListApiRqt.getOrderFrom());
        offerInfoActivityListRqt.setOfferNum(offerInfoActivityListApiRqt.getOfferNum());
        offerInfoActivityListRqt.setOrderNo(offerInfoActivityListApiRqt.getOrderNo());
        offerInfoActivityListRqt.setPushOrderTime(offerInfoActivityListApiRqt.getPushOrderTime());
        offerInfoActivityListRqt.setServeType(offerInfoActivityListApiRqt.getServeType());
        offerInfoActivityListRqt.setServeTypeId(offerInfoActivityListApiRqt.getServeTypeId());
        offerInfoActivityListRqt.setServeLevel1Id(Long.valueOf(offerInfoActivityListApiRqt.getServeLevel1Ids()));
//        List<String> strList = new ArrayList<>();
//        if (offerInfoActivityListApiRqt.getIsOrderContract() == 1) {
//            strList.add("isOrderContract");
//        }
//        if (offerInfoActivityListApiRqt.getIsOrderPackage() == 1) {
//            strList.add("isOrderPackage");
//        }
//        if (offerInfoActivityListApiRqt.getIsOrderShop() == 1) {
//            strList.add(MasterOrderInfoActivityListConstant.ORDER_SHOP);
//        }
        offerInfoActivityListRqt.setOtherOrderFilterCondition(offerInfoActivityListApiRqt.getPropertyList());
        //获取订单师傅距离
        List<BatchGetPushDistanceResp> batchGetPushDistanceResps = this.getPushDistance(userId, Collections.singletonList(offerInfoActivityListApiRqt.getOrderId()));
        Integer distanceValue = 0;
        if(!CollectionUtils.isEmpty(batchGetPushDistanceResps)){
            distanceValue = batchGetPushDistanceResps.get(0).getPushDistance().intValue();
        }
        offerInfoActivityListRqt.setDistanceValue(distanceValue);
        //获取技能是否相关
        Map<String, Integer> skillRelatedStateMap = this.getSkillRelatedState(userId, Collections.singletonList(offerInfoActivityListApiRqt.getOrderId()));
        offerInfoActivityListRqt.setSkillRelatedState(skillRelatedStateMap.get(String.valueOf(offerInfoActivityListApiRqt.getOrderId())));
        return offerInfoActivityListRqt;
    }

    /**
     * 组装orderInfoReward返回结果
     */
    private MasterOrderOfferInfoListResp returnMasterOrderOfferInfoListResp(OfferInfoActivityListResp offerInfoActivityListResp) {
        MasterOrderOfferInfoListResp masterOrderOfferInfoListResp = new MasterOrderOfferInfoListResp();
        masterOrderOfferInfoListResp.setActivityId(offerInfoActivityListResp.getActivityId());
        masterOrderOfferInfoListResp.setActivityName(offerInfoActivityListResp.getActivityName());
        masterOrderOfferInfoListResp.setIsLanding(offerInfoActivityListResp.getIsLanding());
        masterOrderOfferInfoListResp.setActivityStartTime(offerInfoActivityListResp.getActivityStartTime());
        masterOrderOfferInfoListResp.setActivityEndTime(offerInfoActivityListResp.getActivityEndTime());
        return masterOrderOfferInfoListResp;
    }

    /**
     * 组装orderInfoReward返回结果
     */
    private OrderOfferInfoListApiResp returnMasterOrderOfferTagInfoListResp(OfferInfoActivityOrderTaskListResp offerInfoActivityListResp) {
        OrderOfferInfoListApiResp masterOrderOfferInfoListResp = new OrderOfferInfoListApiResp();
        masterOrderOfferInfoListResp.setActivityId(offerInfoActivityListResp.getActivityId());
        masterOrderOfferInfoListResp.setActivityName(offerInfoActivityListResp.getActivityName());
        masterOrderOfferInfoListResp.setIsLanding(offerInfoActivityListResp.getIsLanding());
        masterOrderOfferInfoListResp.setActivityStartTime(offerInfoActivityListResp.getActivityStartTime());
        masterOrderOfferInfoListResp.setActivityEndTime(offerInfoActivityListResp.getActivityEndTime());
        masterOrderOfferInfoListResp.setActivityRewardTimeText(String.format(ActivityConstant.ACTIVITY_REWARD_TIME_TEXT_END, DateUtils.formatDate(offerInfoActivityListResp.getRewardGiveEndTime(), DateUtils.YToMin)));
        return masterOrderOfferInfoListResp;
    }


    /**
     * 报价详情活动列表
     */
    @Override
    @ExceptionHandle(note = "报价详情活动列表接口异常")
    public MasterOrderSummaryApiResp orderInfoReward(TocOfferInfoActivityListApiRqt offerInfoActivityListApiRqt) {

        MasterOrderSummaryApiResp result = new MasterOrderSummaryApiResp();
        if(true){
            return result;
        }

        List<MasterOrderOfferInfoListResp> masterOrderOfferInfoListRespList = new ArrayList<>();

        OfferInfoActivityListRqt offerInfoActivityListRqt = this.returnOrderInfoRewardParm(offerInfoActivityListApiRqt);

        if (!StringUtils.isEmpty(offerInfoActivityListApiRqt.getServeIds())) {
            String[] strArr = offerInfoActivityListApiRqt.getServeIds().split(",");
            List<String> serverIdStrList = Arrays.asList(strArr);

            List<Long> serveIdList = new ArrayList<>();
            for (String s : serverIdStrList) {
                serveIdList.add(Long.valueOf(s));
            }
            offerInfoActivityListRqt.setServeIds(serveIdList);
        }
        offerInfoActivityListRqt.setAppointMethod(AppointMethodEnum.getTypeTxt(offerInfoActivityListApiRqt.getAppointType()));
        //下单用户设置
        offerInfoActivityListRqt.setUserId(offerInfoActivityListApiRqt.getCreateOrderUserId());
        //当前师傅账号
        offerInfoActivityListRqt.setMasterId(offerInfoActivityListApiRqt.getUserId());

        //获取人群信息
        offerInfoActivityListRqt.setUserCrowIds(super.getUserGroupIds(offerInfoActivityListApiRqt.getCreateOrderUserId().toString(), "1"));


        try {
            OfferInfoActivityListSummaryResp offerInfoActivityListRespList = masterActivityBusinessServiceApi.orderInfoReward(offerInfoActivityListRqt);

            if (offerInfoActivityListRespList.getList() != null) {
                for (OfferInfoActivityListResp offerInfoActivityListResp : offerInfoActivityListRespList.getList()) {
                    MasterOrderOfferInfoListResp masterOrderOfferInfoListResp = this.returnMasterOrderOfferInfoListResp(offerInfoActivityListResp);

                    masterOrderOfferInfoListResp.setIsLanding(offerInfoActivityListResp.getIsLanding());
                    if (masterOrderOfferInfoListResp.getIsLanding() == 0) {
                        //如果没有落地页就没有落地页地址
                        masterOrderOfferInfoListResp.setUrl("");
                    } else {
                        masterOrderOfferInfoListResp.setUrl(offerInfoActivityListResp.getActivityUrl() + "&t=" + offerInfoActivityListApiRqt.getIsWeb());
                    }

                    List<MasterOrderOfferInfoListResp.ActivityRewardItem> activityRewardItemList = new ArrayList<>();
                    for (OfferInfoActivityListResp.ActivityReward activityReward : offerInfoActivityListResp.getActivityTaskList()) {
                        MasterOrderOfferInfoListResp.ActivityRewardItem activityRewardItem = new MasterOrderOfferInfoListResp.ActivityRewardItem();

                        activityRewardItem.setTaskSymbol(activityReward.getTaskSymbol());
                        activityRewardItem.setTaskSymbolExt(MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()));
                        String num = "";
                        if (MasterRewardGiveTypeEnum.FIXED_VALUE.getType().equals(activityReward.getRewardGiveType())) {
                            num = MathUtils.convertByPattern(activityReward.getRewardGiveValue().toString());
                        }else if(MasterRewardGiveTypeEnum.RETURN_POINT.getType().equals(activityReward.getRewardGiveType())){
                            num = MathUtils.convertByPattern(activityReward.getRewardGiveValue().toString());
                        }else{
                            num = MathUtils.convertByPattern(activityReward.getRuleResultValue().toString());
                        }
                        activityRewardItem.setNum(num);

                        activityRewardItem.setActivityTaskId(activityReward.getActivityTaskId());
                        //内部使用
                        activityRewardItem.setIsShowGiveValue(activityReward.getIsShowGiveValue());

                        activityRewardItem.setRewardType(activityReward.getRewardType());
                        activityRewardItem.setReturnPointSource(activityReward.getReturnPointSource());

                        activityRewardItem.setRewardGiveType(activityReward.getRewardGiveType());
                        activityRewardItem.setRuleConfigSign(activityReward.getRuleConfigSign());
                        activityRewardItem.setRuleConfigUnit(activityReward.getRuleConfigUnit());
                        //剩余时间
                        activityRewardItem.setCount(this.returnMinLimit(activityReward, offerInfoActivityListResp.getActivityEndTime()));

                        if (activityRewardItem.getCount() != null && activityRewardItem.getCount() > 0) {
                            //报价详情页倒计时下的说明文字
                            activityRewardItem.setTaskShowExt("内" + MasterActivityTypeEnum.getActivityValue(activityRewardItem.getTaskSymbol()) + "，" + this.returnRewardTitle(
                                     activityReward).get(1));
                            activityRewardItem.setRewardInfoDesc(this.returnRewardInfoDesc(activityReward));
                            //奖励标题
                            activityRewardItem.setRewardInfoTitle("限时" + this.returnRewardTitle(
                                    activityReward).get(0));
                        } else {
                            //报价详情页倒计时下的说明文字
                            activityRewardItem.setTaskShowExt(this.returnRewardTitle( activityReward).get(1));
                            activityRewardItem.setRewardInfoDesc(this.returnRewardInfoDesc(activityReward));
                            //奖励标题
                            activityRewardItem.setRewardInfoTitle("限时" + this.returnRewardTitle( activityReward).get(0));
                        }

                        activityRewardItemList.add(activityRewardItem);
                    }
                    masterOrderOfferInfoListResp.setActivityRewardItemList(activityRewardItemList);
                    masterOrderOfferInfoListRespList.add(masterOrderOfferInfoListResp);
                }


                //实现排序
                masterOrderOfferInfoListRespList.sort(new Comparator<MasterOrderOfferInfoListResp>() {
                    @Override
                    public int compare(MasterOrderOfferInfoListResp u1, MasterOrderOfferInfoListResp u2) {
                        if (u1.getActivityId() > u2.getActivityId()) {
                            //return -1:即为正序排序
                            return 1;
                        } else if (u1.getActivityId().equals(u2.getActivityId())) {
                            return 0;
                        } else {
                            //return 1: 即为倒序排序
                            return -1;
                        }
                    }
                });

                List<MasterOrderOfferInfoListResp> masterOrderActivityList = new ArrayList<>();
                if (masterOrderOfferInfoListRespList.size() > 0) {
                    masterOrderActivityList.add(masterOrderOfferInfoListRespList.get(0));
                }
                result.setList(masterOrderActivityList);
            }

            GrabOrderButtonApiResp grabOrderButtonApiResp = new GrabOrderButtonApiResp();

            if (result.getList() == null) {
                result.setGrabOrderButtonApiResp(null);
            } else {
                MasterOrderOfferInfoListResp masterOrderOfferInfoListResp = result.getList().get(0);
                MasterOrderOfferInfoListResp.ActivityRewardItem activityRewardItem = masterOrderOfferInfoListResp.getActivityRewardItemList().get(0);
                if (activityRewardItem.getRewardType().equals(MasterRewardTypeEnum.BONUS.getActivityType())
                        && activityRewardItem.getIsShowGiveValue() == 1
                ) {
                    grabOrderButtonApiResp.setActivityId(masterOrderOfferInfoListResp.getActivityId());

                    grabOrderButtonApiResp.setActivityTaskId(activityRewardItem.getActivityTaskId());
                    grabOrderButtonApiResp.setNum(activityRewardItem.getNum());
                    if (MasterReturnPointSourceEnum.COMMISSION.getType().equals(activityRewardItem.getReturnPointSource())
                            || RuleConfigSignEnum.COMMISSIONAMOUNTRATE.getruleConfigSign().equals(activityRewardItem.getRuleConfigSign())
                    ) {
                        grabOrderButtonApiResp.setNumStr("");
                    } else {
                        grabOrderButtonApiResp.setNumStr(activityRewardItem.getNum());
                    }
                    grabOrderButtonApiResp.setNumType(activityRewardItem.getRewardGiveType());
                    grabOrderButtonApiResp.setAppointMethod(offerInfoActivityListRqt.getAppointMethod());
                    grabOrderButtonApiResp.setRewardType(activityRewardItem.getRewardType());
                    grabOrderButtonApiResp.setReturnPointSource(activityRewardItem.getReturnPointSource());
                    grabOrderButtonApiResp.setCount(activityRewardItem.getCount());

                    GrabOrderButtonApiResp grabOrderButtonApiResp1 = this.returnGrabOrderButtonApiResp(grabOrderButtonApiResp.getNumType(),activityRewardItem.getRuleConfigSign(),grabOrderButtonApiResp.getReturnPointSource(),activityRewardItem.getRuleConfigUnit(),false);
                    grabOrderButtonApiResp.setTagUnit(grabOrderButtonApiResp1.getTagUnit());
                    grabOrderButtonApiResp.setTagWord(grabOrderButtonApiResp1.getTagWord());

                    result.setGrabOrderButtonApiResp(grabOrderButtonApiResp);
                } else {
                    result.setGrabOrderButtonApiResp(null);
                }
            }
            return result;

        } catch (Exception e) {
            throw new BusException("order_info_reward_fail", e.getMessage());
        }

    }

    /**
     * 订单报价详情
     *
     * @param req
     */
    @Override
    public OfferOrderInfoApiResp orderInfoRewardTag(TocOfferInfoActivityApiReq req) {
        OfferOrderInfoApiResp result = new OfferOrderInfoApiResp();

        List<OrderOfferInfoListApiResp> masterOrderOfferInfoListRespList = new ArrayList<>();

        List<Long> orderIdList = new ArrayList<>();
        orderIdList.add(req.getOrderId());
        try {
            //获取订单信息
            List<OrderInfoTemp> orderInfoTemps = super.returnOrderBaseInfo(orderIdList,req.getUserId());
            if (CollectionUtils.isEmpty(orderInfoTemps)) {
                return result;
            }
            OrderInfoTemp offerInfoActivityListApiRqt = orderInfoTemps.get(0);


            OfferInfoActivityOrderTaskListRqt offerInfoActivityListRqt = this.returnOrderInfoTagRewardParm(offerInfoActivityListApiRqt,req.getUserId());

            if (!StringUtils.isEmpty(offerInfoActivityListApiRqt.getServeIds())) {
                List<Long> serveIdList  = com.wanshifu.framework.utils.StringUtils.splitCommaToList(offerInfoActivityListApiRqt.getServeIds()).stream().map(Long::valueOf).collect(Collectors.toList());
                offerInfoActivityListRqt.setServeIds(serveIdList);
            }
            offerInfoActivityListRqt.setAppointMethod(AppointMethodEnum.getTypeTxt(offerInfoActivityListApiRqt.getAppointType()));
            //下单用户设置
            offerInfoActivityListRqt.setUserId(offerInfoActivityListApiRqt.getCreateOrderUserId());
            //当前师傅账号
            offerInfoActivityListRqt.setMasterId(req.getUserId());

            //获取人群信息
            offerInfoActivityListRqt.setUserCrowIds(super.getUserGroupIds(offerInfoActivityListApiRqt.getCreateOrderUserId().toString(), "1"));

            log.info(JSON.toJSONString(offerInfoActivityListRqt));
            OfferInfoActivityListOrderTaskResp offerInfoActivityListRespList = masterActivityBusinessServiceApi.orderInfoRewardTag(offerInfoActivityListRqt);
            result.setCountdownTime(-1L);
            if (offerInfoActivityListRespList.getList() != null) {
                for (OfferInfoActivityOrderTaskListResp offerInfoActivityListResp : offerInfoActivityListRespList.getList()) {
                    OrderOfferInfoListApiResp masterOrderOfferInfoListResp = this.returnMasterOrderOfferTagInfoListResp(offerInfoActivityListResp);

                    masterOrderOfferInfoListResp.setIsLanding(offerInfoActivityListResp.getIsLanding());
                    if (masterOrderOfferInfoListResp.getIsLanding() == 0) {
                        //如果没有落地页就没有落地页地址
                        masterOrderOfferInfoListResp.setUrl("");
                    } else {
                        masterOrderOfferInfoListResp.setUrl(offerInfoActivityListResp.getActivityUrl() + "&t=" + req.getIsWeb());
                    }

                    List<OrderOfferInfoListApiResp.ActivityRewardItem> activityRewardItemList = new ArrayList<>();

                    for (ActivityReward activityReward : offerInfoActivityListResp.getActivityTaskList()) {
                        OrderOfferInfoListApiResp.ActivityRewardItem activityRewardItem = new OrderOfferInfoListApiResp.ActivityRewardItem();

                        activityRewardItem.setTaskSymbol(activityReward.getTaskSymbol());
                        activityRewardItem.setTaskSymbolExt(MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()));
                        activityRewardItem.setQuotedOrderTagList(activityReward.getQuotedOrderTagList());
                        activityRewardItem.setExtReward(ActivityUtil.filterTag(activityReward.getExtReward()));
                        String num = "";
                        if (MasterRewardGiveTypeEnum.FIXED_VALUE.getType().equals(activityReward.getRewardGiveType())) {
                            num = MathUtils.convertByPattern(activityReward.getRewardGiveValue().toString());
                        }else if(MasterRewardGiveTypeEnum.RETURN_POINT.getType().equals(activityReward.getRewardGiveType())){
                            num = MathUtils.convertByPattern(activityReward.getRewardGiveValue().toString());
                        }else{
                            num = MathUtils.convertByPattern(activityReward.getRuleResultValue().toString());
                        }
                        activityRewardItem.setNum(num);

                        activityRewardItem.setActivityTaskId(activityReward.getActivityTaskId());
                        //内部使用
                        activityRewardItem.setIsShowGiveValue(activityReward.getIsShowGiveValue());

                        activityRewardItem.setRewardType(activityReward.getRewardType());
                        activityRewardItem.setReturnPointSource(activityReward.getReturnPointSource());

                        activityRewardItem.setRewardGiveType(activityReward.getRewardGiveType());
                        activityRewardItem.setRuleConfigSign(activityReward.getRuleConfigSign());
                        activityRewardItem.setRuleConfigUnit(activityReward.getRuleConfigUnit());

                        OfferInfoActivityListResp.ActivityReward activityRewardTmp = new OfferInfoActivityListResp.ActivityReward();
                        BeanUtils.copyProperties(activityReward, activityRewardTmp);
                        //剩余时间
                        Long aLong = this.returnMinLimit(activityRewardTmp, offerInfoActivityListResp.getActivityEndTime());
                        activityRewardItem.setCount(aLong);
                        String taskSymBolText = MasterActivityTypeEnum.getActivityValue(activityRewardItem.getTaskSymbol());
                        if(MasterActivityTypeEnum.QUOTED.getType().equals(activityRewardItem.getTaskSymbol())){
                            taskSymBolText = "报价/抢单";
                        }
                        if (activityRewardItem.getCount() != null && activityRewardItem.getCount() > 0) {
                            //报价详情页倒计时下的说明文字
                            activityRewardItem.setTaskShowExt("内" + taskSymBolText + "，" + this.returnRewardTitle(activityRewardTmp).get(1));
                            activityRewardItem.setRewardInfoDesc(this.returnRewardInfoExt(activityRewardTmp)+this.returnRewardInfoDesc(activityRewardTmp));
                            //奖励标题
                            activityRewardItem.setRewardInfoTitle(this.returnRewardTitle(activityRewardTmp).get(0));
                        } else {
                            //报价详情页倒计时下的说明文字
                            activityRewardItem.setTaskShowExt(this.returnRewardTitle(activityRewardTmp).get(1));
                            activityRewardItem.setRewardInfoDesc(this.returnRewardInfoExt(activityRewardTmp)+this.returnRewardInfoDesc(activityRewardTmp));
                            //奖励标题
                            activityRewardItem.setRewardInfoTitle(this.returnRewardTitle(activityRewardTmp).get(0));
                        }

                        if(TaskTypeEnum.QUOTED.symbol.equals(activityRewardItem.getTaskSymbol())){
                            result.setActivityTitle(activityRewardItem.getTaskShowExt());
                            if(aLong>=0){
                                result.setCountdownTime(aLong);
                            }
                        }

                        activityRewardItemList.add(activityRewardItem);
                    }
                    masterOrderOfferInfoListResp.setActivityRewardItemList(activityRewardItemList);
                    masterOrderOfferInfoListRespList.add(masterOrderOfferInfoListResp);
                }


                //实现排序
                masterOrderOfferInfoListRespList.sort(new Comparator<OrderOfferInfoListApiResp>() {
                    @Override
                    public int compare(OrderOfferInfoListApiResp u1, OrderOfferInfoListApiResp u2) {
                        if (u1.getActivityId() > u2.getActivityId()) {
                            //return -1:即为正序排序
                            return 1;
                        } else if (u1.getActivityId().equals(u2.getActivityId())) {
                            return 0;
                        } else {
                            //return 1: 即为倒序排序
                            return -1;
                        }
                    }
                });

                List<OrderOfferInfoListApiResp> masterOrderActivityList = new ArrayList<>();
                if (masterOrderOfferInfoListRespList.size() > 0) {
                    masterOrderActivityList.addAll(masterOrderOfferInfoListRespList);
                }
                result.setActivityList(masterOrderActivityList);
            }

            GrabOrderButtonApiResp grabOrderButtonApiResp = new GrabOrderButtonApiResp();

            if (result.getActivityList() == null) {
                result.setButtonStyleInfo(null);
            } else {
                OrderOfferInfoListApiResp masterOrderOfferInfoListResp = null;
                OrderOfferInfoListApiResp.ActivityRewardItem activityRewardItem = null;
                for (OrderOfferInfoListApiResp orderOfferInfoListApiResp : result.getActivityList()) {
                    if(CollectionUtils.isEmpty(orderOfferInfoListApiResp.getActivityRewardItemList())){
                        continue;
                    }
                    Optional<OrderOfferInfoListApiResp.ActivityRewardItem> rewardItemOptional = orderOfferInfoListApiResp.getActivityRewardItemList().stream().filter(f -> TaskSymbolEnum.QUOTED.taskSymbol.equals(f.getTaskSymbol())).findFirst();
                    if(rewardItemOptional.isPresent()){
                        activityRewardItem = rewardItemOptional.get();
                        masterOrderOfferInfoListResp = orderOfferInfoListApiResp;
                        break;
                    }
                }
                result.setButtonStyleInfo(null);
                if(activityRewardItem!=null){
                    if (activityRewardItem.getRewardType().equals(MasterRewardTypeEnum.BONUS.getActivityType())
                            && activityRewardItem.getIsShowGiveValue() == 1
                    ) {
                        grabOrderButtonApiResp.setActivityId(masterOrderOfferInfoListResp.getActivityId());

                        grabOrderButtonApiResp.setActivityTaskId(activityRewardItem.getActivityTaskId());
                        grabOrderButtonApiResp.setNum(activityRewardItem.getNum());
                        if (
                            RuleConfigSignEnum.COMMISSIONAMOUNTRATE.getruleConfigSign().equals(activityRewardItem.getRuleConfigSign())
                        ) {
                            grabOrderButtonApiResp.setNumStr("");
                        } else {
                            grabOrderButtonApiResp.setNumStr(activityRewardItem.getNum());
                        }
                        grabOrderButtonApiResp.setNumType(activityRewardItem.getRewardGiveType());
                        grabOrderButtonApiResp.setAppointMethod(offerInfoActivityListRqt.getAppointMethod());
                        grabOrderButtonApiResp.setRewardType(activityRewardItem.getRewardType());
                        grabOrderButtonApiResp.setReturnPointSource(activityRewardItem.getReturnPointSource());
                        grabOrderButtonApiResp.setCount(activityRewardItem.getCount());

                        GrabOrderButtonApiResp grabOrderButtonApiResp1 = this.returnGrabOrderButtonApiResp(grabOrderButtonApiResp.getNumType(),activityRewardItem.getRuleConfigSign(), grabOrderButtonApiResp.getReturnPointSource(),activityRewardItem.getRuleConfigUnit(),true);
                        grabOrderButtonApiResp.setTagUnit(grabOrderButtonApiResp1.getTagUnit());
                        grabOrderButtonApiResp.setTagWord(grabOrderButtonApiResp1.getTagWord());

                        result.setButtonStyleInfo(grabOrderButtonApiResp);
                    } else {
                        result.setButtonStyleInfo(null);
                    }
                }
            }
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            throw new BusException("order_info_reward_fail", e.getMessage());
        }
    }

    /**
     * 返回奖励信息数据
     */
    public GrabOrderButtonApiResp returnGrabOrderButtonApiResp(String numType, String ruleConfigSign, String returnPointSource,String ruleConfigUnit,boolean isNew) {
        GrabOrderButtonApiResp grabOrderButtonApiResp = new GrabOrderButtonApiResp();
        if (
                RuleConfigSignEnum.COMMISSIONAMOUNTRATE.getruleConfigSign().equals(ruleConfigSign)) {
            grabOrderButtonApiResp.setTagWord("佣金全返");
            return grabOrderButtonApiResp;
        }


        if ( (!isNew && numType.equals(MasterRewardGiveTypeEnum.RETURN_POINT.getType())) ||
                (isNew && (numType.equals(MasterRewardGiveTypeEnum.REWARD_RULE_CONFIG.getType()) || MasterRewardGiveTypeEnum.RETURN_POINT.getType().equals(numType)))) {
            grabOrderButtonApiResp.setTagWord("额外奖");
            grabOrderButtonApiResp.setTagUnit(ruleConfigUnit+"现金");
            return grabOrderButtonApiResp;
        } else {
            grabOrderButtonApiResp.setTagWord("额外奖");
            grabOrderButtonApiResp.setTagUnit("元");
            return grabOrderButtonApiResp;
        }
    }

    /**
     * 返回限时最小值
     * [（订单下单时间+限时-当前时间）,（活动结束时间-当前时间）（设置为每日/每周/每月循环时，当天的时间-当前时间）]取最小值
     */
    public Long returnMinLimit(OfferInfoActivityListResp.ActivityReward activityReward, Date activityEndTime) {

        try {
            if (ObjectUtils.isEmpty(activityReward.getTimeLimit())) {
                return -1L;
            }

            //退单时间
            long pushOrderTime;
            if(activityReward.getPushOrderTime()!=null){
                pushOrderTime =  activityReward.getPushOrderTime().getTime() / 1000L;
            }else{
                pushOrderTime =  activityReward.getOrderCreateTime().getTime() / 1000L;
            }


            long nowDate = (new Date()).getTime() / 1000L;

            long l = pushOrderTime + activityReward.getTimeLimit() - nowDate;

            //活动结束时间-当前时间
            long activityNow = activityEndTime.getTime() / 1000L - nowDate;

            long max = Math.min(l, activityNow);
            if (StringUtils.isEmpty(activityReward.getExecuteTimeRangeEnd())) {
                if (max < 0) {
                    return -1L;
                }
                return max;
            } else {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String todayExecEndTime = simpleDateFormat.format(new Date()) + " " + activityReward.getExecuteTimeRangeEnd();
                SimpleDateFormat simpleDateFormatComplete = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                long time = simpleDateFormatComplete.parse(todayExecEndTime).getTime() / 1000L - nowDate;
                long min = Math.min(time, max);
                if (min < 0) {
                    return -1L;
                }
                return min;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusException("parse_fail", e.getMessage());
        }


    }


    /***
     * 奖励标题，展示
     **/
    public List<String> returnRewardTitle(OfferInfoActivityListResp.ActivityReward activityReward) {

        List<String> result = new ArrayList<>();

        //奖励节点
        String rewardNode = this.returnRewardNode(activityReward);
        String rewardText = null;
        if(MasterActivityTypeEnum.QUOTED.getType().equals(activityReward.getTaskSymbol())){
            rewardText = "额外奖";
        }else {
            rewardText = "奖励";
        }
        if (activityReward.getRewardType().equals(MasterRewardTypeEnum.BONUS.getActivityType())) {
            //红包
            //如果不展示金额
            if (activityReward.getIsShowGiveValue() == 0) {
                result.add(String.format(MasterOrderInfoActivityListConstant.REWARD_INFO,
                        MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()), "现金"));
                if(activityReward.getReturnPointSource().equals(MasterReturnPointSourceEnum.COMMISSION.getType())){
                    result.add(rewardNode + MasterReturnPointSourceEnum.COMMISSION.getTxt());
                }else{
                    result.add(rewardNode + "奖励现金红包");
                }

                return result;
            }

            if (activityReward.getRewardGiveType().equals(MasterRewardGiveTypeEnum.FIXED_VALUE.getType())) {
                //固定值
                result.add(String.format(MasterOrderInfoActivityListConstant.REWARD_INFO,
                        MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()), "现金：" + this.returnRewardFixedValue(activityReward) + "元"));
                result.add(rewardNode + rewardText + this.returnRewardFixedValue(activityReward) + "元");
                return result;

            } else if(activityReward.getRewardGiveType().equals(MasterRewardGiveTypeEnum.RETURN_POINT.getType())){
                //返点

                //佣金全返
                if (activityReward.getReturnPointSource().equals(MasterReturnPointSourceEnum.COMMISSION.getType())) {
                    result.add(String.format(MasterOrderInfoActivityListConstant.REWARD_INFO,
                            MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()), "现金：" + MasterReturnPointSourceEnum.COMMISSION.getTxt()));
                    //佣金全返
                    result.add(rewardNode + MasterReturnPointSourceEnum.COMMISSION.getTxt());
                    return result;
                }
                result.add(String.format(
                        MasterOrderInfoActivityListConstant.REWARD_INFO,
                        MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()),
                        "现金：" + activityReward.getRewardGiveValue().toString() + "%")
                );
                result.add(rewardNode + rewardText + activityReward.getRewardGiveValue().toString() + "%现金");

            }else{
                if(RuleConfigSignEnum.COMMISSIONAMOUNTRATE.getruleConfigSign().equals(activityReward.getRuleConfigSign())){
                    result.add(String.format(MasterOrderInfoActivityListConstant.REWARD_INFO,
                            MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()), "现金：" + MasterReturnPointSourceEnum.COMMISSION.getTxt()));
                    //佣金全返
                    result.add(rewardNode + MasterReturnPointSourceEnum.COMMISSION.getTxt());
                    return result;
                }else{
                    result.add(String.format(
                            MasterOrderInfoActivityListConstant.REWARD_INFO,
                            MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()),
                            "现金：" + activityReward.getRuleResultValue().toString() + activityReward.getRuleConfigUnit())
                    );
                    result.add(rewardNode + rewardText + activityReward.getRuleResultValue().toString()   + activityReward.getRuleConfigUnit()+"现金");

                }

            }
        } else {
            //积分
            //如果不展示积分
            if (activityReward.getIsShowGiveValue() == 0) {
                result.add(String.format(MasterOrderInfoActivityListConstant.REWARD_INFO,
                        MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()), "积分"));
                result.add(rewardNode + "奖励积分");
                return result;
            }

            if (activityReward.getRewardGiveType().equals(MasterRewardGiveTypeEnum.FIXED_VALUE.getType())) {
                //固定值
                result.add(String.format(MasterOrderInfoActivityListConstant.REWARD_INFO,
                        MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()), "积分：" + this.returnRewardFixedValue(activityReward)));
                result.add(rewardNode + "奖励" + this.returnRewardFixedValue(activityReward) + "积分");

            } else if(activityReward.getRewardGiveType().equals(MasterRewardGiveTypeEnum.RETURN_POINT.getType())){
                //按照成交金额比例返积分
                    result.add(String.format(MasterOrderInfoActivityListConstant.REWARD_INFO,
                            MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()), "积分：" + activityReward.getRewardGiveValue().toString() + "%"));
                    result.add(rewardNode + "奖励" + activityReward.getRewardGiveValue().toString() + "%积分");

                result.add(rewardNode + "奖励" + activityReward.getRewardGiveValue().toString()   +"%积分");

            }else{
                result.add(String.format(
                        MasterOrderInfoActivityListConstant.REWARD_INFO,
                        MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()),
                        "积分：" + activityReward.getRuleResultValue().toString() + activityReward.getRuleConfigUnit())
                );
                result.add(rewardNode + "奖励" + activityReward.getRuleResultValue().toString()   + activityReward.getRuleConfigUnit()+"积分");

            }
        }
        return result;

    }

    /**
     * 固定金额小数点后保留一位 积分保留整数
     * */
    public String returnRewardFixedValue(OfferInfoActivityListResp.ActivityReward activityReward){
        if (activityReward.getRewardGiveType().equals(RewardGiveTypeEnum.FIXED_VALUE.type)) {
            //判断是红包还是积分
            if(RewardSymbol.BONUS.type.equals(activityReward.getRewardType())){
               return activityReward.getRewardGiveValue()+"";
            }

            if(RewardSymbol.INTEGRAL.type.equals(activityReward.getRewardType())){
                return activityReward.getRewardGiveValue().setScale(0, RoundingMode.DOWN)+"";
            }
        }
        return "";
    }

    /**
     * 奖励信息
     */
    public String returnRewardInfoExt(OfferInfoActivityListResp.ActivityReward activityReward) {

        TaskTypeEnum taskTypeEnum = TaskTypeEnum.fromSymbol(activityReward.getTaskSymbol());
        switch (taskTypeEnum) {
            case RESERVE_CUSTOMER:
                //指派后n分钟内预约，
                return String.format(ActivityConstant.RESERVE_CUSTOMER_EXT_INFO,activityReward.getTimeLimit()/60);
            case SERVE_SIGN:
                //预约时间范围内签到，
                return ActivityConstant.SERVE_SIGN_EXT_INFO;
            case QUOTED:
                return ActivityConstant.QUOTED_EXT_INFO;
            default:
                return "";
        }
    }

    /**
     * 奖励信息
     */
    public String returnRewardInfoDesc(OfferInfoActivityListResp.ActivityReward activityReward) {
        switch (activityReward.getRewardGiveNode()) {
            case "task_order_give":
                //好评特殊处理
                if(TaskTypeEnum.MANUAL_RATE.symbol.equals(activityReward.getTaskSymbol())){
                    return ActivityConstant.showTitle9_desc;
                }
                return MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol()) + "后即发放";
            default:
                return MasterActivityRewardNodeEnum.getActivityValue(activityReward.getRewardGiveNode()) + "发放";
        }
    }

    /**
     * 发放节点
     */
    public String returnRewardNode(OfferInfoActivityListResp.ActivityReward activityReward) {
        switch (activityReward.getRewardGiveNode()) {
            case "task_order_give":
                return MasterActivityTypeEnum.getActivityValue(activityReward.getTaskSymbol());
            default:
                return MasterActivityRewardNodeEnum.getActivityValue(activityReward.getRewardGiveNode());
        }
    }


    @Deprecated
    public Long getUserIdByToken(String token, String userClass) {
        if (StringUtils.isEmpty(token)) {
            throw new BusException("请先登录");
        }
        String valueByKey = redisExUtil.getValueBykey(token, userClass);
        UserInfoWebDtoResp userInfo = JSONObject.parseObject(valueByKey, UserInfoWebDtoResp.class);
        return userInfo.getUserId();
    }
}
