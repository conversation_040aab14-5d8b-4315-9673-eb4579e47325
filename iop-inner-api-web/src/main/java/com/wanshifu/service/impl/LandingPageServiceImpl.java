package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.bo.NeedRewardAndCurrentPercentBo;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.constant.CacheKeyConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.MapUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.activity.domain.api.request.*;
import com.wanshifu.iop.activity.domain.api.request.combined.GetCombineRewardConfigRqt;
import com.wanshifu.iop.activity.domain.api.request.landingPage.*;
import com.wanshifu.iop.activity.domain.api.request.master.LotteryTurntableLandingPageDetailRqt;
import com.wanshifu.iop.activity.domain.api.request.reward.RewardGiveIdByActivityIdRqt;
import com.wanshifu.iop.activity.domain.api.response.*;
import com.wanshifu.iop.activity.domain.api.response.PageGetActivityDetailResp.ActivityTaskBo;
import com.wanshifu.iop.activity.domain.api.response.center.MerchantCenterRewardListSubResp;
import com.wanshifu.iop.activity.domain.api.response.landingPage.*;
import com.wanshifu.iop.activity.domain.api.response.landingPage.GetMasterActivityDetailAdResp.ActivityTaskInfo;
import com.wanshifu.iop.activity.domain.api.response.master.LotteryTurntableLandingPageDetailResp;
import com.wanshifu.iop.activity.domain.bo.*;
import com.wanshifu.iop.activity.domain.bo.CustomOrderTagBo;
import com.wanshifu.iop.activity.domain.enums.*;
import com.wanshifu.iop.activity.domain.po.ActivityBase;
import com.wanshifu.iop.activity.domain.po.ActivityBaseExt;
import com.wanshifu.iop.activity.domain.po.CombineRewardConfig;
import com.wanshifu.iop.activity.domain.po.UserActivityTask;
import com.wanshifu.iop.ad.domain.req.activity.ImportInviteeReq;
import com.wanshifu.iop.equity.domain.api.request.prizeWheelBase.GetPrizeWheelInfoReq;
import com.wanshifu.iop.equity.domain.api.request.prizeWheelBusiness.GetWinAwardItemByExtraIdRqt;
import com.wanshifu.iop.equity.domain.api.response.GetWinAwardItemByExtraIdResp;
import com.wanshifu.iop.equity.domain.api.response.PrizeWheelInfoResp;
import com.wanshifu.iop.equity.domain.enums.EquitySymbolEnum;
import com.wanshifu.iop.equity.service.api.PrizeWheelBusinessServiceApi;
import com.wanshifu.iop.inner.api.domains.bo.*;
import com.wanshifu.iop.inner.api.domains.bo.VoucherRewardBo;
import com.wanshifu.iop.inner.api.domains.bo.entity.TagTextBo;
import com.wanshifu.iop.inner.api.domains.enums.AccountTypeNameEnum;
import com.wanshifu.iop.inner.api.domains.enums.AppointMethodEnum;
import com.wanshifu.iop.inner.api.domains.enums.GoodsNumberNameEnum;
import com.wanshifu.iop.inner.api.domains.enums.TaskSymbolEnum;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.request.landingPage.*;
import com.wanshifu.iop.inner.api.domains.request.master.GetDetailAggregationBannerApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryBarrageListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryTurntableLandingPageDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.response.CustomOrderListTagApiResp;
import com.wanshifu.iop.inner.api.domains.response.CustomOrderListTagApiResp.MasterOrderListItem;
import com.wanshifu.iop.inner.api.domains.response.CustomOrderListTagApiResp.MasterOrderListItem.orderAddressInfo;
import com.wanshifu.iop.inner.api.domains.response.CustomOrderListTagApiResp.Technology;
import com.wanshifu.iop.inner.api.domains.response.GetUserAdResp;
import com.wanshifu.iop.inner.api.domains.response.GetUserAdResp.AdInfo;
import com.wanshifu.iop.inner.api.domains.response.OptionalRewardInfoVo;
import com.wanshifu.iop.inner.api.domains.response.RewardInfoVo;
import com.wanshifu.iop.inner.api.domains.response.bigdata.MstInviteActivityDataResp;
import com.wanshifu.iop.inner.api.domains.response.landingpage.*;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviteTaskCollectPageDetailApiResp.MasterInviteTaskApiActivity;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviteTaskCollectPageDetailApiResp.MasterInviteTaskApiActivity.ActivityInfo;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviteTaskCollectPageMyRwardInfoApiResp.RewardInfo;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviteeTaskOutDetailApiResp.ShowOtherRewardInfo;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviterTaskDetailApiResp.ActivityTaskList;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviterTaskDetailApiResp.InviteeLandingPageInfo;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviterTaskDetailApiResp.InviteeRewardInfo;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviterTaskDetailApiResp.OtherInviteActivity;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviterTaskMyRewardApiResp.InviteeUserActivityTaskInfo;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MasterInviterTaskMyRewardApiResp.InviteeUserActivityTaskInfo.InviteeUserActiviyTask;
import com.wanshifu.iop.inner.api.domains.response.landingpage.MerchantRechargeTaskApiResp.ActivityTaskList.TaskRewardResp;
import com.wanshifu.iop.inner.api.domains.response.master.GetDetailAggregationBannerApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.GetDetailAggregationBannerApiResp.ActivityInfo.ActivityTaskApiInfo;
import com.wanshifu.iop.inner.api.domains.response.master.GetDetailAggregationBannerApiResp.Meterial;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryBarrageListApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryTurntableLandingPageDetailApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.PrizeWheelInfoApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.PrizeWheelInfoApiResp.PrizeWheelAwardsApiVo;
import com.wanshifu.iop.inner.api.domains.vo.resp.TimeUnitVo;
import com.wanshifu.master.innerapi.domains.request.activity.ioc.GetFilterOrdersRqt;
import com.wanshifu.master.innerapi.domains.request.activity.ioc.GetWaitOfferRqt;
import com.wanshifu.master.innerapi.domains.request.ioc.AccountRegisterSubmitRqt;
import com.wanshifu.master.innerapi.domains.request.ioc.CheckPhoneRqt;
import com.wanshifu.master.innerapi.domains.request.masterInfo.GetMasterInfoRqtBean;
import com.wanshifu.master.innerapi.domains.request.masterInfo.GetTechnologyInfoRqt;
import com.wanshifu.master.innerapi.domains.response.activity.ioc.GetFilterOrdersResp;
import com.wanshifu.master.innerapi.domains.response.activity.ioc.GetWaitOfferResp;
import com.wanshifu.master.innerapi.domains.response.api.GetCurrentAgreementResp;
import com.wanshifu.master.innerapi.domains.response.ioc.AccountRegisterResultResp;
import com.wanshifu.master.innerapi.domains.response.ioc.GetAccountStatusResp;
import com.wanshifu.master.innerapi.domains.response.master.GetMasterBaseInfoBatchResp;
import com.wanshifu.master.innerapi.domains.response.master.GetMasterBaseInfoBatchRqt;
import com.wanshifu.master.innerapi.domains.response.master.GetMasterInfoRspBean;
import com.wanshifu.master.innerapi.domains.response.master.MasterHeadImgVo;
import com.wanshifu.master.innerapi.service.api.activity.IocActivityApi;
import com.wanshifu.master.innerapi.service.api.agreement.AgreementApi;
import com.wanshifu.master.innerapi.service.api.ioc.AccountApi;
import com.wanshifu.master.manage.config.domain.api.request.release.technique.QueryByServeIdsRqt;
import com.wanshifu.master.manage.config.domain.api.request.release.technique.TechniqueQueryRqt;
import com.wanshifu.master.manage.config.domain.api.response.release.technique.Technique;
import com.wanshifu.master.manage.config.service.api.release.TechniqueServiceApi;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.sdk.AccountServiceApi;
import com.wanshifu.service.ActivityService;
import com.wanshifu.service.LandingPageService;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import com.wanshifu.user.voucher.domain.api.request.BatchQueryTotalAmountByIdReq;
import com.wanshifu.user.voucher.domain.api.request.GetVoucherEventListReqBean;
import com.wanshifu.user.voucher.domain.api.response.BatchQueryTotalAmountByIdResp;
import com.wanshifu.user.voucher.domain.po.VoucherEvent;
import com.wanshifu.util.ActivityUtil;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.MathUtils;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * @author:<EMAIL>
 * @create:2022-08-24 10:11:25
 * @Description ：
 **/
@Service("landingPageService")
@Slf4j
public class LandingPageServiceImpl extends AbstractService implements LandingPageService {

    @Resource
    public IocActivityApi iocActivityApi;
    /**
     * 师傅技能管理
     */
    @Resource
    public TechniqueServiceApi techniqueServiceApi;


    @Resource
    public ServeServiceApi serveServiceApi;

    /**
     * 权益服务--业务外部接口类
     */
    @Resource
    public PrizeWheelBusinessServiceApi prizeWheelBusinessServiceApi;
    @Resource
    public AddressApi addressApi;
    @Resource
    public AgreementApi agreementApi;
    @Resource
    public AccountApi accountApi;
    @Resource
    public AccountServiceApi accountServiceApi;
    @Resource
    public ActivityService activityService;
    @Resource
    private HttpServletRequest httpServletRequest;

    @Value("${wanshifu.common-bigdata-openapi.iop.appcode}")
    public String appcode;
    /**
     * 图片url地址
     */
    @Value("${wanshifu.image-service.url}")
    private String imageUrl;
    /**
     * 筛选订单列表，批量查询数量
     */
    @Value("${wanshifu.master-inner.filterOrders.queryNumber}")
    private Integer queryNumber;
    /**
     * 筛选订单列表，返回给前端数量
     */
    @Value("${wanshifu.master-inner.filterOrders.returnNumber}")
    private Integer returnNumber;
    /**
     * 换一批时间
     */
    @Value("${wanshifu.master-inner.filterOrders.switchTime}")
    private Integer switchTime;
    //优惠券/券包缓存时间
    @Value("${wanshifu.voucherReward.redisCacheTime:10}")
    private Integer voucherCacheTime;
    /**
     * 是否允许app访问
     */
    @Value("${wanshifu.app.enable}")
    private Boolean appEnable;

    /***
     * @Description：获取师傅有奖任务活动落地页详情
     * @param: landingPageRqt
     * @return：
     */
    @Override
    public MasterPrizeTaskApiResp getPrizeTasklandingPage(LandingPageRqt landingPageRqt) {
        //是否登陆
        Long userId = null;
        if (StringUtils.isNotEmpty(landingPageRqt.getSignature())) {
            userId = this.getMasterIdBySignature(landingPageRqt.getSignature());
        }
        boolean isLogin = (userId != null);
        //实例化活动详情
        MasterPrizeTaskApiResp masterPrizeTaskApiResp = new MasterPrizeTaskApiResp();
        masterPrizeTaskApiResp.setLogin(isLogin);
        PageGetActivityDetailResp activityDetail = null;
        Integer isShowTip = 1;
        if (isLogin) {
            //判断用户登陆状态、
            PageGetActivityDetailRqt getActivityDetailRqt = new PageGetActivityDetailRqt();
            BeanUtils.copyProperties(landingPageRqt, getActivityDetailRqt);
            getActivityDetailRqt.setIsShowTip(isShowTip);
            getActivityDetailRqt.setUserId(userId);
            try {
                activityDetail = landingPageServiceApi.getActivityDetail(getActivityDetailRqt);
            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
            }

        } else {
            PageGetActivityDetailRqt getActivityDetailNoLoginRqt = new PageGetActivityDetailRqt();
            BeanUtils.copyProperties(landingPageRqt, getActivityDetailNoLoginRqt);
            try {
                activityDetail = landingPageServiceApi.getActivityDetailNoLogin(
                        getActivityDetailNoLoginRqt);
            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());

            }
        }
        try {
            //基础信息
            MasterPrizeTaskApiResp.ActivityBaseInfo activityBaseInfo = new MasterPrizeTaskApiResp.ActivityBaseInfo();
            PageGetActivityDetailResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");
            //待领奖数，如果自动领奖，待领奖数是0
            if (OperateMethodEnum.AUTO.code.equals(
                    activityDetail.getActivityBaseInfo().getRewardGiveMethod())) {
                masterPrizeTaskApiResp.setAwarding(0);
            } else {
                masterPrizeTaskApiResp.setAwarding(activityDetail.getAwarding());
            }

            masterPrizeTaskApiResp.setHasAwarding(activityDetail.getHasAwarding());

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));
            //自动报名
            if (OperateMethodEnum.AUTO.code.equals(activityBaseInfo.getApplyMethod())) {
                activityBaseInfo.setApplyStartTime(activityBaseInfo.getLaunchStartTime());
                activityBaseInfo.setApplyEndTime(activityBaseInfo.getActivityEndTime());
            }
            activityBaseInfo.setApplyCondition(activityDetail.getActivityBaseExt().getApplyCondition());
            masterPrizeTaskApiResp.setActivityBaseInfo(activityBaseInfo);

            //落地页配置信息
            MasterPrizeTaskApiResp.LandingPageInfo landingPageInfo = new MasterPrizeTaskApiResp.LandingPageInfo();
            PageGetActivityDetailResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);

            if (StringUtils.isNotEmpty(landingPageInfo.getNoQualificationsTips()) && isLogin) {
                masterPrizeTaskApiResp.setLandingPageInfo(landingPageInfo);
                return masterPrizeTaskApiResp;
            } else {
                landingPageInfo.setNoQualificationsTips(null);
            }
            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = activityDetail.getActivityTaskLists().stream()
                    .map(PageGetActivityDetailResp.ActivityTaskBo::getRewardImageAid).filter(
                            Objects::nonNull).collect(Collectors.toList());

            if (!StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {
                //兼容历史数据处理
                if (landingPageInfo1.getActivityModelDetail().contains(ActivityConstant.AID)) {
                    //解析图片
                    List<ActivityModelDetailResp> detailList = JSONArray.parseArray(
                            landingPageInfo1.getActivityModelDetail(),
                            ActivityModelDetailResp.class);

                    for (ActivityModelDetailResp activityModelDetailResp : detailList) {
                        imageAidList.add(activityModelDetailResp.getAid());
                    }
                } else {
                    //解析图片
                    List<String> detailList = JSONArray.parseArray(
                            landingPageInfo1.getActivityModelDetail(),
                            String.class);

                    for (String aid : detailList) {
                        imageAidList.add(Long.valueOf(aid));
                    }
                }

            }

            Collections.addAll(imageAidList,
                    landingPageInfo1.getPreviewImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getTopImageAid(),
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getRewardImageAid()
            );

            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());
            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setPreviewImageUrl(
                    imageResultMap.get(landingPageInfo1.getPreviewImageAid()));
            landingPageInfo.setBackgroundImageUrl(
                    imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));
            landingPageInfo.setTopImageUrl(
                    imageResultMap.get(landingPageInfo1.getAppTopImageAid()));

            landingPageInfo.setRewardTimageUrl(
                    imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            if (!StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {
                //兼容历史数据处理
                if (landingPageInfo1.getActivityModelDetail().contains(ActivityConstant.AID)) {
                    List<ActivityModelDetailResp> detailList = JSONArray.parseArray(
                            landingPageInfo1.getActivityModelDetail(), ActivityModelDetailResp.class);
                    for (ActivityModelDetailResp activityModelDetailResp : detailList) {
                        activityModelDetailResp.setUrl(imageResultMap.getOrDefault(activityModelDetailResp.getAid(), ""));
                    }
                    landingPageInfo.setActivityModelDetail(detailList);
                } else {
                    List<Long> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), Long.class);
                    Map<Long, String> finalImageResultMap = imageResultMap;
                    List<ActivityModelDetailResp> detaiRespList = detailList.stream().map(aid -> {
                        ActivityModelDetailResp respVo = new ActivityModelDetailResp();
                        respVo.setUrl(finalImageResultMap.getOrDefault(aid, ""));
                        respVo.setAid(aid);
                        return respVo;
                    }).collect(Collectors.toList());
                    landingPageInfo.setActivityModelDetail(detaiRespList);
                }
            }


            masterPrizeTaskApiResp.setLandingPageInfo(landingPageInfo);
            //实例化任务列表
            List<MasterPrizeTaskApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();

            //任务列表
            for (PageGetActivityDetailResp.ActivityTaskBo activityTaskList : activityDetail.getActivityTaskLists()) {
                MasterPrizeTaskApiResp.ActivityTaskList activityTaskListTmp = new MasterPrizeTaskApiResp.ActivityTaskList();

                if (activityTaskList.getRewardImageAid() != null) {
                    activityTaskListTmp.setRewardImageUrl(
                            imageResultMap.get(activityTaskList.getRewardImageAid()));
                }
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);
                activityTaskListTmp.setRewardGiveValue(activityTaskListTmp.getRewardGiveValue());
                activityTaskListTmp.setRewardType(activityTaskList.getRewardConfigId());

                if (activityTaskList.getUserActivityTask() != null) {
                    activityTaskListTmp.setUserActivityTaskId(
                            activityTaskList.getUserActivityTask().getUserActivityTaskId());
                }
                //设置奖励文案
                TagTextBo tagTextBo = this.splitTagText(activityTaskList.getTagText());
                if (tagTextBo != null) {
                    activityTaskListTmp.setCustomizeTargetText(tagTextBo.getCustomizeTargetText());
                }
                //设置任务文案 xx内完成1单
                if (isLogin) {
                    activityTaskListTmp.setTitle(this.getTaskTitle(activityTaskList.getTaskSymbol(),
                            activityTaskList.getUserActivityTask(), activityTaskListTmp.getCustomizeTargetText(), activityTaskListTmp.getTargetQuantityValue()));
                } else {
                    activityTaskListTmp.setTitle(
                            this.getTaskTitleNoLogin(activityTaskList.getTaskSymbol(), UserTypeEnum.MASTER.type));
                }

                //文案设定--------------------
                //执行频率
                activityTaskListTmp.setTitleDesc(
                        this.getTaskTitleDesc(activityTaskList.getActivityTaskInfo()));
                //已领次数
                activityTaskListTmp.setRewardNumText(this.getRewardNumText(activityTaskList));
                //还差次数
                activityTaskListTmp.setNeedRewardNumText(
                        this.getNeedRewardNumText(activityTaskList));

                if (activityBaseInfo.getIsActivityStart() == 1) {
                    if (activityTaskList.getButtonStatus() == 3
                            || activityTaskList.getButtonStatus() == 0) {
                        activityTaskListTmp.setSecondsRemain(0L);
                    } else {
                        activityTaskListTmp.setSecondsRemain(
                                this.secondsRemain(masterPrizeTaskApiResp.getActivityBaseInfo().getActivityEndTime(),
                                        activityTaskList.getTimeLimit().longValue(),
                                        activityTaskList.getApplyTime()));
                    }
                } else {
                    activityTaskListTmp.setSecondsRemain(0L);
                }

                //没报名倒计时返回0
                if (activityBaseInfo.getIsApply() == 0) {
                    activityTaskListTmp.setSecondsRemain(0L);
                }
                activityTaskListTmp.setIsShowOrderLimit(TaskSymbolEnum.isShowOrderLimit(activityTaskListTmp.getTaskSymbol()));

                if (!StringUtils.isEmpty(activityTaskListTmp.getTargetAmountValueRange())) {
                    activityTaskListTmp.setTargetAmountValueRange(
                            activityTaskListTmp.getTargetAmountValueRange().replace("[", "")
                                    .replace("]", ""));
                }

                activityTaskLists.add(activityTaskListTmp);
            }
            //任务列表
            masterPrizeTaskApiResp.setActivityTaskLists(activityTaskLists);


            MasterPrizeTaskApiResp.ActivityBaseInfo activityBaseInfo2 = masterPrizeTaskApiResp.getActivityBaseInfo();
            masterPrizeTaskApiResp.setActivityBaseInfo(activityBaseInfo2);
            masterPrizeTaskApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);
            return masterPrizeTaskApiResp;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("get_activity_detail_fail", e.getMessage());
        }
    }

    /***
     * @Description：还差多次文案
     * @param: activityTaskInfo
     * @return：
     * @param activityTaskInfo
     */
    private String getNeedRewardNumText(ActivityTaskBo activityTaskInfo) {
        if (!ActivityButtonStateEnum.NOT_SHOW.code.equals(activityTaskInfo.getNeedRewardNum())) {
            return "还差" + activityTaskInfo.getNeedRewardNum() + "次";
        }
        return null;
    }

    /***
     * @Description：还差多次文案
     * @param: activityTaskInfo
     * @return：
     * @param needRewardNum
     */
    private String getNeedRewardNumTextV2(Integer needRewardNum) {
        if (!ActivityButtonStateEnum.NOT_SHOW.code.equals(needRewardNum)) {
            return "还差" + needRewardNum + "次";
        }
        return null;
    }

    /***
     * @Description：获取已领次数文案
     * @param: activityTaskInfo
     * @return：
     * @param activityTaskInfo
     */
    private String getRewardNumText(ActivityTaskBo activityTaskInfo) {
        ActivityTaskBeanBo activityTask = activityTaskInfo.getActivityTaskInfo();
        Integer rewardNum = activityTaskInfo.getRewardNum();
        boolean isOverOne = getRewardSet(activityTask);
        boolean isRewardNum = rewardNum == null || 0 == rewardNum;
        if (!isOverOne || isRewardNum) {
            return null;
        }
        //任务执行类型[single:单次,cycle:持续循环,day:每天,week:每周,month:每月]
        switch (activityTask.getExecuteType()) {
            case "single":
            case "day":
                return "今日已领" + rewardNum + "次";
            case "cycle":
                return "已领" + rewardNum + "次";
            case "week":
                return "本周已领" + rewardNum + "次";
            case "month":
                return "本月已领" + rewardNum + "次";
            default:
                return null;
        }

    }

    /***
     * @Description：获取已领次数文案
     * @param: activityTaskInfo
     * @return：
     * @param activityTaskInfo
     */
    private String getRewardNumTextV2(LotteryTurntableLandingPageDetailResp.ActivityTaskBo activityTaskInfo) {
        ActivityTaskBeanBo activityTask = activityTaskInfo.getActivityTaskInfo();
        Integer rewardNum = activityTaskInfo.getRewardNum();
        boolean isOverOne = getRewardSet(activityTask);
        boolean isRewardNum = rewardNum == null || 0 == rewardNum;
        if (!isOverOne || isRewardNum) {
            return null;
        }
        //任务执行类型[single:单次,cycle:持续循环,day:每天,week:每周,month:每月]
        switch (activityTask.getExecuteType()) {
            case "single":
            case "day":
                return "今日已领" + rewardNum + "次";
            case "cycle":
                return "已领" + rewardNum + "次";
            case "week":
                return "本周已领" + rewardNum + "次";
            case "month":
                return "本月已领" + rewardNum + "次";
            default:
                return null;
        }

    }

    /***
     * @Description：判断个人奖励上限设置是否大于1
     * @param: activityTask
     * @return：
     */
    private boolean getRewardSet(ActivityTaskBeanBo activityTask) {
        switch (activityTask.getExecuteType()) {
            case "single":
            case "cycle":
                return activityTask.getRewardUserUpperLimit() > 1;
            case "day":
                return activityTask.getRewardUserDayUpperLimit() > 1;
            case "week":
                return activityTask.getRewardUserDayUpperLimit() > 1
                        || activityTask.getRewardUserWeekUpperLimit() > 1;
            case "month":
                return activityTask.getRewardUserDayUpperLimit() > 1
                        || activityTask.getRewardUserMonthUpperLimit() > 1;
            default:
                return false;
        }

    }

    private List<AddressIopResp> createTaskCityName(String threeDivisionIds) {
        List<String> threeDivisionIdsList = JSONArray.parseArray(threeDivisionIds, String.class);
        String threeDivisionIdsStr = com.wanshifu.framework.utils.StringUtils.listToCommaSplit(
                threeDivisionIdsList);
        //地址转换
        List<Address> addressList = addressApi.getDivisionInfoListByDivisionIdsPost(
                threeDivisionIdsStr);
        if (CollectionUtils.isEmpty(addressList)) {
            log.warn(
                    "com.wanshifu.service.impl.LandingPageServiceImpl.createTaskCityName 没有查询到对应的地址信息，threeDivisionIdsStr={}",
                    threeDivisionIdsStr);
            return null;
        }
        //二级---市分组----实际上对应数据库中3|4级
        Map<Long, List<Address>> parentGroupMap = addressList.stream()
                .collect(Collectors.groupingBy(g -> g.getParentId()));
        List<AddressIopResp> secondList = new ArrayList<>();
        parentGroupMap.entrySet().stream().forEach(parentEntry -> {
            AddressIopResp secondResp = new AddressIopResp();
            secondResp.setLevel(2);
            secondResp.setId(parentEntry.getKey());
            List<Address> thirdList = parentEntry.getValue();
            secondResp.setLv2DivisionId(thirdList.get(0).getLv2DivisionId());
            secondResp.setLv2DivisionName(thirdList.get(0).getLv2DivisionName());
            //遍历查询二级市名:三级没匹配到用四级的名称
            String secondName =
                    thirdList.get(0).getLv3DivisionId().equals(parentEntry.getKey())
                            ? thirdList.get(0)
                            .getLv3DivisionName() : thirdList.get(0).getLv4DivisionName();
            secondResp.setName(secondName);
            //三级------区或者镇
            List<AddressIopResp> thirdRespList = new ArrayList<>();
            thirdList.stream().forEach(vo -> {
                AddressIopResp thirdResp = new AddressIopResp();
                thirdResp.setLevel(3);
                thirdResp.setId(vo.getDivisionId());
                thirdResp.setName(vo.getDivisionName());
                thirdRespList.add(thirdResp);
            });
            secondResp.setChildren(thirdRespList);
            secondList.add(secondResp);
        });
        //一级----------省分组
        Map<Long, List<AddressIopResp>> provinceGroupMap = secondList.stream()
                .collect(Collectors.groupingBy(g -> g.getLv2DivisionId()));
        List<AddressIopResp> respList = new ArrayList<>();
        provinceGroupMap.entrySet().stream().forEach(entrySet -> {
            AddressIopResp provinceResp = new AddressIopResp();
            provinceResp.setLevel(1);
            provinceResp.setId(entrySet.getKey());
            List<AddressIopResp> childList = entrySet.getValue();
            provinceResp.setName(childList.get(0).getLv2DivisionName());
            provinceResp.setChildren(childList);
            respList.add(provinceResp);
        });
        return respList;
    }

    /**
     * 在原来三级基础上增加4级，但4级仍然放在三级框里展示：区，区-镇
     *
     * @return
     */
    private List<AddressIopResp> newCreateTaskCityName(ActivityTaskBo.TaskOrderLimit taskOrderLimit) {
        if (StringUtils.isEmpty(taskOrderLimit.getFirstProvinceIds())) {
            return null;
        }
        List<String> divisionIdsList = new ArrayList<>();
        List<Long> firstList = JSONArray.parseArray(taskOrderLimit.getFirstProvinceIds(), Long.class);
        List<Long> secondList = StringUtils.isNotEmpty(taskOrderLimit.getSecondCityIds()) ? JSONArray.parseArray(taskOrderLimit.getSecondCityIds(), Long.class) : new ArrayList<>();
        List<Long> thirdList = StringUtils.isNotEmpty(taskOrderLimit.getThirdDistrictIds()) ? JSONArray.parseArray(taskOrderLimit.getThirdDistrictIds(), Long.class) : new ArrayList<>();
        List<Long> fourthList = StringUtils.isNotEmpty(taskOrderLimit.getFourthStreetIds()) ? JSONArray.parseArray(taskOrderLimit.getFourthStreetIds(), Long.class) : new ArrayList<>();
        List<Long> selectAllList = StringUtils.isNotEmpty(taskOrderLimit.getSelectAllAreaIds()) ? JSONArray.parseArray(taskOrderLimit.getSelectAllAreaIds(), Long.class) : new ArrayList<>();
        divisionIdsList.addAll(firstList.stream().map(m -> m.toString()).collect(Collectors.toList()));
        divisionIdsList.addAll(secondList.stream().map(m -> m.toString()).collect(Collectors.toList()));
        //处理查询id太长问题，url太长会报错,而且效率很低，太长三级去redis缓存中查询
        List<Address> threeAndfourAddressList = new ArrayList<>();
        if (divisionIdsList.size() + thirdList.size() + fourthList.size() > 400) {
            List<Long> queryAddressList = new ArrayList<>();
            queryAddressList.addAll(thirdList);
            queryAddressList.addAll(fourthList);
            threeAndfourAddressList = this.getThreeAndFourAddressListFromRedis(queryAddressList);
        } else {
            divisionIdsList.addAll(thirdList.stream().map(m -> m.toString()).collect(Collectors.toList()));
            divisionIdsList.addAll(fourthList.stream().map(m -> m.toString()).collect(Collectors.toList()));
        }
        String threeDivisionIdsStr = com.wanshifu.framework.utils.StringUtils.listToCommaSplit(divisionIdsList);
        //查询地址信息
        List<Address> addressList = null;
        try {
            addressList = addressApi.getDivisionInfoListByDivisionIdsPost(threeDivisionIdsStr);
        } catch (Exception e) {
            log.error("查询getDivisionInfoListByDivisionIdsPost地址接口异常：" + e.getMessage());
        }
        if (CollectionUtils.isEmpty(addressList)) {
            log.warn("com.wanshifu.service.impl.LandingPageServiceImpl.createTaskCityName 没有查询到对应的地址信息，threeDivisionIdsStr={}", threeDivisionIdsStr);
            return null;
        }
        addressList.addAll(threeAndfourAddressList);
        Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getDivisionId, address -> address, (v1, v2) -> v1));

        List<AddressIopResp> respList = new ArrayList<>();
        Map<Long, List<Address>> parentGroupMap = addressList.stream().collect(Collectors.groupingBy(g -> g.getParentId()));
        List<AddressIopResp> secondRespList = new ArrayList<>();
        secondList.forEach(id -> {
            AddressIopResp secondResp = new AddressIopResp();
            secondResp.setId(id);
            secondResp.setLevel(2);
            secondResp.setName(addressMap.get(id).getDivisionName());
            //4级和3级合并名称设置：区，**区-镇1,**区-镇2
            List<Address> thirdDevisionList = parentGroupMap.get(id);
            if (CollectionUtils.isEmpty(thirdDevisionList)) {
                secondRespList.add(secondResp);
                return;
            }
            List<AddressIopResp> thirdRespList = new ArrayList<>();
            for (Address addressVo : thirdDevisionList) {
                AddressIopResp thirdResp = new AddressIopResp();
                thirdResp.setLevel(3);
                String name = "";
                if ((selectAllList.size() > 0 && selectAllList.contains(addressVo.getDivisionId())) || CollectionUtils.isEmpty(parentGroupMap.get(addressVo.getDivisionId()))) {
                    name = addressVo.getDivisionName();
                } else {
                    StringBuilder builder = new StringBuilder("");
                    List<Address> fourthChildList = parentGroupMap.get(addressVo.getDivisionId());
                    for (int i = 0; i < fourthChildList.size(); i++) {
                        if (i == 0) {
                            builder.append(addressVo.getDivisionName()).append("-").append(fourthChildList.get(i).getDivisionName());
                        } else {
                            builder.append(",").append(addressVo.getDivisionName()).append("-").append(fourthChildList.get(i).getDivisionName());
                        }
                    }
                    name = builder.toString();
                }
                thirdResp.setName(name);
                thirdRespList.add(thirdResp);
            }
            secondResp.setChildren(thirdRespList);

            secondRespList.add(secondResp);
        });
        firstList.forEach(id -> {
            AddressIopResp firstResp = new AddressIopResp();
            firstResp.setLevel(1);
            firstResp.setName(addressMap.get(id).getDivisionName());
            List<Long> childIdList = parentGroupMap.get(id).stream().map(Address::getDivisionId).collect(Collectors.toList());
            List<AddressIopResp> secondChildList = secondRespList.stream().filter(f -> childIdList.contains(f.getId())).collect(Collectors.toList());
            firstResp.setChildren(secondChildList);
            respList.add(firstResp);
        });
        threeAndfourAddressList = null;
        addressList = null;
        fourthList = null;
        return respList;
    }

    /**
     * 从redis缓存中获取4级地址信息
     *
     * @param addressIdList
     * @return
     */
    @Override
    public List<Address> getThreeAndFourAddressListFromRedis(List<Long> addressIdList) {
        if (CollectionUtils.isEmpty(addressIdList)) {
            return null;
        }
        String redisKey = String.format(CacheKeyConstant.FOUR_AND_FIVE_ADDRESS_CACHE_KEY);
        //如果存在,get方法无需提前判断exist
        String cacheValue = redisHelper.get(redisKey);
        if (!org.springframework.util.StringUtils.isEmpty(cacheValue)) {
            List<AddressBo> cacheList = JSONObject.parseArray(cacheValue, AddressBo.class);
            if (!CollectionUtils.isEmpty(cacheList)) {
                Map<Long, AddressBo> cacheMap = cacheList.stream().collect(Collectors.toMap(AddressBo::getId, AddressBo -> AddressBo));
                List<Address> resultList = new ArrayList<>();
                addressIdList.forEach(id -> {
                    Address addressVo = new Address();
                    AddressBo bo = cacheMap.get(id);
                    addressVo.setDivisionId(bo.getId());
                    addressVo.setDivisionName(bo.getName());
                    addressVo.setParentId(bo.getParentId());
                    resultList.add(addressVo);
                });
                cacheList = null;
                return resultList;
            }
        }
        List<Address> fiveAddressList = addressApi.getByLevelAndNameLike(5, "");
        List<Address> fourAddressList = addressApi.getByLevelAndNameLike(4, "");
        fiveAddressList.addAll(fourAddressList);
        List<Address> addressList = fiveAddressList;
        List<AddressBo> cacheList = addressList.parallelStream().map(vo -> {
            AddressBo bo = new AddressBo();
            bo.setId(vo.getDivisionId());
            bo.setName(vo.getDivisionName());
            bo.setParentId(vo.getParentId());
            return bo;
        }).collect(Collectors.toList());
        redisHelper.set(redisKey, JSON.toJSONString(cacheList), CacheKeyConstant.ONE_DAY_SECOND);
        List<Address> resultList = addressList.stream().filter(f -> addressIdList.contains(f.getDivisionId())).collect(Collectors.toList());
        addressList = null;
        cacheList = null;
        fiveAddressList = null;
        fourAddressList = null;
        return resultList;
    }

//    /**
//     * 获取活动单个任务订单限制
//     *
//     * @param getAddressDivisionNameRqt
//     * @return
//     */
//    @Override
//    public GetActivityTaskOrderLimitApiResp getActivityTaskOrderLimitInfo(GetActivityTaskOrderLimitApiRqt getAddressDivisionNameRqt) {
//        GetActivityTaskOrderLimitRqt getActivityTaskOrderLimitRqt = new GetActivityTaskOrderLimitRqt();
//        getActivityTaskOrderLimitRqt.setActivityTaskId(getAddressDivisionNameRqt.getActivityTaskId());
//        GetActivityTaskOrderLimitResp orderLimit = landingPageServiceApi.getActivityTaskOrderLimitInfo(getActivityTaskOrderLimitRqt);
//        if (ObjectUtils.isEmpty(orderLimit)) {
//            throw new BusinessException("get_task_order_limit_fail", "获取活动任务订单限制信息为空！");
//        }
//        GetActivityTaskOrderLimitApiResp resp = new GetActivityTaskOrderLimitApiResp();
//        //三级结构地址服务名称-null为不限
//        if (StringUtils.isNotEmpty(orderLimit.getThreeAddressNames())) {
//            List<AddressMinResp> threeDivisionIdsNameList = JSONArray.parseArray(orderLimit.getThreeAddressNames(), AddressMinResp.class);
//            resp.setThreeDivisionIdsName(threeDivisionIdsNameList);
//        } else {
//            resp.setThreeDivisionIdsName(null);
//        }
//        //服务类目解析：分为家庭服务类目及商家服务类目
//        Map<Long, String> serveNameMap = new HashMap<>();
//        if (StringUtils.isNotEmpty(orderLimit.getServeIds())) {
//            List<String> serveList = new ArrayList<>();
//            List<Long> objects = JSONObject.parseArray(orderLimit.getServeIds(), Long.class);
//            //订单来源为不限时默认填的是商家服务类目
//            if (ActivityUtil.isFilterMerchantOrderFrom(orderLimit.getOrderFrom()) || StringUtils.isEmpty(orderLimit.getOrderFrom())) {
//                serveNameMap = this.getServeIdsMap(objects, orderLimit.getActivityId(), getAddressDivisionNameRqt.getActivityTaskId(), 1L);
//            } else if (orderLimit.getOrderFrom().equals(ActivityConstant.FAMILY_IOP)) {
//                serveNameMap = this.getServeIdsMap(new ArrayList<>(objects), orderLimit.getActivityId(), getAddressDivisionNameRqt.getActivityTaskId(), 2L);
//            }
//            Map<Long, String> finalServeNameMap = serveNameMap;
//            objects.forEach(id -> {
//                if (StringUtils.isNotEmpty(finalServeNameMap.get(id))) {
//                    serveList.add(finalServeNameMap.get(id));
//                }
//            });
//            resp.setServesName(String.join(",", serveList));
//        } else {
//            resp.setServesName(ActivityConstant.NOT_LIMIT_CN);
//        }
//        //订单类型名称： user:用户订单,master:师傅订单,enterprise:总包订单,NULL:不限
//        resp.setFromAccountTypeName(this.returnAccountTypeName(orderLimit.getFromAccountType(), orderLimit.getSpType()));
//        //来源(客户端)[site:网站,enterprise_system:总包外部订单,weixin:微信,ikea:宜家,thirdpart:第三方平台,applet:小程序,NULL:不限（多选）],
//        resp.setOrderFromName(StringUtils.isEmpty(orderLimit.getOrderFrom()) ? ActivityConstant.NOT_LIMIT_CN : ActivityUtil.coverOrderFromToIocName(orderLimit.getOrderFrom(), orderLimit.getUserClass(), orderLimit.getInviteType()));
//        //其他屏蔽条件
//        if (StringUtils.isNotEmpty(orderLimit.getOtherOrderFilterCondition())) {
//            resp.setOtherOrderFilterConditionName(orderLimit.getOtherOrderFilterCondition().replace(ActivityConstant.IS_ORDER_PACKAGE, ActivityConstant.IS_ORDER_PACKAGE_NAME).replace(ActivityConstant.IS_ORDER_SHOP, ActivityConstant.IS_ORDER_SHOP_NAME));
//        }
//        //其他订单限制条件
//        if (!StringUtils.isNotEmpty(orderLimit.getOtherOrderSuitCondition())) {
//            resp.setOtherOrderSuitConditionName(orderLimit.getOtherOrderSuitCondition().replace(ActivityConstant.IS_ORDER_CONTRACT, ActivityConstant.IS_ORDER_CONTRACT_NAME));
//        }
//        //appointMethodName 下单模式[normal:直接指派,open:公开抛单,definite_price:一口价,advance_payment:预付款,NULL:不限（多选）]
//        resp.setAppointMethodName(this.returnAppointMethodName(orderLimit.getAppointMethod()));
//        //商品数量
//        resp.setGoodsNumberName(GoodsNumberNameEnum.getGoodsNumberNameByNumber(orderLimit.getGoodsNumber()));
//        //订单报价限制
//        if (orderLimit.getOfferLimitTime() != null) {
//            //天，小时，分从大到小依次进行除法，能被整除的为该单位
//            TimeUnitVo timeUnitVo = ActivityUtil.transformTimeUtil(orderLimit.getOfferLimitTime());
//            resp.setOfferLimitTime(timeUnitVo.getTimeNumber());
//            resp.setOfferLimitTimeUnit(timeUnitVo.getTimeUnit());
//        }
//        //技能树
//        this.setSkillTree(resp, orderLimit);
//        return resp;
//    }

    /**
     * 抽奖专用落地页详情
     *
     * @param rqt
     * @return
     */
    @Override
    public LotteryTurntableLandingPageDetailApiResp getLotteryTurntableLandingPageDetail(LotteryTurntableLandingPageDetailApiRqt rqt) {
        //是否登陆
        Long userId = null;
        if (StringUtils.isNotEmpty(rqt.getSignature())) {
            userId = this.getMasterIdBySignature(rqt.getSignature());
        }
        boolean isLogin = (userId != null);
        //1.获取活动信息
        //实例化活动详情
        LotteryTurntableLandingPageDetailApiResp lotteryTurntableLandingPageDetailApiResp = new LotteryTurntableLandingPageDetailApiResp();
        lotteryTurntableLandingPageDetailApiResp.setLogin(isLogin);


        LotteryTurntableLandingPageDetailResp lotteryTurntableLandingPageDetailResp = new LotteryTurntableLandingPageDetailResp();
        Integer isShowTip = 1;
        if (isLogin) {
            //判断用户登陆状态、
            LotteryTurntableLandingPageDetailRqt lotteryTurntableLandingPageDetailRqt = new LotteryTurntableLandingPageDetailRqt();
            BeanUtils.copyProperties(rqt, lotteryTurntableLandingPageDetailRqt);
            lotteryTurntableLandingPageDetailRqt.setIsShowTip(isShowTip);
            lotteryTurntableLandingPageDetailRqt.setUserId(userId);
            try {
                lotteryTurntableLandingPageDetailResp = landingPageServiceApi.getLotteryTurntableLandingPageDetail(lotteryTurntableLandingPageDetailRqt);
            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
            }

        } else {
            PageGetActivityDetailRqt getActivityDetailNoLoginRqt = new PageGetActivityDetailRqt();
            BeanUtils.copyProperties(rqt, getActivityDetailNoLoginRqt);
            try {
                lotteryTurntableLandingPageDetailResp = (LotteryTurntableLandingPageDetailResp) this.coverObject(landingPageServiceApi.getActivityDetailNoLogin(getActivityDetailNoLoginRqt), LotteryTurntableLandingPageDetailResp.class);

            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());

            }
        }
        try {
            //基础信息
            LotteryTurntableLandingPageDetailApiResp.ActivityBaseInfo activityBaseInfo = new LotteryTurntableLandingPageDetailApiResp.ActivityBaseInfo();
            LotteryTurntableLandingPageDetailResp.ActivityBaseInfo activityBaseInfo1 = lotteryTurntableLandingPageDetailResp.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");
            //待领奖数，如果自动领奖，待领奖数是0
            if (OperateMethodEnum.AUTO.code.equals(
                    lotteryTurntableLandingPageDetailResp.getActivityBaseInfo().getRewardGiveMethod())) {
                lotteryTurntableLandingPageDetailApiResp.setAwarding(0);
            } else {
                lotteryTurntableLandingPageDetailApiResp.setAwarding(lotteryTurntableLandingPageDetailResp.getAwarding());
            }

            lotteryTurntableLandingPageDetailApiResp.setHasAwarding(lotteryTurntableLandingPageDetailResp.getHasAwarding());

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));
            //自动报名
            if (OperateMethodEnum.AUTO.code.equals(activityBaseInfo.getApplyMethod())) {
                activityBaseInfo.setApplyStartTime(activityBaseInfo.getLaunchStartTime());
                activityBaseInfo.setApplyEndTime(activityBaseInfo.getActivityEndTime());
            }
            activityBaseInfo.setApplyCondition(lotteryTurntableLandingPageDetailResp.getActivityBaseExt().getApplyCondition());
            lotteryTurntableLandingPageDetailApiResp.setActivityBaseInfo(activityBaseInfo);

            //落地页配置信息
            LotteryTurntableLandingPageDetailApiResp.LandingPageInfo landingPageInfo = new LotteryTurntableLandingPageDetailApiResp.LandingPageInfo();
            LotteryTurntableLandingPageDetailResp.LandingPageInfo landingPageInfo1 = lotteryTurntableLandingPageDetailResp.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);

            if (isLogin && StringUtils.isNotEmpty(landingPageInfo.getNoQualificationsTips())) {
                lotteryTurntableLandingPageDetailApiResp.setLandingPageInfo(landingPageInfo);
                return lotteryTurntableLandingPageDetailApiResp;
            } else {
                landingPageInfo.setNoQualificationsTips(null);
            }
            //2.获取转盘信息,转盘id为同一个，所以直接取值
            LotteryTurntableLandingPageDetailResp.ActivityTaskBo activityTaskBoLott = lotteryTurntableLandingPageDetailResp.getActivityTaskLists().stream().
                    filter(f -> (RewardSymbol.LOTTERY_TURNTABLE.type.equals(f.getRewardSymbol())) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).findFirst().orElseThrow(() -> new RuntimeException("转盘信息不存在"));

            Long prizeWheelId = activityTaskBoLott.getRewardExtraId();
            PrizeWheelInfoApiResp prizeWheelInfoApiResp = this.getPrizeWheelInfoApiRespData(prizeWheelId);
            List<Long> awardImgAidList = prizeWheelInfoApiResp.getPrizeWheelAwardList().stream()
                    .map(PrizeWheelAwardsApiVo::getAwardImgAid).filter(
                            Objects::nonNull).collect(Collectors.toList());
            ;
            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = lotteryTurntableLandingPageDetailResp.getActivityTaskLists().stream()
                    .map(LotteryTurntableLandingPageDetailResp.ActivityTaskBo::getRewardImageAid).filter(
                            Objects::nonNull).collect(Collectors.toList());

            imageAidList.addAll(awardImgAidList);
            //未登录需要自己去查aid对应的图片
            if (!isLogin) {
                imageAidList.add(landingPageInfo1.getAppTopImageAid());
                imageAidList.add(landingPageInfo1.getRewardImageAid());
                imageAidList.add(landingPageInfo1.getTopImageAid());
                imageAidList.add(landingPageInfo1.getPreviewImageAid());
                imageAidList.add(landingPageInfo1.getBackgroundImageAid());
            }
            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());
            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }
            landingPageInfo.setRewardTimageUrl(
                    imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());
            //图片设置
            if (CollectionUtils.isNotEmpty(awardImgAidList)) {
                Map<Long, String> finalImageResultMap = imageResultMap;
                prizeWheelInfoApiResp.getPrizeWheelAwardList().stream().forEach(prizeWheelAwardsApiVo -> {
                    prizeWheelAwardsApiVo.setAwardImgUrl(finalImageResultMap.getOrDefault(prizeWheelAwardsApiVo.getAwardImgAid(), ""));
                });
                //按照奖品排序
                prizeWheelInfoApiResp.getPrizeWheelAwardList().stream().sorted(Comparator.comparing(
                        PrizeWheelAwardsApiVo::getSerialNo));

            }
            if (!StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {
                List<ActivityModelDetailResp> detailList = JSONArray.parseArray(
                        landingPageInfo1.getActivityModelDetail(), ActivityModelDetailResp.class);
                landingPageInfo.setActivityModelDetail(detailList);
            }
            if (!isLogin) {
                landingPageInfo.setRewardTimageUrl(imageResultMap.get(landingPageInfo1.getRewardImageAid()));
                landingPageInfo.setAppTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));
                landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));
                landingPageInfo.setPreviewImageUrl(imageResultMap.get(landingPageInfo1.getPreviewImageAid()));
                landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getTopImageAid()));
            }

            lotteryTurntableLandingPageDetailApiResp.setLandingPageInfo(landingPageInfo);
            //实例化任务列表
            List<LotteryTurntableLandingPageDetailApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();

            //任务列表
            for (LotteryTurntableLandingPageDetailResp.ActivityTaskBo activityTaskList : lotteryTurntableLandingPageDetailResp.getActivityTaskLists()) {
                LotteryTurntableLandingPageDetailApiResp.ActivityTaskList activityTaskListTmp = new LotteryTurntableLandingPageDetailApiResp.ActivityTaskList();

                if (activityTaskList.getRewardImageAid() != null) {
                    activityTaskListTmp.setRewardImageUrl(
                            imageResultMap.get(activityTaskList.getRewardImageAid()));
                }
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);
                activityTaskListTmp.setRewardGiveValue(activityTaskListTmp.getRewardGiveValue());
                activityTaskListTmp.setRewardType(activityTaskList.getRewardConfigId());

                if (activityTaskList.getUserActivityTask() != null) {
                    activityTaskListTmp.setUserActivityTaskId(
                            activityTaskList.getUserActivityTask().getUserActivityTaskId());
                }
                //设置奖励文案
                TagTextBo tagTextBo = this.splitTagText(activityTaskList.getTagText());
                if (tagTextBo != null) {
                    activityTaskListTmp.setCustomizeTargetText(tagTextBo.getCustomizeTargetText());
                }
                //设置任务文案 xx内完成1单
                activityTaskListTmp.setTitle(this.getTaskTitle(activityTaskList.getTaskSymbol(),
                        activityTaskList.getUserActivityTask(), activityTaskListTmp.getCustomizeTargetText(), activityTaskListTmp.getTargetQuantityValue()));
                //文案设定--------------------
                //执行频率
                activityTaskListTmp.setTitleDesc(
                        this.getTaskTitleDesc(activityTaskList.getActivityTaskInfo()));
                //已领次数
                activityTaskListTmp.setRewardNumText(this.getRewardNumTextV2(activityTaskList));
                //还差次数
                activityTaskListTmp.setNeedRewardNumText(
                        this.getNeedRewardNumTextV2(activityTaskList.getNeedRewardNum()));

                if (activityBaseInfo.getIsActivityStart() == 1 && isLogin) {
                    if (activityTaskList.getButtonStatus() == 3
                            || activityTaskList.getButtonStatus() == 0) {
                        activityTaskListTmp.setSecondsRemain(0L);
                    } else {
                        activityTaskListTmp.setSecondsRemain(
                                this.secondsRemain(lotteryTurntableLandingPageDetailApiResp.getActivityBaseInfo().getActivityEndTime(),
                                        activityTaskList.getTimeLimit().longValue(),
                                        activityTaskList.getApplyTime()));
                    }
                } else {
                    activityTaskListTmp.setSecondsRemain(0L);
                }

                //没报名倒计时返回0
                if (activityBaseInfo.getIsApply() == 0) {
                    activityTaskListTmp.setSecondsRemain(0L);
                }
                activityTaskListTmp.setIsShowOrderLimit(TaskSymbolEnum.isShowOrderLimit(activityTaskListTmp.getTaskSymbol()));

                if (!StringUtils.isEmpty(activityTaskListTmp.getTargetAmountValueRange())) {
                    activityTaskListTmp.setTargetAmountValueRange(
                            activityTaskListTmp.getTargetAmountValueRange().replace("[", "")
                                    .replace("]", ""));
                }
                //如果库存为0，也要展示已抢光
                if (prizeWheelInfoApiResp.getPrizeWheelNum() == 0 && isLogin) {
                    activityTaskListTmp.setButtonStatus(ActivityButtonStateEnum.CollectedCompleteALl.code);
                    activityTaskListTmp.setIsShowProgressState(ActivityButtonStateEnum.NOT_SHOW.code);
                    activityTaskListTmp.setNeedRewardNum(ActivityButtonStateEnum.NOT_SHOW.code);
                    activityTaskListTmp.setRewardNum(ActivityButtonStateEnum.NOT_SHOW.code);
                }
                activityTaskLists.add(activityTaskListTmp);
            }
            //任务列表
            lotteryTurntableLandingPageDetailApiResp.setActivityTaskLists(activityTaskLists);
            //避免返回给前端
            prizeWheelInfoApiResp.setPrizeWheelNum(null);
            lotteryTurntableLandingPageDetailApiResp.setPrizeWheelInfoApiResp(prizeWheelInfoApiResp);

            LotteryTurntableLandingPageDetailApiResp.ActivityBaseInfo activityBaseInfo2 = lotteryTurntableLandingPageDetailApiResp.getActivityBaseInfo();
            lotteryTurntableLandingPageDetailApiResp.setActivityBaseInfo(activityBaseInfo2);
            lotteryTurntableLandingPageDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);
            return lotteryTurntableLandingPageDetailApiResp;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("get_activity_detail_fail", e.getMessage());
        }
    }

    private PrizeWheelInfoApiResp getPrizeWheelInfoApiRespData(Long prizeWheelId) {
        if (null == prizeWheelId) {
            return null;
        }
        GetPrizeWheelInfoReq getPrizeWheelInfoReq = new GetPrizeWheelInfoReq();
        getPrizeWheelInfoReq.setPrizeWheelId(prizeWheelId);
        try {
            PrizeWheelInfoResp prizeWheelInfoResp = prizeWheelBusinessServiceApi.getPrizeWheelDetailById(getPrizeWheelInfoReq);
            PrizeWheelInfoApiResp prizeWheelInfoApiResp = new PrizeWheelInfoApiResp();
            prizeWheelInfoApiResp.setPrizeWheelId(prizeWheelId);
            List<PrizeWheelAwardsApiVo> prizeWheelAwardList = prizeWheelInfoResp.getPrizeWheelAwardList().stream().map(prizeWheelAwardsVo -> {
                PrizeWheelAwardsApiVo prizeWheelAwardsApiVo = new PrizeWheelAwardsApiVo();
                BeanCopyUtil.copyProperties(prizeWheelAwardsVo, prizeWheelAwardsApiVo);

                EquitySymbolEnum equitySymbolEnum = EquitySymbolEnum.fromType(prizeWheelAwardsApiVo.getEquitySymbol());
                prizeWheelAwardsApiVo.setEquitySymbolName(prizeWheelAwardsVo.getAwardName());
                if (null != equitySymbolEnum) {
                    prizeWheelAwardsApiVo.setRewardValueUnit(equitySymbolEnum.simpleAlias);
                }
                return prizeWheelAwardsApiVo;
            }).collect(Collectors.toList());
            prizeWheelInfoApiResp.setPrizeWheelAwardList(prizeWheelAwardList);
            prizeWheelInfoApiResp.setPrizeWheelNum(prizeWheelInfoResp.getRemainLotteryDrawNum());
            return prizeWheelInfoApiResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
    }

    /**
     * 抽奖弹幕
     *
     * @param rqt
     * @return
     */
    @Override
    public List<LotteryBarrageListApiResp> getLotteryBarrageList(LotteryBarrageListApiRqt rqt) {
        //做缓存
        String redisKey = String.format(CacheKeyConstant.ACTIVITY_PRIZEWHEELID_KEY, rqt.getActivityId(), rqt.getPrizeWheelId());
        //如果存在
        if (redisHelper.exists(redisKey)) {
            return JSONObject.parseArray(redisHelper.get(redisKey), LotteryBarrageListApiResp.class);
        }
        RewardGiveIdByActivityIdRqt rewardGiveIdByActivityIdRqt = new RewardGiveIdByActivityIdRqt();
        rewardGiveIdByActivityIdRqt.setActivityId(rqt.getActivityId());
        RewardGiveIdByActivityIdResp rewardGiveIdByActivityIdResp = activityBusinessServiceApi.getRewardGiveIdByActivityId(rewardGiveIdByActivityIdRqt);
        if (rewardGiveIdByActivityIdResp == null || CollectionUtils.isEmpty(rewardGiveIdByActivityIdResp.getRewardGiveIds())) {
            return null;
        }
        //获取中奖记录
        GetWinAwardItemByExtraIdRqt getWinAwardItemByExtraIdRqt = new GetWinAwardItemByExtraIdRqt();
        getWinAwardItemByExtraIdRqt.setPrizeWheelId(rqt.getPrizeWheelId());
        getWinAwardItemByExtraIdRqt.setUserClass(rqt.getUserClass());
        getWinAwardItemByExtraIdRqt.setRewardGiveIds(rewardGiveIdByActivityIdResp.getRewardGiveIds());
        List<GetWinAwardItemByExtraIdResp> getWinAwardItemByExtraIdResps = prizeWheelBusinessServiceApi.getWinAwardItemByExtraId(getWinAwardItemByExtraIdRqt);

        if (null == getWinAwardItemByExtraIdResps) {
            log.warn("中奖记录小于10条，则不展示弹幕");
            return null;
        }
        //获取师傅信息
        List<Long> masterIds = getWinAwardItemByExtraIdResps.stream().map(GetWinAwardItemByExtraIdResp::getUserId).collect(Collectors.toList());
        GetMasterBaseInfoBatchRqt getMasterBaseInfoBatchRqt = new GetMasterBaseInfoBatchRqt();
        getMasterBaseInfoBatchRqt.setMasterIds(masterIds);
        List<GetMasterBaseInfoBatchResp> getMasterBaseInfoBatchResps = masterInfoApi.getMasterBaseInfoBatch(getMasterBaseInfoBatchRqt);

        List<LotteryBarrageListApiResp> lotteryBarrageListApiResps = getWinAwardItemByExtraIdResps.stream().map(getWinAwardItemByExtraIdResp -> {
            GetMasterBaseInfoBatchResp getMasterBaseInfoBatchResp = getMasterBaseInfoBatchResps.stream().filter(master -> master.getMasterId().equals(getWinAwardItemByExtraIdResp.getUserId())).findFirst().orElseThrow(() -> new RuntimeException("师傅信息不存在"));
            //截取姓名
            List<String> listNames = ActivityUtil.nameSplit(getMasterBaseInfoBatchResp.getMasterName());

            LotteryBarrageListApiResp lotteryBarrageListApiResp = new LotteryBarrageListApiResp();
            lotteryBarrageListApiResp.setEquitySymbol(getWinAwardItemByExtraIdResp.getEquitySymbol());
            lotteryBarrageListApiResp.setRewardValue(getWinAwardItemByExtraIdResp.getRewardValue());
            lotteryBarrageListApiResp.setPrizeWheelAwardId(getWinAwardItemByExtraIdResp.getPrizeWheelAwardId());

            EquitySymbolEnum equitySymbolEnum = EquitySymbolEnum.fromType(getWinAwardItemByExtraIdResp.getEquitySymbol());
            lotteryBarrageListApiResp.setUserRewardText(
                    String.format(ActivityConstant.MASTER_COMMON_CONTRACT_NAME, listNames.get(0), MathUtils.decimalCutZero(getWinAwardItemByExtraIdResp.getRewardValue()) + equitySymbolEnum.simpleAlias, equitySymbolEnum.cn));

            return lotteryBarrageListApiResp;

        }).collect(Collectors.toList());

        //缓存1h
        redisHelper.set(redisKey, JSON.toJSONString(lotteryBarrageListApiResps), CacheKeyConstant.ONE_HOUR_SECOND);

        return lotteryBarrageListApiResps;
    }

    /**
     * 只返回一个任务
     * 待领奖数量=0时，展示序号最小的待完成任务，进度按照当前任务进度百分比进行展示
     * 存在多个待领奖奖励时，展示任务ID最大的任务展示该状态
     *
     * @param getActivityDetailApiRqt
     * @return
     */
    @Override
    public MerchantActivityDetailBannerAdApiResp getMerchantActivityDetailBannerAd(GetActivityDetailBannerApiRqt getActivityDetailApiRqt) {
        //实例化活动详情
        MerchantActivityDetailBannerAdApiResp getActivityDetailApiResp = new MerchantActivityDetailBannerAdApiResp();
        MerchantActivityDetailBannerAdResp activityDetail = null;

        GetActivityDetailBannerRqt getActivityDetailRqt = new GetActivityDetailBannerRqt();
        getActivityDetailRqt.setUserId(getActivityDetailApiRqt.getUserId());
        getActivityDetailRqt.setActivityId(getActivityDetailApiRqt.getActivityId());

        try {
            activityDetail = landingPageServiceApi.getMerchantActivityDetailBannerAd(getActivityDetailRqt);
        } catch (ApiAccessException e) {
            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
        try {
            //组合活动返回信息为空，特殊处理
            if(Objects.isNull(activityDetail)){
                return null;
            }
            //基础信息
            MerchantActivityDetailBannerAdApiResp.ActivityBaseInfo activityBaseInfo = new MerchantActivityDetailBannerAdApiResp.ActivityBaseInfo();
            MerchantActivityDetailBannerAdResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            //待领奖信息
            getActivityDetailApiResp.setRewardInfoRespList(this.buildRewardInfo(activityDetail.getRewardInfoList(),getActivityDetailApiRqt.getUserClass()));
            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));
            //礼盒图片先默认写死，后续加配置表
            activityBaseInfo.setTaskNotFinishGiftImgUrl(ActivityConstant.TASK_NO_FINISH_GIFT_IMG_URL);
            activityBaseInfo.setTaskFinishGiftImgUrl(ActivityConstant.TASK_FINISHED_GIFT_IMG_URL);
            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);
            getActivityDetailApiResp.setShowTaskStatus(2);
            //任务列表赋值
            List<MerchantActivityDetailBannerAdResp.ActivityTaskBo> activityTaskRespLists = activityDetail.getActivityTaskLists();
            if (CollectionUtils.isEmpty(activityTaskRespLists)) {
                return getActivityDetailApiResp;
            }
            //查询所有待领奖的任务id列表
            List<Long> userActivityTaskIdList = activityTaskRespLists.stream().filter(f -> ActivityButtonStateEnum.NOT_CLAIMED.code.equals(f.getButtonStatus())).map(m -> m.getUserActivityTaskId()).collect(Collectors.toList());
            //过滤不符合条件的任务
            Iterator<MerchantActivityDetailBannerAdResp.ActivityTaskBo> activityTaskRespListIterator = activityTaskRespLists.iterator();
            while (activityTaskRespListIterator.hasNext()) {
                MerchantActivityDetailBannerAdResp.ActivityTaskBo activityTaskVo = activityTaskRespListIterator.next();
                //判断是否选中
                if (Objects.isNull(activityTaskVo.getIsChoice()) || 0 == activityTaskVo.getIsChoice()) {
                    activityTaskRespListIterator.remove();
                }
            }
            if (CollectionUtils.isEmpty(activityTaskRespLists)) {
                return getActivityDetailApiResp;
            }
            MerchantActivityDetailBannerAdResp.ActivityTaskBo selectedTaskVo = null;
            //判断是否有未领奖的任务:取最后一个未领取任务
            Optional<MerchantActivityDetailBannerAdResp.ActivityTaskBo> activityTaskBoOptional = activityTaskRespLists.stream().filter(f -> ActivityButtonStateEnum.NOT_CLAIMED.code.equals(f.getButtonStatus())).
                    sorted(Comparator.comparing(MerchantActivityDetailBannerAdResp.ActivityTaskBo::getActivityTaskId).reversed()).findFirst();
            if (activityTaskBoOptional.isPresent()) {
                selectedTaskVo = activityTaskBoOptional.get();
                getActivityDetailApiResp.setShowTaskStatus(1);
            } else {
                //取未完成任务第一个
                Optional<MerchantActivityDetailBannerAdResp.ActivityTaskBo> activityTaskUnfinishOptional = activityTaskRespLists.stream().filter(f -> ActivityButtonStateEnum.NOT_COMPLETE.code.equals(f.getButtonStatus())).
                        sorted(Comparator.comparing(MerchantActivityDetailBannerAdResp.ActivityTaskBo::getActivityTaskId)).findFirst();
                if (activityTaskUnfinishOptional.isPresent()) {
                    selectedTaskVo = activityTaskUnfinishOptional.get();
                    getActivityDetailApiResp.setShowTaskStatus(0);
                }
            }

            List<PackageItemBo> packageItemBoList = null;
            //如果是多选一奖励,需要获取多选一奖励信息
            if(Objects.nonNull(selectedTaskVo) && RewardSymbol.COMBINE_CHOOSE_REWARD.type.equals(selectedTaskVo.getRewardSymbol())){
                GetCombineRewardConfigRqt queryRqt = new GetCombineRewardConfigRqt();
                queryRqt.setId(selectedTaskVo.getRewardExtraId());
                CombineRewardConfig combineRewardConfig = combinedPageServiceApi.getCombineRewardConfig(queryRqt);
                packageItemBoList = JSONArray.parseArray(combineRewardConfig.getPackageList(), PackageItemBo.class);
            }

            //提前批量查询现金券及卷包
            Map<Long, VoucherRewardBo> voucherBoMap = new HashMap<>();
            Map<Long, VoucherRewardBo> voucherPackBoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList()) || Objects.nonNull(selectedTaskVo)) {
                List<Integer> queryVoucherIdList = new ArrayList<>();
                List<Integer> queryVoucherPackIdList = new ArrayList<>();

                if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList())) {
                    List<Integer> voucherIdList = activityDetail.getActivityRewardGiveItemList().stream().filter(f -> RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol())).map(m -> m.getRewardExtraId().intValue()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(voucherIdList)) {
                        queryVoucherIdList.addAll(voucherIdList);
                    }
                }
                if (Objects.nonNull(selectedTaskVo) && RewardSymbol.VOUCHER.type.equals(selectedTaskVo.getRewardSymbol())) {
                    queryVoucherIdList.add(selectedTaskVo.getRewardExtraId().intValue());
                }

                if(CollectionUtils.isNotEmpty(packageItemBoList)){
                    for(PackageItemBo bo : packageItemBoList){
                        if(RewardSymbol.VOUCHER.type.equals(bo.getRewardSymbol())){
                            queryVoucherIdList.add(bo.getRewardExtraId());
                        }
                        if(RewardSymbol.VOUCHER_PACK.type.equals(bo.getRewardSymbol())){
                            queryVoucherPackIdList.add(bo.getRewardExtraId());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList())) {
                    List<Integer> voucherPackIdList = activityDetail.getActivityRewardGiveItemList().stream().filter(f -> RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol())).map(m -> m.getRewardExtraId().intValue()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(voucherPackIdList)) {
                        queryVoucherPackIdList.addAll(voucherPackIdList);
                    }
                }
                if (Objects.nonNull(selectedTaskVo) && RewardSymbol.VOUCHER_PACK.type.equals(selectedTaskVo.getRewardSymbol())) {
                    queryVoucherPackIdList.add(selectedTaskVo.getRewardExtraId().intValue());
                }

                voucherBoMap = super.getOnlyVoucherBatchGetReward(queryVoucherIdList);
                voucherPackBoMap = super.batchQueryVoucherPackValue(queryVoucherPackIdList);
            }
            //已获取奖励文案
            if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList())) {
                getActivityDetailApiResp.setActivityAllGiveRewardText(this.returnActivityAllGiveRewardText(activityDetail.getActivityRewardGiveItemList(), voucherBoMap, voucherPackBoMap));
            }
            if (Objects.isNull(selectedTaskVo)) {
                return getActivityDetailApiResp;
            }
            //返回任务赋值开始------
            MerchantActivityDetailBannerAdApiResp.ActivityTaskVo activityRespTaskVo = new MerchantActivityDetailBannerAdApiResp.ActivityTaskVo();
            BeanUtils.copyProperties(selectedTaskVo, activityRespTaskVo);
            activityRespTaskVo.setUserActivityTaskIdList(userActivityTaskIdList);

            List<MerchantActivityDetailBannerAdApiResp.ActivityTaskVo> combinedVoList = null;
            //奖励价值赋值，优惠券类型
            if (selectedTaskVo.getRewardExtraId() != null && selectedTaskVo.getRewardExtraId() > 0) {
                if(RewardSymbol.COMBINE_CHOOSE_REWARD.type.equals(selectedTaskVo.getRewardSymbol())){
                    combinedVoList = buildCombinedTaskVo(packageItemBoList,voucherBoMap,voucherPackBoMap);
                } else {
                    VoucherRewardBo voucherVo = RewardSymbol.VOUCHER.type.equals(selectedTaskVo.getRewardSymbol()) ? voucherBoMap.get(selectedTaskVo.getRewardExtraId()) : voucherPackBoMap.get(selectedTaskVo.getRewardExtraId());
                    if (voucherVo != null) {
                        activityRespTaskVo.setVoucherType(voucherVo.getVoucherType());
                        activityRespTaskVo.setRewardValue(voucherVo.getRewardValue());
                        if (RewardSymbol.VOUCHER_PACK.type.equals(selectedTaskVo.getRewardSymbol())) {
                            BigDecimal packNum = Objects.nonNull(selectedTaskVo.getRewardGiveValue()) && selectedTaskVo.getRewardGiveValue().compareTo(BigDecimal.ZERO) > 0 ? selectedTaskVo.getRewardGiveValue() : new BigDecimal("1");
                            BigDecimal rewardValue = voucherVo.getRewardValue().multiply(packNum).setScale(2, BigDecimal.ROUND_HALF_UP);
                            activityRespTaskVo.setRewardValue(rewardValue);
                        }
                    }
                }
            } else {
                //非优惠券奖励类型
                activityRespTaskVo.setRewardValue(selectedTaskVo.getRewardGiveValue());
            }
            if (1 == activityBaseInfo.getIsActivityStart()) {
                //判断是否选中
                if (1 == activityRespTaskVo.getIsChoice()) {
                    if (3 == selectedTaskVo.getButtonStatus() || 0 == selectedTaskVo.getButtonStatus()) {
                        activityRespTaskVo.setSecondsRemain(0L);
                    } else {
                        activityRespTaskVo.setSecondsRemain(this.secondsRemain(activityBaseInfo.getActivityEndTime(), selectedTaskVo.getTimeLimit().longValue(), selectedTaskVo.getApplyTime()));
                    }
                }
            } else {
                activityRespTaskVo.setSecondsRemain(0L);
            }

            //没报名倒计时返回0
            if (0 == activityBaseInfo.getIsApply()) {
                activityRespTaskVo.setSecondsRemain(0L);
            }
            //还差xx文案、奖励文案赋值
            NeedRewardAndCurrentPercentBo returnBo = this.returnNeedRewardText(activityRespTaskVo, getActivityDetailApiResp.getShowTaskStatus());
            activityRespTaskVo.setNeedRewardText(returnBo.getNeedRewardText());
            activityRespTaskVo.setRewardText(this.returnRewardText(activityRespTaskVo,combinedVoList));
            activityRespTaskVo.setCurrentTaskPercent(returnBo.getCurrentTaskPercent());
            //任务赋值
            getActivityDetailApiResp.setActivityTaskVo(activityRespTaskVo);
            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusException("get_activity_detail_fail", e.getMessage());
        }
    }

    /**
     * 师傅多状态广告位详情聚合接口
     * @param rqt
     * @return
     */
    @Override
    public GetDetailAggregationBannerApiResp getDetailAggregationBanner(GetDetailAggregationBannerApiRqt rqt) {
        Long userId = rqt.getUserId();
        //1.调用用户广告位获取广告接口
        List<AdInfo> adInfos = new ArrayList<>();
        try{
            GetUserAdRqt getUserAdRqt = new GetUserAdRqt();
            getUserAdRqt.setUserClass(rqt.getUserClass());
            getUserAdRqt.setLaunchPort(rqt.getLaunchPort());
            getUserAdRqt.setAdPositionSymbol(rqt.getAdPositionSymbol());
            String signature = httpServletRequest.getHeader("signature");

            GetUserAdResp getUserAdResp = activityService.getAdPosition(getUserAdRqt,null,null,signature);
            if(getUserAdResp==null || CollectionUtils.isEmpty(getUserAdResp.getAdInfo())){
                return new GetDetailAggregationBannerApiResp();
            }
            adInfos = getUserAdResp.getAdInfo();
        }catch (Exception e){

            throw new BusException(e.getMessage());
        }
        AdInfo adInfo1 = adInfos.get(0);
        //2.获取多状态活动详情数据
        GetMasterActivityDetailAdResp getMasterActivityDetailAdResp = null;
        try {
            GetMasterActivityDetailAdRqt getActivityDetailRqt = new GetMasterActivityDetailAdRqt();
            getActivityDetailRqt.setUserId(userId);
            getActivityDetailRqt.setActivityId(adInfo1.getActivityId());
            getMasterActivityDetailAdResp = landingPageServiceApi.getMasterActivityDetailAd(getActivityDetailRqt);
        } catch (ApiAccessException e) {
            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
        GetDetailAggregationBannerApiResp getDetailAggregationBannerApiResp = new GetDetailAggregationBannerApiResp();
        GetDetailAggregationBannerApiResp.ActivityInfo activityInfo  = new GetDetailAggregationBannerApiResp.ActivityInfo();
        BeanCopyUtil.copyProperties(getMasterActivityDetailAdResp,activityInfo);

        List<ActivityTaskInfo> activityTaskInfos = getMasterActivityDetailAdResp.getActivityTaskInfo();
        GetDetailAggregationBannerApiResp.ActivityInfo.ActivityTaskApiInfo activityTaskApiInfo = new ActivityTaskApiInfo();
        if (CollectionUtils.isNotEmpty(activityTaskInfos)) {
            BeanCopyUtil.copyProperties(activityTaskInfos.get(0),activityTaskApiInfo);
            activityInfo.setActivityTaskInfo(activityTaskApiInfo);
        }
        getDetailAggregationBannerApiResp.setActivityInfo(activityInfo);

        getDetailAggregationBannerApiResp.setAdId(adInfo1.getAdId());
        getDetailAggregationBannerApiResp.setAdName(adInfo1.getAdName());
        getDetailAggregationBannerApiResp.setAdDescription(adInfo1.getAdDescription());
        List<GetDetailAggregationBannerApiResp.Meterial> meterialList = adInfo1.getMeterialList().stream().map(m->{
            GetDetailAggregationBannerApiResp.Meterial meterial = new Meterial();
            BeanCopyUtil.copyProperties(m,meterial);

            return meterial;
        }).collect(Collectors.toList());
        getDetailAggregationBannerApiResp.setMeterialList(meterialList);
        return getDetailAggregationBannerApiResp;
    }
    /**
     * 商家后台多状态广告位数据-通用
     * */
    @Override
    public MerchantActivityDetailMultiStatusAdApiResp getMerchantActivityDetailMultiStatusAd(MerchantActivityDetailMultiStatusAdApiRqt getActivityDetailApiRqt) {
        //实例化活动详情
        MerchantActivityDetailMultiStatusAdApiResp getActivityDetailApiResp = new MerchantActivityDetailMultiStatusAdApiResp();
        MerchantActivityDetailMultiStatusAdResp activityDetail = null;

        MerchantActivityDetailMultiStatusAdRqt getActivityDetailRqt = new MerchantActivityDetailMultiStatusAdRqt();
        getActivityDetailRqt.setUserId(getActivityDetailApiRqt.getUserId());
        getActivityDetailRqt.setActivityId(getActivityDetailApiRqt.getActivityId());

        try {
            activityDetail = landingPageServiceApi.getMerchantActivityDetailMultiStatusAd(getActivityDetailRqt);
        } catch (ApiAccessException e) {

            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
        try {
            //组合活动返回信息为空，特殊处理
            if(Objects.isNull(activityDetail)){
                return null;
            }
            //基础信息
            MerchantActivityDetailMultiStatusAdApiResp.ActivityBaseInfo activityBaseInfo = new MerchantActivityDetailMultiStatusAdApiResp.ActivityBaseInfo();
            MerchantActivityDetailMultiStatusAdResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            //待领奖信息
            getActivityDetailApiResp.setRewardInfoRespList(this.buildRewardInfo(activityDetail.getRewardInfoList(),getActivityDetailApiRqt.getUserClass()));
            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));
            //礼盒图片先默认写死，后续加配置表
            activityBaseInfo.setTaskNotFinishGiftImgUrl(ActivityConstant.TASK_NO_FINISH_GIFT_IMG_URL);
            activityBaseInfo.setTaskFinishGiftImgUrl(ActivityConstant.TASK_FINISHED_GIFT_IMG_URL);
            String activityUrl = "";
            if (!org.springframework.util.StringUtils.isEmpty(activityBaseInfo1.getActivityUrl())) {
                activityUrl = activityBaseInfo1.getActivityUrl() + "&t=" + getActivityDetailApiRqt.getIsWeb();
            }
            activityBaseInfo.setActivityUrl(activityUrl);
            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);
            getActivityDetailApiResp.setShowTaskStatus(2);
            //设置关闭按钮状态,该广告关联了活动，当前切换了活动状态，且设置的活动任务非每日循环（按照当前任务查询即可）
            getActivityDetailApiResp.setClosable(1);
            //任务列表赋值
            List<MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo> activityTaskRespLists = activityDetail.getActivityTaskLists();
            if (CollectionUtils.isEmpty(activityTaskRespLists)) {
                return getActivityDetailApiResp;
            }
            //查询所有待领奖的任务id列表
            List<Long> userActivityTaskIdList = activityTaskRespLists.stream().filter(f -> ActivityButtonStateEnum.NOT_CLAIMED.code.equals(f.getButtonStatus())).map(m -> m.getUserActivityTaskId()).collect(Collectors.toList());
            //过滤不符合条件的任务
            Iterator<MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo> activityTaskRespListIterator = activityTaskRespLists.iterator();
            while (activityTaskRespListIterator.hasNext()) {
                MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo activityTaskVo = activityTaskRespListIterator.next();
                //判断是否选中
                if (Objects.isNull(activityTaskVo.getIsChoice()) || 0 == activityTaskVo.getIsChoice()) {
                    activityTaskRespListIterator.remove();
                }
            }
            if (CollectionUtils.isEmpty(activityTaskRespLists)) {
                return getActivityDetailApiResp;
            }
            MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo selectedTaskVo = null;
            //判断是否有未领奖的任务:取最后一个未领取任务
            Optional<MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo> activityTaskBoOptional = activityTaskRespLists.stream().filter(f -> ActivityButtonStateEnum.NOT_CLAIMED.code.equals(f.getButtonStatus())).
                    sorted(Comparator.comparing(MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo::getActivityTaskId).reversed()).findFirst();
            if (activityTaskBoOptional.isPresent()) {
                selectedTaskVo = activityTaskBoOptional.get();
                getActivityDetailApiResp.setShowTaskStatus(1);
            } else {
                //取未完成任务第一个
                Optional<MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo> activityTaskUnfinishOptional = activityTaskRespLists.stream().filter(f -> ActivityButtonStateEnum.NOT_COMPLETE.code.equals(f.getButtonStatus())).
                        sorted(Comparator.comparing(MerchantActivityDetailMultiStatusAdResp.ActivityTaskBo::getActivityTaskId)).findFirst();
                if (activityTaskUnfinishOptional.isPresent()) {
                    selectedTaskVo = activityTaskUnfinishOptional.get();
                    getActivityDetailApiResp.setShowTaskStatus(0);
                }
            }

            List<PackageItemBo> packageItemBoList = null;
            //如果是多选一奖励,需要获取多选一奖励信息
            if(Objects.nonNull(selectedTaskVo) && RewardSymbol.COMBINE_CHOOSE_REWARD.type.equals(selectedTaskVo.getRewardSymbol())){
                GetCombineRewardConfigRqt queryRqt = new GetCombineRewardConfigRqt();
                queryRqt.setId(selectedTaskVo.getRewardExtraId());
                CombineRewardConfig combineRewardConfig = combinedPageServiceApi.getCombineRewardConfig(queryRqt);
                packageItemBoList = JSONArray.parseArray(combineRewardConfig.getPackageList(), PackageItemBo.class);
            }

            //提前批量查询现金券及卷包
            Map<Long, VoucherRewardBo> voucherBoMap = new HashMap<>();
            Map<Long, VoucherRewardBo> voucherPackBoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList()) || Objects.nonNull(selectedTaskVo)) {
                List<Integer> queryVoucherIdList = new ArrayList<>();
                List<Integer> queryVoucherPackIdList = new ArrayList<>();

                if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList())) {
                    List<Integer> voucherIdList = activityDetail.getActivityRewardGiveItemList().stream().filter(f -> RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol())).map(m -> m.getRewardExtraId().intValue()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(voucherIdList)) {
                        queryVoucherIdList.addAll(voucherIdList);
                    }
                }
                if (Objects.nonNull(selectedTaskVo) && RewardSymbol.VOUCHER.type.equals(selectedTaskVo.getRewardSymbol())) {
                    queryVoucherIdList.add(selectedTaskVo.getRewardExtraId().intValue());
                }

                if(CollectionUtils.isNotEmpty(packageItemBoList)){
                    for(PackageItemBo bo : packageItemBoList){
                        if(RewardSymbol.VOUCHER.type.equals(bo.getRewardSymbol())){
                            queryVoucherIdList.add(bo.getRewardExtraId());
                        }
                        if(RewardSymbol.VOUCHER_PACK.type.equals(bo.getRewardSymbol())){
                            queryVoucherPackIdList.add(bo.getRewardExtraId());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList())) {
                    List<Integer> voucherPackIdList = activityDetail.getActivityRewardGiveItemList().stream().filter(f -> RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol())).map(m -> m.getRewardExtraId().intValue()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(voucherPackIdList)) {
                        queryVoucherPackIdList.addAll(voucherPackIdList);
                    }
                }
                if (Objects.nonNull(selectedTaskVo) && RewardSymbol.VOUCHER_PACK.type.equals(selectedTaskVo.getRewardSymbol())) {
                    queryVoucherPackIdList.add(selectedTaskVo.getRewardExtraId().intValue());
                }

                voucherBoMap = super.getOnlyVoucherBatchGetReward(queryVoucherIdList);
                voucherPackBoMap = super.batchQueryVoucherPackValue(queryVoucherPackIdList);
            }
            //已获取奖励文案
            if (CollectionUtils.isNotEmpty(activityDetail.getActivityRewardGiveItemList())) {
                getActivityDetailApiResp.setActivityAllGiveRewardText(this.returnActivityAllGiveRewardTextNew(activityDetail.getActivityRewardGiveItemList(), voucherBoMap, voucherPackBoMap));
            }
            if (Objects.isNull(selectedTaskVo)) {
                return getActivityDetailApiResp;
            }
            //返回任务赋值开始------
            MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo activityRespTaskVo = new MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo();

            BeanUtils.copyProperties(selectedTaskVo, activityRespTaskVo);
            activityRespTaskVo.setUserActivityTaskIdList(userActivityTaskIdList);

            List<MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo> combinedVoList = null;
            //奖励价值赋值，优惠券类型
            if (selectedTaskVo.getRewardExtraId() != null && selectedTaskVo.getRewardExtraId() > 0) {
                if(RewardSymbol.COMBINE_CHOOSE_REWARD.type.equals(selectedTaskVo.getRewardSymbol())){
                    combinedVoList = buildCombinedTaskVoDatail(packageItemBoList,voucherBoMap,voucherPackBoMap);
                } else {
                    VoucherRewardBo voucherVo = RewardSymbol.VOUCHER.type.equals(selectedTaskVo.getRewardSymbol()) ? voucherBoMap.get(selectedTaskVo.getRewardExtraId()) : voucherPackBoMap.get(selectedTaskVo.getRewardExtraId());
                    if (voucherVo != null) {
                        activityRespTaskVo.setVoucherType(voucherVo.getVoucherType());
                        activityRespTaskVo.setRewardValue(voucherVo.getRewardValue());
                        if (RewardSymbol.VOUCHER_PACK.type.equals(selectedTaskVo.getRewardSymbol())) {
                            BigDecimal packNum = Objects.nonNull(selectedTaskVo.getRewardGiveValue()) && selectedTaskVo.getRewardGiveValue().compareTo(BigDecimal.ZERO) > 0 ? selectedTaskVo.getRewardGiveValue() : new BigDecimal("1");
                            BigDecimal rewardValue = voucherVo.getRewardValue().multiply(packNum).setScale(2, BigDecimal.ROUND_HALF_UP);
                            activityRespTaskVo.setRewardValue(rewardValue);
                        }
                    }
                }
            } else {
                //非优惠券奖励类型
                activityRespTaskVo.setRewardValue(selectedTaskVo.getRewardGiveValue());
            }
            if (1 == activityBaseInfo.getIsActivityStart()) {
                //判断是否选中
                if (1 == activityRespTaskVo.getIsChoice()) {
                    if (3 == selectedTaskVo.getButtonStatus() || 0 == selectedTaskVo.getButtonStatus()) {
                        activityRespTaskVo.setSecondsRemain(0L);
                    } else {
                        activityRespTaskVo.setSecondsRemain(this.secondsRemain(activityBaseInfo.getActivityEndTime(), selectedTaskVo.getTimeLimit().longValue(), selectedTaskVo.getApplyTime()));
                    }
                }
            } else {
                activityRespTaskVo.setSecondsRemain(0L);
            }

            //没报名倒计时返回0
            if (0 == activityBaseInfo.getIsApply()) {
                activityRespTaskVo.setSecondsRemain(0L);
            }
            //还差xx文案、奖励文案赋值
            NeedRewardAndCurrentPercentBo returnBo = this.returnNeedRewardTextDetail(activityRespTaskVo, getActivityDetailApiResp.getShowTaskStatus());
            String finishRewardText = this.returnFinishRewardTextDetail(activityRespTaskVo.getTaskSymbol(),activityRespTaskVo.getTargetQuantityValue(),activityRespTaskVo.getTargetAmountValue(), getActivityDetailApiResp.getShowTaskStatus());
            activityRespTaskVo.setNeedRewardText(returnBo.getNeedRewardText());
            activityRespTaskVo.setFinishRewardText(finishRewardText);
            activityRespTaskVo.setRewardText(this.returnRewardTextDetail(activityRespTaskVo,combinedVoList));
            activityRespTaskVo.setCurrentTaskPercent(returnBo.getCurrentTaskPercent());
            //设置关闭按钮状态,该广告关联了活动，当前切换了活动状态，且设置的活动任务非每日循环（按照当前任务查询即可）
            Integer closable = 1;
            if(TaskExecuteTypeEnum.DAY.type.equals(activityRespTaskVo.getExecuteType())){
                closable = 0;
            }
            getActivityDetailApiResp.setClosable(closable);

            getActivityDetailApiResp.setActivityTaskVo(activityRespTaskVo);
            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusException("get_activity_detail_fail", e.getMessage());
        }
    }

    /**
     * 构建待领奖信息
     * @param rewardListRespSimplePageInfo
     * @return
     */
    private List<RewardInfoVo> buildUnclaimedRewardInfo(List<MerchantCenterRewardListSubResp> rewardListRespSimplePageInfo, String userClass){
        if(CollectionUtils.isEmpty(rewardListRespSimplePageInfo)){
            return null;
        }

        List<RewardInfoVo> unclaimedRewardInfoList = new ArrayList<>();
        //优惠券及券包信息查询
        List<ActivityTaskVoucherBo> queryBo = new ArrayList<>();
        for (MerchantCenterRewardListSubResp merchantCenterRewardListResp : rewardListRespSimplePageInfo) {
            List<OptionalRewardInfoBo> optionalRewardInfoList = merchantCenterRewardListResp.getOptionalRewardInfoList();
            if(CollectionUtils.isEmpty(optionalRewardInfoList)){
                continue;
            }
            for(OptionalRewardInfoBo infoBo : optionalRewardInfoList){
                if(!RewardSymbol.VOUCHER.type.equals(infoBo.getRewardSymbol()) && !RewardSymbol.VOUCHER_PACK.type.equals(infoBo.getRewardSymbol())){
                    continue;
                }
                ActivityTaskVoucherBo activityTaskVoucherBo = new ActivityTaskVoucherBo();
                activityTaskVoucherBo.setActivityTaskId(merchantCenterRewardListResp.getActivityTaskId());
                activityTaskVoucherBo.setRewardSymbol(infoBo.getRewardSymbol());
                activityTaskVoucherBo.setRewardExtraId(infoBo.getRewardExtraId());
                queryBo.add(activityTaskVoucherBo);
            }
        }
        Map<Long, VoucherRewardBo> voucherMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(queryBo)){
            voucherMap = this.voucherBatchGetRewardValueSuper(queryBo, userClass);
        }

        for (MerchantCenterRewardListSubResp merchantCenterRewardListResp : rewardListRespSimplePageInfo) {
            RewardInfoVo unclaimedRewardInfo = new RewardInfoVo();
            BeanUtils.copyProperties(merchantCenterRewardListResp, unclaimedRewardInfo);

            List<OptionalRewardInfoBo> optionalRewardInfoList = merchantCenterRewardListResp.getOptionalRewardInfoList();
            if(CollectionUtils.isEmpty(optionalRewardInfoList)){
                unclaimedRewardInfoList.add(unclaimedRewardInfo);
                continue;
            }

            List<OptionalRewardInfoVo> optionalRewardInfoVoList = new ArrayList<>();
            //如果多选一奖励里有优惠券/券包,需要设置相应信息
            for(OptionalRewardInfoBo infoBo : optionalRewardInfoList){
                OptionalRewardInfoVo vo = new OptionalRewardInfoVo();
                BeanUtils.copyProperties(infoBo, vo);
                if((!RewardSymbol.VOUCHER.type.equals(vo.getRewardSymbol()) && !RewardSymbol.VOUCHER_PACK.type.equals(vo.getRewardSymbol())) ||
                        (Objects.isNull(vo.getRewardExtraId())) || vo.getRewardExtraId() == 0){
                    continue;
                }
                VoucherRewardBo voucherRewardBo = voucherMap.get(vo.getRewardExtraId());
                if(Objects.isNull(voucherRewardBo)){
                    continue;
                }
                vo.setVoucherType(voucherRewardBo.getVoucherType());

                vo.setRewardValue(this.getRewardValue(voucherRewardBo));
                optionalRewardInfoVoList.add(vo);
            }
            unclaimedRewardInfo.setOptionalRewardInfoList(optionalRewardInfoVoList);
            unclaimedRewardInfoList.add(unclaimedRewardInfo);
        }
        return unclaimedRewardInfoList;
    }



    /**
     * 构建多选一奖励taskVo
     * @param packageItemBoList
     * @param voucherBoMap
     * @param voucherPackBoMap
     * @return
     */
    private List<MerchantActivityDetailBannerAdApiResp.ActivityTaskVo> buildCombinedTaskVo(List<PackageItemBo> packageItemBoList, Map<Long, VoucherRewardBo> voucherBoMap, Map<Long, VoucherRewardBo> voucherPackBoMap){
        List<MerchantActivityDetailBannerAdApiResp.ActivityTaskVo> combinedVoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(packageItemBoList)){
            return combinedVoList;
        }
        
        for(PackageItemBo bo : packageItemBoList){
            MerchantActivityDetailBannerAdApiResp.ActivityTaskVo vo = new MerchantActivityDetailBannerAdApiResp.ActivityTaskVo();
            vo.setRewardSymbol(bo.getRewardSymbol());
            if(RewardSymbol.VOUCHER.type.equals(bo.getRewardSymbol())){
                VoucherRewardBo voucherBo = voucherBoMap.get(Long.valueOf(bo.getRewardExtraId()));
                if(Objects.isNull(voucherBo)){
                    continue;
                }
                vo.setVoucherType(voucherBo.getVoucherType());
                vo.setRewardValue(voucherBo.getRewardValue());
            } else if(RewardSymbol.VOUCHER_PACK.type.equals(bo.getRewardSymbol())){
                VoucherRewardBo voucherPackBo = voucherPackBoMap.get(Long.valueOf(bo.getRewardExtraId()));
                if(Objects.isNull(voucherPackBo)){
                    continue;
                }
                BigDecimal packNum = new BigDecimal("1");
                BigDecimal rewardValue = voucherPackBo.getRewardValue().multiply(packNum).setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setRewardValue(rewardValue);
            } else if(RewardSymbol.COMBINE_CHOOSE_REWARD.type.equals(bo.getRewardSymbol()) && RewardSymbol.COMBINE.type.equals(bo.getRewardSymbol())){
                log.error("多选一奖励数据异常:多选一奖励包含组合奖励");
                throw new BusException("多选一奖励数据异常");
            } else {
                vo.setRewardValue(bo.getGiveValue());
            }
            combinedVoList.add(vo);
        }
        return combinedVoList;
    }
    /**
     * 构建多选一奖励taskVo
     * @param packageItemBoList
     * @param voucherBoMap
     * @param voucherPackBoMap
     * @return
     */
    private List<MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo> buildCombinedTaskVoDatail(List<PackageItemBo> packageItemBoList, Map<Long, VoucherRewardBo> voucherBoMap, Map<Long, VoucherRewardBo> voucherPackBoMap){
        List<MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo> combinedVoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(packageItemBoList)){
            return combinedVoList;
        }

        for(PackageItemBo bo : packageItemBoList){
            MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo vo = new MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo();
            vo.setRewardSymbol(bo.getRewardSymbol());
            if(RewardSymbol.VOUCHER.type.equals(bo.getRewardSymbol())){
                VoucherRewardBo voucherBo = voucherBoMap.get(Long.valueOf(bo.getRewardExtraId()));
                if(Objects.isNull(voucherBo)){
                    continue;
                }
                vo.setVoucherType(voucherBo.getVoucherType());
                vo.setRewardValue(voucherBo.getRewardValue());
            } else if(RewardSymbol.VOUCHER_PACK.type.equals(bo.getRewardSymbol())){
                VoucherRewardBo voucherPackBo = voucherPackBoMap.get(Long.valueOf(bo.getRewardExtraId()));
                if(Objects.isNull(voucherPackBo)){
                    continue;
                }
                BigDecimal packNum = new BigDecimal("1");
                BigDecimal rewardValue = voucherPackBo.getRewardValue().multiply(packNum).setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setRewardValue(rewardValue);
            } else if(RewardSymbol.COMBINE_CHOOSE_REWARD.type.equals(bo.getRewardSymbol()) && RewardSymbol.COMBINE.type.equals(bo.getRewardSymbol())){
                log.error("多选一奖励数据异常:多选一奖励包含组合奖励");
                throw new BusException("多选一奖励数据异常");
            } else {
                vo.setRewardValue(bo.getGiveValue());
            }
            combinedVoList.add(vo);
        }
        return combinedVoList;
    }
    /**
     * 返回用户在该活动获取奖励文案
     *
     * @param activityRewardGiveItemList
     * @return
     */
    private String returnActivityAllGiveRewardText(List<MerchantActivityDetailBannerAdResp.ActivityRewardGiveItemVo> activityRewardGiveItemList, Map<Long, VoucherRewardBo> voucherBoMap, Map<Long, VoucherRewardBo> voucherPackBoMap) {
        StringBuilder builder = new StringBuilder("您已通过活动领取");
        Map<String, List<MerchantActivityDetailBannerAdResp.ActivityRewardGiveItemVo>> rewardSymbolGroupMap = activityRewardGiveItemList.stream().collect(Collectors.groupingBy(it -> it.getRewardSymbol()));

        rewardSymbolGroupMap.forEach((symbol, rewardGiveItemVoList) -> {
            RewardSymbol rewardSymbol = RewardSymbol.fromType(symbol);
            switch (rewardSymbol) {
                case VOUCHER:
                    List<Long> voucherIdList = rewardGiveItemVoList.stream().map(m -> m.getRewardExtraId()).collect(Collectors.toList());
                    BigDecimal voucherTotalAmount = BigDecimal.ZERO;
                    for (Long voucherId : voucherIdList) {
                        VoucherRewardBo bo = voucherBoMap.get(voucherId);
                        if (Objects.nonNull(bo)) {
                            //折扣券取最大面额
                            BigDecimal addAmount = ActivityConstant.VOUCHER_DISCOUNT.equals(bo.getVoucherType()) ? bo.getDiscountMaxValue() : bo.getRewardValue();
                            voucherTotalAmount = voucherTotalAmount.add(addAmount).setScale(2, RoundingMode.HALF_UP);
                        }
                    }
                    if (voucherTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        builder.append("价值").append(MathUtils.decimalCutZero(voucherTotalAmount)).append("元优惠券、");
                    }
                    break;
                case VOUCHER_PACK:
//                    List<Long> voucherPackIdList = rewardGiveItemVoList.stream().map(m -> m.getRewardExtraId()).collect(Collectors.toList());
                    BigDecimal voucherPackTotalAmount = BigDecimal.ZERO;
                    for (MerchantActivityDetailBannerAdResp.ActivityRewardGiveItemVo rewardGiveItemVo : rewardGiveItemVoList) {
                        VoucherRewardBo bo = voucherPackBoMap.get(rewardGiveItemVo.getRewardExtraId());
                        if (Objects.nonNull(bo)) {
                            BigDecimal packNum = Objects.nonNull(rewardGiveItemVo.getGiveValue()) && rewardGiveItemVo.getGiveValue().compareTo(BigDecimal.ZERO) > 0 ? rewardGiveItemVo.getGiveValue() : new BigDecimal("1");
                            BigDecimal addValue = bo.getRewardValue().multiply(packNum);
                            voucherPackTotalAmount = voucherPackTotalAmount.add(addValue).setScale(2, RoundingMode.HALF_UP);
                        }
                    }
                    if (voucherPackTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        builder.append("价值").append(MathUtils.decimalCutZero(voucherPackTotalAmount)).append("元优惠礼包、");
                    }
                    break;
                case COIN:
                    int coinTotal = rewardGiveItemVoList.stream().mapToInt(m -> m.getGiveValue().intValue()).sum();
                    if (coinTotal > 0) {
                        builder.append(coinTotal).append("金币、");
                    }
                    break;
                case SECURITY_FUND:
                    BigDecimal securityFundAmount = BigDecimal.ZERO;
                    for (MerchantActivityDetailBannerAdResp.ActivityRewardGiveItemVo vo : rewardGiveItemVoList) {
                        securityFundAmount = securityFundAmount.add(vo.getGiveValue()).setScale(2, RoundingMode.HALF_UP);
                    }
                    if (securityFundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        builder.append(MathUtils.decimalCutZero(securityFundAmount)).append("元服务保障金、");
                    }
                    break;
                case LOTTERY_DRAW:
                    int lotteryDrawTimes = rewardGiveItemVoList.stream().mapToInt(m -> m.getGiveValue().intValue()).sum();
                    if (lotteryDrawTimes > 0) {
                        builder.append(lotteryDrawTimes).append("次金币商城抽奖机会、");
                    }
                    break;
                default:
                    break;
            }
        });
        String resultText = builder.toString();
        if (resultText.endsWith("、")) {
            resultText = resultText.substring(0, resultText.length() - 1);
        }
        return resultText;
    }
    /**
     * 返回用户在该活动获取奖励文案
     *
     * @param activityRewardGiveItemList
     * @return
     */
    private String returnActivityAllGiveRewardTextNew(List<MerchantActivityDetailMultiStatusAdResp.ActivityRewardGiveItemVo> activityRewardGiveItemList, Map<Long, VoucherRewardBo> voucherBoMap, Map<Long, VoucherRewardBo> voucherPackBoMap) {
        StringBuilder builder = new StringBuilder("您已通过活动领取");
        Map<String, List<MerchantActivityDetailMultiStatusAdResp.ActivityRewardGiveItemVo>> rewardSymbolGroupMap = activityRewardGiveItemList.stream().collect(Collectors.groupingBy(it -> it.getRewardSymbol()));

        rewardSymbolGroupMap.forEach((symbol, rewardGiveItemVoList) -> {
            RewardSymbol rewardSymbol = RewardSymbol.fromType(symbol);
            switch (rewardSymbol) {
                case VOUCHER:
                    List<Long> voucherIdList = rewardGiveItemVoList.stream().map(m -> m.getRewardExtraId()).collect(Collectors.toList());
                    BigDecimal voucherTotalAmount = BigDecimal.ZERO;
                    for (Long voucherId : voucherIdList) {
                        VoucherRewardBo bo = voucherBoMap.get(voucherId);
                        if (Objects.nonNull(bo)) {
                            //折扣券取最大面额
                            BigDecimal addAmount = ActivityConstant.VOUCHER_DISCOUNT.equals(bo.getVoucherType()) ? bo.getDiscountMaxValue() : bo.getRewardValue();
                            voucherTotalAmount = voucherTotalAmount.add(addAmount).setScale(2, RoundingMode.HALF_UP);
                        }
                    }
                    if (voucherTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        builder.append("").append(MathUtils.decimalCutZero(voucherTotalAmount)).append("元优惠券、");
                    }
                    break;
                case VOUCHER_PACK:
                    //                    List<Long> voucherPackIdList = rewardGiveItemVoList.stream().map(m -> m.getRewardExtraId()).collect(Collectors.toList());
                    BigDecimal voucherPackTotalAmount = BigDecimal.ZERO;
                    for (MerchantActivityDetailMultiStatusAdResp.ActivityRewardGiveItemVo rewardGiveItemVo : rewardGiveItemVoList) {
                        VoucherRewardBo bo = voucherPackBoMap.get(rewardGiveItemVo.getRewardExtraId());
                        if (Objects.nonNull(bo)) {
                            BigDecimal packNum = Objects.nonNull(rewardGiveItemVo.getGiveValue()) && rewardGiveItemVo.getGiveValue().compareTo(BigDecimal.ZERO) > 0 ? rewardGiveItemVo.getGiveValue() : new BigDecimal("1");
                            BigDecimal addValue = bo.getRewardValue().multiply(packNum);
                            voucherPackTotalAmount = voucherPackTotalAmount.add(addValue).setScale(2, RoundingMode.HALF_UP);
                        }
                    }
                    if (voucherPackTotalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        builder.append("").append(MathUtils.decimalCutZero(voucherPackTotalAmount)).append("元优惠礼包、");
                    }
                    break;
                case COIN:
                    int coinTotal = rewardGiveItemVoList.stream().mapToInt(m -> m.getGiveValue().intValue()).sum();
                    if (coinTotal > 0) {
                        builder.append(coinTotal).append("金币、");
                    }
                    break;
                case SECURITY_FUND:
                    BigDecimal securityFundAmount = BigDecimal.ZERO;
                    for (MerchantActivityDetailMultiStatusAdResp.ActivityRewardGiveItemVo vo : rewardGiveItemVoList) {
                        securityFundAmount = securityFundAmount.add(vo.getGiveValue()).setScale(2, RoundingMode.HALF_UP);
                    }
                    if (securityFundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        builder.append(MathUtils.decimalCutZero(securityFundAmount)).append("元服务保障金、");
                    }
                    break;
                case LOTTERY_DRAW:
                    int lotteryDrawTimes = rewardGiveItemVoList.stream().mapToInt(m -> m.getGiveValue().intValue()).sum();
                    if (lotteryDrawTimes > 0) {
                        builder.append(lotteryDrawTimes).append("次金币商城抽奖机会、");
                    }
                    break;
                default:
                    break;
            }
        });
        String resultText = builder.toString();
        if (resultText.endsWith("、")) {
            resultText = resultText.substring(0, resultText.length() - 1);
        }
        return resultText;
    }
    /**
     * 奖励文案
     *
     * @param activityRespTaskVo
     * @param combinedVoList
     * @return
     */
    private String returnRewardText(MerchantActivityDetailBannerAdApiResp.ActivityTaskVo activityRespTaskVo,
                                    List<MerchantActivityDetailBannerAdApiResp.ActivityTaskVo> combinedVoList) {
        StringBuilder rewardTextBuilder = new StringBuilder();
        String rewardValueStr = Objects.nonNull(activityRespTaskVo.getRewardValue()) ? MathUtils.decimalCutZero(activityRespTaskVo.getRewardValue()) : "0";
        RewardSymbol rewardSymbol = RewardSymbol.fromType(activityRespTaskVo.getRewardSymbol());
        switch (rewardSymbol) {
            case VOUCHER:
                //xx元优惠券/x.x折优惠券
                String amountAndUnitStr = ActivityConstant.VOUCHER_DISCOUNT.equals(activityRespTaskVo.getVoucherType()) ? "折优惠券" : "元优惠券";
                //折扣券可能有95折，9折2种情况:直接干掉0.即可
                if (ActivityConstant.VOUCHER_DISCOUNT.equals(activityRespTaskVo.getVoucherType())) {
                    rewardValueStr = rewardValueStr.split("\\.")[1];
                }
                rewardTextBuilder.append(rewardValueStr).append(amountAndUnitStr);
                break;
            case VOUCHER_PACK:
                rewardTextBuilder.append(rewardValueStr).append("元优惠券礼包");
                break;
            case COIN:
                rewardTextBuilder.append(rewardValueStr).append("金币");
                break;
            case SECURITY_FUND:
                rewardTextBuilder.append(rewardValueStr).append("元服务保障金");
                break;
            case LOTTERY_DRAW:
                rewardTextBuilder.append(rewardValueStr).append("次金币商城抽奖机会");
                break;
            case COMBINE_CHOOSE_REWARD:
                if(CollectionUtils.isEmpty(combinedVoList)){
                    break;
                }
                for(int i = 0; i<combinedVoList.size(); i++){
                    MerchantActivityDetailBannerAdApiResp.ActivityTaskVo vo = combinedVoList.get(i);
                    if(i>0){
                        rewardTextBuilder.append(",");
                    }
                    rewardTextBuilder.append(this.returnRewardText(vo,null));
                }
                break;
            default:
                break;
        }
        return rewardTextBuilder.toString();
    }
    /**
     * 奖励文案
     *
     * @param activityRespTaskVo
     * @param combinedVoList
     * @return
     */
    private String returnRewardTextDetail(MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo activityRespTaskVo,
            List<MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo> combinedVoList) {
        StringBuilder rewardTextBuilder = new StringBuilder();
        String rewardValueStr = Objects.nonNull(activityRespTaskVo.getRewardValue()) ? MathUtils.decimalCutZero(activityRespTaskVo.getRewardValue()) : "0";
        RewardSymbol rewardSymbol = RewardSymbol.fromType(activityRespTaskVo.getRewardSymbol());
        switch (rewardSymbol) {
            case VOUCHER:
                //xx元优惠券/x.x折优惠券
                String amountAndUnitStr = ActivityConstant.VOUCHER_DISCOUNT.equals(activityRespTaskVo.getVoucherType()) ? "折优惠券" : "元优惠券";
                //折扣券可能有95折，9折2种情况:直接干掉0.即可
                if (ActivityConstant.VOUCHER_DISCOUNT.equals(activityRespTaskVo.getVoucherType())) {
                    rewardValueStr = rewardValueStr.split("\\.")[1];
                }
                rewardTextBuilder.append(rewardValueStr).append(amountAndUnitStr);
                break;
            case VOUCHER_PACK:
                rewardTextBuilder.append(rewardValueStr).append("元优惠券礼包");
                break;
            case COIN:
                rewardTextBuilder.append(rewardValueStr).append("金币");
                break;
            case SECURITY_FUND:
                rewardTextBuilder.append(rewardValueStr).append("元服务保障金");
                break;
            case LOTTERY_DRAW:
                rewardTextBuilder.append(rewardValueStr).append("次金币商城抽奖机会");
                break;
            case COMBINE_CHOOSE_REWARD:
                if(CollectionUtils.isEmpty(combinedVoList)){
                    break;
                }
                for(int i = 0; i<combinedVoList.size(); i++){
                    MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo vo = combinedVoList.get(i);
                    if(i>0){
                        rewardTextBuilder.append(",");
                    }
                    rewardTextBuilder.append(this.returnRewardTextDetail(vo,null));
                }
                break;
            default:
                break;
        }
        return rewardTextBuilder.toString();
    }
    public static void main(String[] args) {
        String rewardValueStr = "0.78";
        rewardValueStr = rewardValueStr.split("\\.")[1];
        System.out.println(rewardValueStr);
    }

    /**
     * 还差xx文案:下单还差xx单可领奖
     *
     * @param activityRespTaskVo
     * @return
     */
    private NeedRewardAndCurrentPercentBo returnNeedRewardText(MerchantActivityDetailBannerAdApiResp.ActivityTaskVo activityRespTaskVo, Integer showTaskStatus) {
        NeedRewardAndCurrentPercentBo returnBo = new NeedRewardAndCurrentPercentBo();
        //任务完成不走下面代码
        if (showTaskStatus != null && showTaskStatus == 1) {
            returnBo.setCurrentTaskPercent("100%");
            returnBo.setNeedRewardText("");
            return returnBo;
        }
        String taskName = "";
        String taskNeedTimesAndUnit = "";
        String needAmountValueStr = "";
        String currentAmountTaskPercent = "";
        String currentQuantityTaskPercent = "";
        int needQuantityValue = 0;
        //金额
        if (Objects.nonNull(activityRespTaskVo.getTargetAmountValue()) && activityRespTaskVo.getTargetAmountValue().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal needAmountValue = activityRespTaskVo.getTargetAmountValue().subtract(activityRespTaskVo.getCurrentAmountValue());
            needAmountValueStr = MathUtils.decimalCutZero(needAmountValue);
            BigDecimal percentDecimal = activityRespTaskVo.getCurrentAmountValue().multiply(new BigDecimal(100)).divide(activityRespTaskVo.getTargetAmountValue(), 2, BigDecimal.ROUND_UP);
            currentAmountTaskPercent = MathUtils.decimalCutZero(percentDecimal) + "%";

        }
        //次数
        if (Objects.nonNull(activityRespTaskVo.getTargetQuantityValue()) && activityRespTaskVo.getTargetQuantityValue().compareTo(BigDecimal.ZERO) > 0) {
            needQuantityValue = activityRespTaskVo.getTargetQuantityValue().intValue() - activityRespTaskVo.getCurrentQuantityValue();
            BigDecimal percentDecimal = new BigDecimal(activityRespTaskVo.getCurrentQuantityValue().toString()).multiply(new BigDecimal(100)).divide(activityRespTaskVo.getTargetQuantityValue(), 2, BigDecimal.ROUND_UP);
            currentQuantityTaskPercent = MathUtils.decimalCutZero(percentDecimal) + "%";
        }
        TaskSymbolEnum taskSymbolEnum = TaskSymbolEnum.getTaskSymBolEnumBySymbol(activityRespTaskVo.getTaskSymbol());
        switch (taskSymbolEnum) {
            case CREATE_ORDER:
                taskName = "下单";
                taskNeedTimesAndUnit = needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(currentQuantityTaskPercent);
                break;
            case APPOINT_MASTER:
                taskName = "指派";
                taskNeedTimesAndUnit = needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(currentQuantityTaskPercent);
                break;
            case ORDER_PAY:
                taskName = "付款";
                taskNeedTimesAndUnit = StringUtils.isNotEmpty(needAmountValueStr) ? needAmountValueStr + "元" : needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(StringUtils.isNotEmpty(needAmountValueStr) ? currentAmountTaskPercent : currentQuantityTaskPercent);
                break;
            case ORDER_CHECK:
                taskName = "验收";
                taskNeedTimesAndUnit = StringUtils.isNotEmpty(needAmountValueStr) ? needAmountValueStr + "元" : needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(StringUtils.isNotEmpty(needAmountValueStr) ? currentAmountTaskPercent : currentQuantityTaskPercent);
                break;
            default:
                break;
        }
        returnBo.setNeedRewardText(String.format(ActivityConstant.NEED_REWARD_TEXT_TEMPLATE, taskName, taskNeedTimesAndUnit));
        return returnBo;
    }
    /**
     * 还差xx文案:下单还差xx单可领奖
     *
     * @param activityRespTaskVo
     * @return
     */
    private NeedRewardAndCurrentPercentBo returnNeedRewardTextDetail(MerchantActivityDetailMultiStatusAdApiResp.ActivityTaskVo activityRespTaskVo, Integer showTaskStatus) {
        NeedRewardAndCurrentPercentBo returnBo = new NeedRewardAndCurrentPercentBo();
        //任务完成不走下面代码
        if (showTaskStatus != null && showTaskStatus == 1) {
            returnBo.setCurrentTaskPercent("100%");
            returnBo.setNeedRewardText("");
            return returnBo;
        }
        String taskName = "";
        String taskNeedTimesAndUnit = "";
        String needAmountValueStr = "";
        String currentAmountTaskPercent = "";
        String currentQuantityTaskPercent = "";
        int needQuantityValue = 0;
        //金额
        if (Objects.nonNull(activityRespTaskVo.getTargetAmountValue()) && activityRespTaskVo.getTargetAmountValue().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal needAmountValue = activityRespTaskVo.getTargetAmountValue().subtract(activityRespTaskVo.getCurrentAmountValue());
            needAmountValueStr = MathUtils.decimalCutZero(needAmountValue);
            BigDecimal percentDecimal = activityRespTaskVo.getCurrentAmountValue().multiply(new BigDecimal(100)).divide(activityRespTaskVo.getTargetAmountValue(), 2, BigDecimal.ROUND_UP);
            currentAmountTaskPercent = MathUtils.decimalCutZero(percentDecimal) + "%";

        }
        //次数
        if (Objects.nonNull(activityRespTaskVo.getTargetQuantityValue()) && activityRespTaskVo.getTargetQuantityValue().compareTo(BigDecimal.ZERO) > 0) {
            needQuantityValue = activityRespTaskVo.getTargetQuantityValue().intValue() - activityRespTaskVo.getCurrentQuantityValue();
            BigDecimal percentDecimal = new BigDecimal(activityRespTaskVo.getCurrentQuantityValue().toString()).multiply(new BigDecimal(100)).divide(activityRespTaskVo.getTargetQuantityValue(), 2, BigDecimal.ROUND_UP);
            currentQuantityTaskPercent = MathUtils.decimalCutZero(percentDecimal) + "%";
        }
        TaskSymbolEnum taskSymbolEnum = TaskSymbolEnum.getTaskSymBolEnumBySymbol(activityRespTaskVo.getTaskSymbol());
        switch (taskSymbolEnum) {
            case CREATE_ORDER:
                taskName = "下单";
                taskNeedTimesAndUnit = needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(currentQuantityTaskPercent);
                break;
            case APPOINT_MASTER:
                taskName = "指派";
                taskNeedTimesAndUnit = needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(currentQuantityTaskPercent);
                break;
            case ORDER_PAY:
                taskName = "付款";
                taskNeedTimesAndUnit = StringUtils.isNotEmpty(needAmountValueStr) ? needAmountValueStr + "元" : needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(StringUtils.isNotEmpty(needAmountValueStr) ? currentAmountTaskPercent : currentQuantityTaskPercent);
                break;
            case ORDER_CHECK:
                taskName = "验收";
                taskNeedTimesAndUnit = StringUtils.isNotEmpty(needAmountValueStr) ? needAmountValueStr + "元" : needQuantityValue + "单";
                returnBo.setCurrentTaskPercent(StringUtils.isNotEmpty(needAmountValueStr) ? currentAmountTaskPercent : currentQuantityTaskPercent);
                break;
            default:
                break;
        }
        returnBo.setNeedRewardText(String.format(ActivityConstant.NEED_REWARD_TEXT_TEMPLATE, taskName, taskNeedTimesAndUnit));
        return returnBo;
    }
    /**
     * 已完成文案
     *
     * @param
     * @return
     */
    private String returnFinishRewardTextDetail(String taskSymbol, BigDecimal targetQuantityValue,BigDecimal targetAmountValue,Integer showTaskStatus) {
        //任务未完成不走下面代码
        if (showTaskStatus != null && showTaskStatus == 0) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        TaskSymbolEnum taskSymbolEnum = TaskSymbolEnum.getTaskSymBolEnumBySymbol(taskSymbol);
        String taskNeedTimesAndUnit = "";
        boolean isAmount = false;
        if (Objects.nonNull(targetAmountValue) && targetAmountValue.compareTo(BigDecimal.ZERO) > 0) {
            isAmount = true;
        }
        switch (taskSymbolEnum) {
            case CREATE_ORDER:
                taskNeedTimesAndUnit = targetQuantityValue + "单";
                stringBuilder.append(String.format(ActivityConstant.FINISH_REWARD_TEXT_TEMPLATE,taskNeedTimesAndUnit , "下单"));
                break;
            case APPOINT_MASTER:
                taskNeedTimesAndUnit = targetQuantityValue + "单";
                stringBuilder.append(String.format(ActivityConstant.FINISH_REWARD_TEXT_TEMPLATE,taskNeedTimesAndUnit , "指派"));
                break;
            case ORDER_PAY:

                taskNeedTimesAndUnit = isAmount ? targetAmountValue + "元" : targetQuantityValue + "单";
                stringBuilder.append(String.format(ActivityConstant.FINISH_REWARD_TEXT_TEMPLATE,taskNeedTimesAndUnit , "付款"));
                break;
            case ORDER_CHECK:
                taskNeedTimesAndUnit = isAmount ? targetAmountValue + "元" : targetQuantityValue + "单";
                stringBuilder.append(String.format(ActivityConstant.FINISH_REWARD_TEXT_TEMPLATE,taskNeedTimesAndUnit , "验收"));
                break;
            default:
                break;
        }
        return stringBuilder.toString();
    }
    /**
     * 构建技能树
     *
     * @param resp
     * @param orderLimit
     */
    private void setSkillTree(GetActivityTaskOrderLimitApiResp resp, GetActivityTaskOrderLimitResp orderLimit) {
//        if (StringUtils.isNotEmpty(orderLimit.getFirstSkillIds()) && StringUtils.isNotEmpty(orderLimit.getSecondSkillIds())) {
//            String skillIds = super.createSkillStrByExtend(orderLimit.getFirstSkillIds(), orderLimit.getSecondSkillIds());
//            //根据服务id获取订单限制的技能
//            try {
////                TechniqueQueryReq techniqueQueryByServeIdsReq = new TechniqueQueryReq();
////                techniqueQueryByServeIdsReq.setTechniqueIds(skillIds);
////                List<Technique> techniques = techniqueServiceApi.query(techniqueQueryByServeIdsReq);
//
//                TechniqueQueryRqt techniqueQueryRqt = new TechniqueQueryRqt();
//                techniqueQueryRqt.setTechniqueIds(skillIds);
//                List<Technique> techniques = techniqueServiceApi.query(techniqueQueryRqt);
//                if (CollectionUtils.isEmpty(techniques)) {
//                    throw new BusinessException("没有查询出对应的技能信息");
//                }
//                //整理技能树
//                resp.setTechnologyIdsName(this.createTechnologyTwo(techniques));
//            } catch (Exception e) {
//                log.error(e.getMessage());
//            }
//        }
    }

    /**
     * 设置下单模式
     *
     * @param appointMethod
     * @return
     */
    private String returnAppointMethodName(String appointMethod) {
        if (StringUtils.isEmpty(appointMethod)) {
            return ActivityConstant.NOT_LIMIT_CN;
        }
        return AppointMethodEnum.getNameByTypeList(Arrays.asList(appointMethod.split(",")));
    }

    /**
     * 设置订单类型/服务商类型名称
     *
     * @param fromAccountType
     * @param spType
     * @return
     */
    private String returnAccountTypeName(String fromAccountType, String spType) {
        if (StringUtils.isNotEmpty(fromAccountType)) {
            return AccountTypeNameEnum.getAccountTypeNameByType(fromAccountType);
        }
        if (StringUtils.isNotEmpty(spType)) {
            return AccountTypeNameEnum.getAccountTypeNameByType(spType);
        }
        return ActivityConstant.NOT_LIMIT_CN;
    }

    /***
     * @Description：获取匹配订单信息
     * @param: customOrderTagRqt
     * @return：
     */
    @Override
    @UserLoginInfo
    public CustomOrderListTagApiResp customOrderListTag(CustomOrderTagRqt customOrderTagRqt) {
        CustomOrderListTagApiResp customOrderListTagApiResp = new CustomOrderListTagApiResp();
        customOrderListTagApiResp.setSwitchTime(switchTime);
        //师傅token
        Long masterId = customOrderTagRqt.getUserId();
        if (masterId == null) {
            masterId = this.getMasterIdBySignature(customOrderTagRqt.getSignature());
        }
        //请求 订单配置列表信息数据
        GetFilterOrdersRqt getFilterOrdersRqt = new GetFilterOrdersRqt();
        getFilterOrdersRqt.setMasterId(masterId);
        getFilterOrdersRqt.setQueryNumber(queryNumber);
        List<GetFilterOrdersResp> filterOrderList = iocActivityApi.getFilterOrders(
                getFilterOrdersRqt);
        if (CollectionUtils.isEmpty(filterOrderList)) {
            customOrderListTagApiResp.setMatchOrderFlag(0);
            return customOrderListTagApiResp;
        }

        CustomOrderTagBo customOrderTagBo = createCustomOrderTagBo(customOrderTagRqt,
                filterOrderList,
                masterId,
                customOrderTagRqt.getActivityId());
        CustomOrderTagResp customOrderTagResp = new CustomOrderTagResp();
        try {
            customOrderTagResp = landingPageServiceApi.customOrderListTag(customOrderTagBo);
        } catch (ExecutionException | InterruptedException e) {
            e.printStackTrace();
        }
        if (customOrderTagResp == null) {
            return customOrderListTagApiResp;
        }

        //返回师傅缺失的技能列表
        if (0 == customOrderTagResp.getMatchOrderFlag()) {
            customOrderListTagApiResp.setMatchOrderFlag(0);
            List<String> serveIds = customOrderTagResp.getServeIds();
            //如果返回为null
            if (!CollectionUtils.isEmpty(serveIds)) {
                Set<String> set = new HashSet<>();
                for (String serve : serveIds) {
                    List<String> serveIdList = JSONArray.parseArray(serve, String.class);
                    set.addAll(serveIdList);
                }
                List<String> serveList = new ArrayList<>(set);
                String serverId = com.wanshifu.framework.utils.StringUtils.listToCommaSplit(
                        serveList.stream().map(String::valueOf).collect(
                                Collectors.toList()));
                //根据服务id获取订单限制的技能
//                TechniqueQueryByServeIdsReq techniqueQueryByServeIdsReq = new TechniqueQueryByServeIdsReq();
//                techniqueQueryByServeIdsReq.setServeIds(serverId);
//                List<Technique> techniques = techniqueServiceApi.queryByServeIdsPost(
//                        techniqueQueryByServeIdsReq);

                QueryByServeIdsRqt queryByServeIdsRqt = new QueryByServeIdsRqt();
                queryByServeIdsRqt.setServeIds(serverId);
                List<Technique> techniques = techniqueServiceApi.queryByServeIds(
                        queryByServeIdsRqt);
                if (CollectionUtils.isEmpty(techniques)) {
                    log.warn("没有查询出对应的技能id");

                    set = null;
                    serveIds = null;
                    serveList = null;
                    serverId = null;

                    return customOrderListTagApiResp;
                }
                //整理技能树
                setTechnologyIdsName(techniques, masterId, customOrderListTagApiResp);
            }
            return customOrderListTagApiResp;
        }
        //处理匹配订单
        return getMatchOrderFlagInfo(customOrderListTagApiResp, masterId,
                customOrderTagResp.getMatchOrderIds());
    }

    /***
     * @Description：
     * @param: customOrderListTagApiResp
     * @param: masterId
     * @return：
     */
    private CustomOrderListTagApiResp getMatchOrderFlagInfo(
            CustomOrderListTagApiResp customOrderListTagApiResp, Long masterId,
            List<Long> matchOrderIds) {
        customOrderListTagApiResp.setMatchOrderFlag(1);
        GetWaitOfferRqt getWaitOfferRqt = new GetWaitOfferRqt();
        //获取匹配的订单id
        getWaitOfferRqt.setOrderIds(matchOrderIds);
        getWaitOfferRqt.setMasterId(masterId);
        //请求 订单基本信息，获取数据
        List<GetWaitOfferResp> getWaitOfferResps = iocActivityApi.getWaitOffer(getWaitOfferRqt);
        if (CollectionUtils.isEmpty(getWaitOfferResps)) {
            throw new BusException("没有查询到对应的订单基本信息");
        }
        List<String> imageAids = getWaitOfferResps.stream().filter(f->Objects.nonNull(f.getGoodsImageAid())&&f.getGoodsImageAid()>0).map(GetWaitOfferResp::getGoodsImageAid)
                .map(String::valueOf).collect(Collectors.toList());
        Map<Long, String> imageAidMap = httpImageUtils.sendPostRequest(imageAids);
        List<CustomOrderListTagApiResp.MasterOrderListItem> masterOrderListItemList = getWaitOfferResps.stream()
                .map(
                        getWaitOfferResp -> {
                            MasterOrderListItem masterOrderListItem = new MasterOrderListItem();
                            BeanUtils.copyProperties(getWaitOfferResp, masterOrderListItem);
                            masterOrderListItem.setGoodsImageUrl(
                                    imageAidMap.get(masterOrderListItem.getGoodsImageAid()));
                            GetWaitOfferResp.OrderAddressInfoDTO orderAddressInfoDTO = getWaitOfferResp.getOrderAddressInfo();
                            CustomOrderListTagApiResp.MasterOrderListItem.orderAddressInfo orderAddressInfo =
                                    new orderAddressInfo();
                            BeanUtils.copyProperties(orderAddressInfoDTO, orderAddressInfo);
                            masterOrderListItem.setOrderAddressInfo(orderAddressInfo);
                            masterOrderListItem.setDistanceValue(
                                    orderAddressInfoDTO.getDistanceValue());
                            return masterOrderListItem;
                        }
                ).collect(Collectors.toList());
        //按照距离排序
        //实现排序
        masterOrderListItemList.sort(new Comparator<MasterOrderListItem>() {
            @Override
            public int compare(MasterOrderListItem u1, MasterOrderListItem u2) {
                if (u1.getDistanceValue() != null && u2.getDistanceValue() != null) {
                    //return -1:即为正序排序
                    return u1.getDistanceValue().compareTo(u2.getDistanceValue());
                } else {
                    //return 1: 即为倒序排序
                    return u1.getDistanceValue() == null ? 1 : -1;
                }
            }
        });
        imageAids = null;
        customOrderListTagApiResp.setMasterOrderListItemList(masterOrderListItemList);
        //组装数据返回。
        return customOrderListTagApiResp;
    }

    /***
     * @Description：整理技能树
     * @param: techniques
     * @return：
     */
    private void setTechnologyIdsName(List<Technique> techniques, Long masterId,
                                      CustomOrderListTagApiResp customOrderListTagApiResp) {
        //二级技能id集合
        List<Long> level2ids = techniques.stream().map(Technique::getLevel2Id)
                .collect(Collectors.toList());

        //获取师傅已验证的技能
        GetMasterInfoRqtBean getMasterInfoRqtBean = new GetMasterInfoRqtBean();
        getMasterInfoRqtBean.setMasterId(masterId);
        getMasterInfoRqtBean.setIncludeAvatar(false);
        GetMasterInfoRspBean getMasterInfoRspBean = masterInfoApi.getMasterInfo(
                getMasterInfoRqtBean);
        if (getMasterInfoRspBean == null || StringUtil.isEmpty(
                getMasterInfoRspBean.getPassTechnologyIds())) {
            log.warn("没有查询出对应的技能id,masterId = {}", masterId);
            return;
        }
        //师傅技能id
        List<Long> passTechnologyIds = com.wanshifu.framework.utils.StringUtils.splitCommaToList(
                        getMasterInfoRspBean.getPassTechnologyIds())
                .stream().map(Long::valueOf).collect(Collectors.toList());
        //取得差集。
        List<Long> notMatchlevel2ids = level2ids.stream().filter(
                techniqueId -> {
                    return !passTechnologyIds.contains(techniqueId);
                }
        ).collect(Collectors.toList());
        ;

        //notMatchlevel2ids
        customOrderListTagApiResp.setTechnologyIds(JSONObject.toJSONString(notMatchlevel2ids));
        List<Technique> notMatchLevelList = techniques.stream().filter(
                technique -> notMatchlevel2ids.contains(technique.getLevel2Id())
        ).collect(Collectors.toList());
        List<Technology> technologies = createTechnology(notMatchLevelList);
        customOrderListTagApiResp.setTechnologyIdsName(technologies);

    }

    /***
     * @Description：根据活动id返回活动模型
     * @param: activityDetailApiRqt
     * @return：
     */
    @Override
    public String getActivityModelById(ActivityDetailApiRqt activityDetailApiRqt) {
        ActivityDetailByIdRqt activityDetailByIdRqt = new ActivityDetailByIdRqt();

        activityDetailByIdRqt.setActivityId(activityDetailApiRqt.getActivityId());
        ActivityBase activityBase = activityBusinessServiceApi.getActivityDetailById(
                activityDetailByIdRqt);
        if (null == activityBase) {
            throw new BusException("活动信息不存在");
        }
        return activityBase.getActivityModel();
    }

    /***
     * @Description：获取师傅订单活动落地页详情
     * @param: masterOrderLandingPageRqt
     * @return：
     */
    @Override
    public MasterOrderTaskApiResp getMasterOrderTaskLandingPage(MasterOrderLandingApiPageRqt masterOrderLandingPageRqt) {
        //是否是app
        boolean isApp = ActivityConstant.IS_APP.equals(masterOrderLandingPageRqt.getIsWeb());
        if (!appEnable && isApp) {
            return null;
        }
        Long userId = masterOrderLandingPageRqt.getUserId();
        if (StringUtils.isNotEmpty(masterOrderLandingPageRqt.getSignature())) {
            userId = this.getMasterIdBySignature(masterOrderLandingPageRqt.getSignature());
        }

        boolean isLogin = (userId != null);
        //实例化活动详情
        MasterOrderTaskApiResp masterOrderTaskApiResp = new MasterOrderTaskApiResp();
        masterOrderTaskApiResp.setLogin(isLogin);
        MasterOrderTaskResp activityDetail = null;
        if (isLogin) {
            //判断用户登陆状态
            MasterOrderTaskRqt getActivityDetailRqt = new MasterOrderTaskRqt();
            BeanUtils.copyProperties(masterOrderLandingPageRqt, getActivityDetailRqt);
            getActivityDetailRqt.setUserId(userId);
            try {
                activityDetail = landingPageServiceApi.getMasterOrderTaskLandingPage(getActivityDetailRqt);
            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
            }

        } else {
            PageGetActivityDetailRqt getActivityDetailNoLoginRqt = new PageGetActivityDetailRqt();
            BeanUtils.copyProperties(masterOrderLandingPageRqt, getActivityDetailNoLoginRqt);
            try {
                activityDetail = (MasterOrderTaskResp) this.coverObject(landingPageServiceApi.getActivityDetailNoLogin(getActivityDetailNoLoginRqt), MasterOrderTaskResp.class);
            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());

            }
        }
        try {
            //基础信息
            MasterOrderTaskApiResp.ActivityBaseInfo activityBaseInfo = new MasterOrderTaskApiResp.ActivityBaseInfo();
            MasterOrderTaskResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");
            //待领奖数，如果自动领奖，待领奖数是0
            if (OperateMethodEnum.AUTO.code.equals(activityDetail.getActivityBaseInfo().getRewardGiveMethod())) {
                masterOrderTaskApiResp.setAwarding(0);
            } else {
                masterOrderTaskApiResp.setAwarding(activityDetail.getAwarding());
            }

            masterOrderTaskApiResp.setHasAwarding(activityDetail.getHasAwarding());

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));

            masterOrderTaskApiResp.setActivityBaseInfo(activityBaseInfo);


            //落地页配置信息
            MasterOrderTaskApiResp.LandingPageInfo landingPageInfo = new MasterOrderTaskApiResp.LandingPageInfo();
            MasterOrderTaskResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);

            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    imageAidList.add(Long.valueOf(aid));
                }
            }

            Collections.addAll(imageAidList,
                    landingPageInfo1.getPreviewImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getTopImageAid(),
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getRewardImageAid()
            );

            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());
            //获取服务   todo 服务批量缓存
            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setPreviewImageUrl(imageResultMap.get(landingPageInfo1.getPreviewImageAid()));
            landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));
            landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));

            landingPageInfo.setRewardTimageUrl(imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            List<String> activityDetailList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    activityDetailList.add(imageResultMap.getOrDefault(Long.valueOf(aid), ""));
                }
            }

            landingPageInfo.setActivityModelDetail(activityDetailList);
            masterOrderTaskApiResp.setLandingPageInfo(landingPageInfo);

            //任务列表

            MasterOrderTaskApiResp.ActivityBaseInfo activityBaseInfo2 = masterOrderTaskApiResp.getActivityBaseInfo();

            masterOrderTaskApiResp.setActivityBaseInfo(activityBaseInfo2);
            masterOrderTaskApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);
            return masterOrderTaskApiResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
    }

    /***
     * @Description：获取商家有奖活动落地页详情
     * @param: metchantPrizeLandingPageRqt
     * @return：
     */
    @Override
    public MetchantPrizeTaskApiResp getMerchantPrizeTaskLandingPage(MetchantPrizeLandingPageRqt metchantPrizeLandingPageRqt) {
        //是否是app
        boolean isApp = ActivityConstant.IS_APP.equals(metchantPrizeLandingPageRqt.getIsWeb());
        if (!appEnable && isApp) {
            return null;
        }
        Long userId = this.getMerchantUserId(super.getCookiesToken(), super.getAppToken());
        //是否登陆
        boolean isLogin = !org.springframework.util.StringUtils.isEmpty(userId);
        //web网页直接报错。
        if (!isLogin && !isApp) {
            throw new BusException("not_login", "请先登录");
        }
        //实例化活动详情
        MetchantPrizeTaskApiResp getActivityDetailApiResp = new MetchantPrizeTaskApiResp();
        MerchantPrizeTaskResp activityDetail = null;
        getActivityDetailApiResp.setLogin(isLogin);
        if (isLogin) {
            //判断用户登陆状态、
            MerchantPrizeTaskTaskRqt getActivityDetailRqt = new MerchantPrizeTaskTaskRqt();
            BeanUtils.copyProperties(metchantPrizeLandingPageRqt, getActivityDetailRqt);
            getActivityDetailRqt.setUserId(userId);
            try {
                activityDetail = landingPageServiceApi.getPrizeTaskLandingPage(getActivityDetailRqt);
            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
            }

        } else {
            PageGetActivityDetailRqt getActivityDetailNoLoginRqt = new PageGetActivityDetailRqt();
            BeanUtils.copyProperties(metchantPrizeLandingPageRqt, getActivityDetailNoLoginRqt);
            try {
                activityDetail = (MerchantPrizeTaskResp) this.coverObject(landingPageServiceApi.getActivityDetailNoLogin(getActivityDetailNoLoginRqt), MerchantPrizeTaskResp.class);
            } catch (ApiAccessException e) {
                throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
            }
        }
        try {
            //基础信息
            MetchantPrizeTaskApiResp.ActivityBaseInfo activityBaseInfo = new MetchantPrizeTaskApiResp.ActivityBaseInfo();
            MerchantPrizeTaskResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");
            //待领奖数，如果自动领奖，待领奖数是0
            if (OperateMethodEnum.AUTO.code.equals(activityDetail.getActivityBaseInfo().getRewardGiveMethod())) {
                getActivityDetailApiResp.setAwarding(0);
            } else {
                getActivityDetailApiResp.setAwarding(activityDetail.getAwarding());
            }

            getActivityDetailApiResp.setHasAwarding(activityDetail.getHasAwarding());

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));

            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);


            //落地页配置信息
            MetchantPrizeTaskApiResp.LandingPageInfo landingPageInfo = new MetchantPrizeTaskApiResp.LandingPageInfo();
            MerchantPrizeTaskResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);

            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = activityDetail.getActivityTaskLists().stream().map(MerchantPrizeTaskResp.ActivityTaskBo::getRewardImageAid).filter(Objects::nonNull).collect(Collectors.toList());

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    imageAidList.add(Long.valueOf(aid));
                }
            }

            Collections.addAll(imageAidList,
                    landingPageInfo1.getPreviewImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getTopImageAid(),
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getRewardImageAid()
            );

            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());
            //服务map<服务id,服务名>
            List<MerchantPrizeTaskResp.ActivityTaskBo> activityTaskLists1 = activityDetail.getActivityTaskLists();
            List<Long> serveIdList = new ArrayList<>();
//            List<Long> familyServeIdList = new ArrayList<>();
            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setPreviewImageUrl(imageResultMap.get(landingPageInfo1.getPreviewImageAid()));
            landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));
            if (isApp || (metchantPrizeLandingPageRqt.getIsH5() != null && metchantPrizeLandingPageRqt.getIsH5() == 1)) {
                landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));
            } else {
                landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getTopImageAid()));
            }


            landingPageInfo.setRewardTimageUrl(imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            List<String> activityDetailList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    activityDetailList.add(imageResultMap.getOrDefault(Long.valueOf(aid), ""));
                }
            }

            landingPageInfo.setActivityModelDetail(activityDetailList);

            getActivityDetailApiResp.setLandingPageInfo(landingPageInfo);

            //实例化任务列表
            List<MetchantPrizeTaskApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();
            //提前批量查询现金券及卷包
            List<ActivityTaskVoucherBo> extraIdList = activityDetail.getActivityTaskLists().stream().
                    filter(f -> (RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) || RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()))
                            && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                    map(m -> {
                        ActivityTaskVoucherBo activityTaskVoucherBo = new ActivityTaskVoucherBo();
                        activityTaskVoucherBo.setActivityTaskId(m.getActivityTaskId());
                        activityTaskVoucherBo.setRewardExtraId(m.getRewardExtraId());
                        activityTaskVoucherBo.setRewardSymbol(m.getRewardSymbol());

                        return activityTaskVoucherBo;
                    }).distinct().collect(Collectors.toList());
            Map<Long, VoucherRewardBo> voucherRewardBoMap = this.voucherBatchGetRewardValueSuper(extraIdList, UserTypeEnum.MERCHANT.type);

            //任务列表 再处理
            for (MerchantPrizeTaskResp.ActivityTaskBo activityTaskList : activityDetail.getActivityTaskLists()) {
                MetchantPrizeTaskApiResp.ActivityTaskList activityTaskListTmp = new MetchantPrizeTaskApiResp.ActivityTaskList();

                if (activityTaskList.getRewardImageAid() != null) {
                    activityTaskListTmp.setRewardImageUrl(imageResultMap.get(activityTaskList.getRewardImageAid()));
                }
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);


                if (isApp || (metchantPrizeLandingPageRqt.getIsH5() != null && metchantPrizeLandingPageRqt.getIsH5() == 1)) {
                    activityTaskListTmp.setButtonSkipUrl(activityTaskList.getButtonSkipUrlMap().get("app"));
                } else {
                    activityTaskListTmp.setButtonSkipUrl(activityTaskList.getButtonSkipUrlMap().get("web"));
                }
                //奖励价值赋值，优惠券类型
                if (voucherRewardBoMap != null && activityTaskList.getRewardExtraId() != null && activityTaskList.getRewardExtraId() > 0) {
                    VoucherRewardBo voucherVo = voucherRewardBoMap.get(activityTaskList.getRewardExtraId());
                    if (voucherVo != null) {
                        activityTaskListTmp.setVoucherType(voucherVo.getVoucherType());
                        BigDecimal rewardValue = this.getRewardValueOfRuleConfig(voucherVo.getRewardValue(), activityTaskList);
                        //如果是优惠券礼包，并且是奖励公式
                        //非优惠券奖励类型
                        if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherVo.getVoucherType())) {
                            //折扣需要先乘10
                            String rewardValueStr = MathUtils.decimalCutZero(rewardValue.multiply(new BigDecimal(10)));
                            BigDecimal remainder = new BigDecimal(rewardValueStr).remainder(new BigDecimal(10));
                            if (remainder.compareTo(BigDecimal.ZERO) == 0) {
                                rewardValue = new BigDecimal(rewardValue.intValue());
                            } else {
                                rewardValue = new BigDecimal(rewardValueStr);
                            }
                        }
                        //如果是优惠券礼包，并且是奖励公式
                        //非优惠券奖励类型
                        activityTaskListTmp.setRewardValue(rewardValue);
                    }
                } else {
                    //非优惠券奖励类型
                    activityTaskListTmp.setRewardValue(activityTaskList.getRewardGiveValue());
                }
                if (activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER.type)
                        || activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER_PACK.type)) {
                    //非优惠券奖励类型
                    //非优惠券奖励类型
                    activityTaskListTmp.setRewardGiveValue(activityTaskListTmp.getRewardValue());
                }

                //付款/验收如果是返点需要计算具体值
                if (activityTaskListTmp.getTaskSymbol().equals(TaskTypeEnum.ORDER_PAY.symbol)
                        || activityTaskListTmp.getTaskSymbol().equals(TaskTypeEnum.ORDER_CHECK.symbol)) {
                    if (activityTaskListTmp.getRewardGiveType().equals(RewardGiveTypeEnum.RETURN_POINT.type)) {
                        //并且是返点类型
                        BigDecimal divide = activityTaskListTmp.getRewardValue().multiply(activityTaskListTmp.getTargetAmountValue()).divide(new BigDecimal(100));
                        activityTaskListTmp.setRewardGiveValue(divide);
                    }
                }

                BigDecimal rewardGiveValue = activityTaskListTmp.getRewardGiveValue();

                if (rewardGiveValue.compareTo(new BigDecimal("100")) > 0) {
                    activityTaskListTmp.setRewardGiveValue(rewardGiveValue.setScale(0, RoundingMode.HALF_DOWN));
                } else {
                    activityTaskListTmp.setRewardGiveValue(rewardGiveValue.setScale(2, RoundingMode.HALF_DOWN));
                }
                //是否展示订单限制
                activityTaskListTmp.setIsShowOrderLimit(TaskSymbolEnum.isShowOrderLimit(activityTaskListTmp.getTaskSymbol()));

                activityTaskListTmp.setRewardType(activityTaskList.getRewardConfigId());

                if (activityTaskList.getUserActivityTask() != null) {
                    activityTaskListTmp.setUserActivityTaskId(activityTaskList.getUserActivityTask().getUserActivityTaskId());
                }
                //设置奖励文案
                TagTextBo tagTextBo = this.splitTagText(activityTaskList.getTagText());
                if (tagTextBo != null) {
                    activityTaskListTmp.setCustomizeRewardText(tagTextBo.getCustomizeRewardText());
                    activityTaskListTmp.setRewardTextType(tagTextBo.getRewardTextType());
                    activityTaskListTmp.setCustomizeTargetText(tagTextBo.getCustomizeTargetText());
                }
                //设置任务文案 xx内完成1单
                if (isLogin) {
                    activityTaskListTmp.setTitle(this.getTaskTitle(activityTaskList.getTaskSymbol(), activityTaskList.getUserActivityTask(),
                            activityTaskListTmp.getCustomizeTargetText(),
                            activityTaskListTmp.getTargetQuantityValue()));
                } else {
                    activityTaskListTmp.setTitle(TaskTypeEnum.getTaskTypeCn(activityTaskList.getTaskSymbol()) + ActivityConstant.text_task_default);
                }

                //设置任务描述 最多可领取3次
                activityTaskListTmp.setTitleDesc(this.getTaskDescByTask(activityTaskList.getActivityTaskInfo()));

                if (activityBaseInfo.getIsActivityStart() == 1) {
                    if (activityTaskList.getButtonStatus() == 3 || activityTaskList.getButtonStatus() == 0) {
                        activityTaskListTmp.setSecondsRemain(0L);
                    } else {
                        activityTaskListTmp.setSecondsRemain(this.secondsRemain(getActivityDetailApiResp.getActivityBaseInfo().getActivityEndTime(), activityTaskList.getTimeLimit().longValue(), activityTaskList.getApplyTime()));
                    }
                } else {
                    activityTaskListTmp.setSecondsRemain(0L);
                }

                //没报名倒计时返回0
                if (activityBaseInfo.getIsApply() == 0) {
                    activityTaskListTmp.setSecondsRemain(0L);
                }
                if (!org.springframework.util.StringUtils.isEmpty(activityTaskListTmp.getTargetAmountValueRange())) {
                    activityTaskListTmp.setTargetAmountValueRange(activityTaskListTmp.getTargetAmountValueRange().replace("[", "").replace("]", ""));
                }


                activityTaskLists.add(activityTaskListTmp);
            }

            //任务列表
            getActivityDetailApiResp.setActivityTaskLists(activityTaskLists);

            List<Integer> collect = activityTaskLists.stream().map(MetchantPrizeTaskApiResp.ActivityTaskList::getButtonStatus).collect(Collectors.toList());

            MetchantPrizeTaskApiResp.ActivityBaseInfo activityBaseInfo2 = getActivityDetailApiResp.getActivityBaseInfo();


            if (RewardGiveStrategyEnum.MULTIPLE.code.equals(activityBaseInfo2.getRewardGiveStrategy())) {
                if ((!collect.contains(2)) && (!collect.contains(3)) && (!collect.contains(4)) && activityTaskLists.size() > 0) {
                    activityBaseInfo2.setHasReceivedReward(1);
                } else {
                    activityBaseInfo2.setHasReceivedReward(0);
                }
            }

            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo2);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);

            //判断奖励是否都领完
            List<MetchantPrizeTaskApiResp.ActivityTaskList> rewardCompleted = activityTaskLists.stream().filter(f -> f.getButtonStatus().equals(ActivityButtonStateEnum.Collected.code)
                    || f.getButtonStatus().equals(ActivityButtonStateEnum.CollectedComplete.code)
                    || f.getButtonStatus().equals(ActivityButtonStateEnum.CollectedCompleteALl.code)
            ).collect(Collectors.toList());

            if (rewardCompleted.size() == activityTaskLists.size()) {
                getActivityDetailApiResp.setRewardCompleted(1);
            }


            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
    }

    private BigDecimal getRewardValueOfRuleConfig(BigDecimal rewardValue,
                                                  MerchantPrizeTaskResp.ActivityTaskBo activityTaskList) {
        //是否设置了奖励公式
        if (activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER_PACK.type)) {
            if (activityTaskList.getRuleConfigId() != null && null != activityTaskList.getRuleConfigResultValue()) {
                rewardValue = rewardValue.multiply(activityTaskList.getRuleConfigResultValue());
            } else {
                rewardValue = rewardValue.multiply(activityTaskList.getRewardGiveValue());
            }
        }
        return rewardValue;
    }


    /***
     * @Description：获取商家充值专用活动落地页详情
     * @param: merchantRechargeTaskLandingPageRqt
     * @return：
     */
    @Override
    public MerchantRechargeTaskApiResp getMerchantRechargeTaskLandingPage(MerchantRechargeTaskLandingPageRqt merchantRechargeTaskLandingPageRqt) {
        //是否是app
        boolean isApp = ActivityConstant.IS_APP.equals(merchantRechargeTaskLandingPageRqt.getIsWeb());
        if (!appEnable && isApp) {
            return null;
        }
        Long userId = this.getMerchantUserId(super.getCookiesToken(), super.getAppToken());
        //是否登陆
        boolean isLogin = !org.springframework.util.StringUtils.isEmpty(userId);
        //web网页直接报错。
        if (!isLogin && !isApp) {
            throw new BusException("not_login", "请先登录");
        }
        //实例化活动详情
        MerchantRechargeTaskApiResp getActivityDetailApiResp = new MerchantRechargeTaskApiResp();
        MerchantRechargeTaskResp activityDetail = null;
        getActivityDetailApiResp.setLogin(isLogin);
        try {
            if (isLogin) {
                //判断用户登陆状态、
                MerchantRechargeTaskRqt getActivityDetailRqt = new MerchantRechargeTaskRqt();
                BeanUtils.copyProperties(merchantRechargeTaskLandingPageRqt, getActivityDetailRqt);
                getActivityDetailRqt.setUserId(userId);
                activityDetail = landingPageServiceApi.getRechargeTaskLandingPage(getActivityDetailRqt);
            } else {
                PageGetActivityDetailRqt getActivityDetailNoLoginRqt = new PageGetActivityDetailRqt();
                BeanUtils.copyProperties(merchantRechargeTaskLandingPageRqt, getActivityDetailNoLoginRqt);
                activityDetail = (MerchantRechargeTaskResp) this.coverObject(landingPageServiceApi.getActivityDetailNoLogin(getActivityDetailNoLoginRqt), MerchantRechargeTaskResp.class);
            }
        } catch (ApiAccessException e) {
            throw new BusinessException(e.getRetMesg());
        }
        try {
            //基础信息
            MerchantRechargeTaskApiResp.ActivityBaseInfo activityBaseInfo = new MerchantRechargeTaskApiResp.ActivityBaseInfo();
            MerchantRechargeTaskResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");
            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);

            //落地页配置信息 todo 落地页地址，图片统一方法赋值
            MerchantRechargeTaskApiResp.LandingPageInfo landingPageInfo = new MerchantRechargeTaskApiResp.LandingPageInfo();
            MerchantRechargeTaskResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);
            //获取 图片
            Map<Long, String> imageResultMapNew = new HashMap<>(10);
            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = activityDetail.getActivityTaskLists().stream().map(MerchantRechargeTaskResp.ActivityTaskBo::getRewardImageAid).filter(Objects::nonNull).collect(Collectors.toList());

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    imageAidList.add(Long.valueOf(aid));
                }
            }

            Collections.addAll(imageAidList,
                    landingPageInfo1.getPreviewImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getTopImageAid(),
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getRewardImageAid()
            );

            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());
            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setPreviewImageUrl(imageResultMap.get(landingPageInfo1.getPreviewImageAid()));
            landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));
            landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getTopImageAid()));
            landingPageInfo.setAppTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));

            landingPageInfo.setRewardTimageUrl(imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            List<String> activityDetailList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    activityDetailList.add(imageResultMap.getOrDefault(Long.valueOf(aid), ""));
                }
            }

            landingPageInfo.setActivityModelDetail(activityDetailList);
            getActivityDetailApiResp.setLandingPageInfo(landingPageInfo);
            //实例化任务列表
            List<MerchantRechargeTaskApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();
            //提前批量查询现金券及卷包
            Map<Long, VoucherRewardBo> voucherRewardBoMap = this.voucherBatchGetRewardValueSuper(this.getExtadIdList(activityDetail.getActivityTaskLists()), UserTypeEnum.MERCHANT.type);
            //获取奖励配置参数 todo
            List<RewardConfigResp> rewardConfigApiResps = super.getRewardConfigList(UserTypeEnum.MERCHANT.type, ActivityTypeEnum.ORDER_TASK.getActivityType());
            //获取奖励总上限标记
            boolean isUpperUserActivityLimit = activityBaseInfo.isUpperUserActivityLimit();
            //任务列表赋值
            for (MerchantRechargeTaskResp.ActivityTaskBo activityTaskList : activityDetail.getActivityTaskLists()) {
                MerchantRechargeTaskApiResp.ActivityTaskList activityTaskListTmp = new MerchantRechargeTaskApiResp.ActivityTaskList();
//                if (activityTaskList.getRewardImageAid() != null) {
//                    activityTaskListTmp.setRewardImageUrl(imageResultMap.get(activityTaskList.getRewardImageAid()));
//                }
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);

                List<MerchantRechargeTaskApiResp.ActivityTaskList.TaskRewardResp> taskApiRewardRespList = this.createTaskRewardRespList(activityTaskList.getTaskRewardRespList(), voucherRewardBoMap, activityTaskList, rewardConfigApiResps);
                //设置奖励文案
                TagTextBo tagTextBo = this.splitTagText(activityTaskList.getTagText());
                if (tagTextBo != null) {
                    activityTaskListTmp.setCustomizeRewardText(tagTextBo.getCustomizeRewardText());
                    activityTaskListTmp.setRewardTextType(tagTextBo.getRewardTextType());
                }
                if (!org.springframework.util.StringUtils.isEmpty(activityTaskListTmp.getTargetAmountValueRange())) {
                    activityTaskListTmp.setTargetAmountValueRange(activityTaskListTmp.getTargetAmountValueRange().replace("[", "").replace("]", ""));
                }
                activityTaskListTmp.setTaskRewardRespList(taskApiRewardRespList);
                //判断是否已经到达总上限，其余正常按钮状态置灰
                if (isUpperUserActivityLimit && ActivityConstant.YES_TRUE == activityTaskListTmp.getButtonStatus()) {
                    activityTaskListTmp.setButtonStatus(ActivityConstant.NO_FALSE);
                    activityTaskListTmp.setIsShowRecharge(ActivityConstant.NO_FALSE);
                }
                activityTaskLists.add(activityTaskListTmp);
            }
            //任务列表
            getActivityDetailApiResp.setActivityTaskLists(activityTaskLists);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);

            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
    }

    /***
     * @Description：获取商家多状态广告位
     * @param: getActivityDetailRqt
     * @return：
     */
    @Override
    @UserLoginInfo
    public MerchantActivityDetailAdApiResp getMerchantActivityDetailAd(GetActivityDetailApiRqt getActivityDetailApiRqt) {
        //实例化活动详情
        MerchantActivityDetailAdApiResp getActivityDetailApiResp = new MerchantActivityDetailAdApiResp();
        MerchantActivityDetailAdResp activityDetail = null;

        GetActivityDetailRqt getActivityDetailRqt = new GetActivityDetailRqt();
        getActivityDetailRqt.setUserId(getActivityDetailApiRqt.getUserId());
        getActivityDetailRqt.setActivityId(getActivityDetailApiRqt.getActivityId());

        try {
            activityDetail = landingPageServiceApi.getMerchantActivityDetailAd(getActivityDetailRqt);
        } catch (ApiAccessException e) {
            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
        }
        try {
            //组合活动返回信息为空，特殊处理
            if(Objects.isNull(activityDetail)){
                return null;
            }
            //基础信息
            MerchantActivityDetailAdApiResp.ActivityBaseInfo activityBaseInfo = new MerchantActivityDetailAdApiResp.ActivityBaseInfo();
            MerchantActivityDetailAdResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            String activityUrl = "";
            if (!org.springframework.util.StringUtils.isEmpty(activityBaseInfo1.getActivityUrl())) {
                activityUrl = activityBaseInfo1.getActivityUrl() + "&t=" + getActivityDetailApiRqt.getIsWeb();
            }
            //兼容自定义落地页地址
//            if(StringUtils.isEmpty(activityUrl)){
//                activityUrl="https://test-pages.wanshifu.com/web/p_4p48VXua3P1i3f4vcuwC3w%3D%3D";
//            }
            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));
            //实例化任务列表
            List<MerchantActivityDetailAdApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();
            //提前批量查询现金券及卷包
            Map<Long, VoucherRewardBo> voucherRewardBoMap = this.voucherBatchGetRewardValueOfMerchantAd(activityDetail.getActivityTaskLists(), getActivityDetailApiRqt.getUserClass());
            //任务列表赋值
            for (MerchantActivityDetailAdResp.ActivityTaskBo activityTaskList : activityDetail.getActivityTaskLists()) {
                MerchantActivityDetailAdApiResp.ActivityTaskList activityTaskListTmp = new MerchantActivityDetailAdApiResp.ActivityTaskList();
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);
                //判断是否选中
                if (null == activityTaskListTmp.getIsChoice() || 0 == activityTaskListTmp.getIsChoice()) {
                    continue;
                }
                activityTaskListTmp.setButtonSkipUrl(activityTaskList.getButtonSkipUrlMap().get("web"));
                //奖励价值赋值，优惠券类型
                if (voucherRewardBoMap != null && activityTaskList.getRewardExtraId() != null && activityTaskList.getRewardExtraId() > 0) {
                    VoucherRewardBo voucherVo = voucherRewardBoMap.get(activityTaskList.getRewardExtraId());
                    if (voucherVo != null) {
                        activityTaskListTmp.setVoucherType(voucherVo.getVoucherType());
                        activityTaskListTmp.setRewardValue(voucherVo.getRewardValue());
                    }
                } else {
                    //非优惠券奖励类型
                    activityTaskListTmp.setRewardValue(activityTaskList.getRewardGiveValue());
                }
                if (activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER.type)
                        || activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER_PACK.type)) {
                    activityTaskListTmp.setRewardGiveValue(activityTaskListTmp.getRewardValue());
                }
                activityTaskListTmp.setRewardType(activityTaskList.getRewardConfigId());

                if (1 == activityBaseInfo.getIsActivityStart()) {
                    //判断是否选中
                    if (1 == activityTaskListTmp.getIsChoice()) {
                        if (3 == activityTaskList.getButtonStatus() || 0 == activityTaskList.getButtonStatus()) {
                            activityTaskListTmp.setSecondsRemain(0L);
                        } else {
                            activityTaskListTmp.setSecondsRemain(this.secondsRemain(activityBaseInfo.getActivityEndTime(), activityTaskList.getTimeLimit().longValue(), activityTaskList.getApplyTime()));
                        }
                    }
                } else {
                    activityTaskListTmp.setSecondsRemain(0L);
                }

                //没报名倒计时返回0
                if (0 == activityBaseInfo.getIsApply()) {
                    activityTaskListTmp.setSecondsRemain(0L);
                }

                activityTaskListTmp.setActivityUrl(activityUrl);
                activityTaskLists.add(activityTaskListTmp);
            }
            //任务列表
            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);
            getActivityDetailApiResp.setActivityTaskLists(activityTaskLists);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);

            //判断奖励是否都领完
            List<MerchantActivityDetailAdApiResp.ActivityTaskList> rewardCompleted = activityTaskLists.stream().filter(f -> ActivityButtonStateEnum.Collected.code.equals(f.getButtonStatus())
                    || ActivityButtonStateEnum.CollectedComplete.code.equals(f.getButtonStatus())
                    || ActivityButtonStateEnum.CollectedCompleteALl.code.equals(f.getButtonStatus())
            ).collect(Collectors.toList());

            if (rewardCompleted.size() == activityTaskLists.size()) {
                getActivityDetailApiResp.setRewardCompleted(1);
            }

            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusException("get_activity_detail_fail", e.getMessage());
        }
    }

    /***
     * @Description：发送验证码
     * @param: sendMessageCodeRqt
     * @return：
     */
    @Override
    public boolean sendMessageCode(SendMessageCodeRqt sendMessageCodeRqt) {
        String phone = sendMessageCodeRqt.getPhone();
        if (StringUtils.isEmpty(phone) || !ActivityUtil.isSurePhone(phone)) {
            throw new BusinessException("手机号不正确，请检查");
        }
        try {
            //  加手机号
            String lockKey = String.format(CacheKeyConstant.PHONE_SENCMESSAGE_KEY, phone);
            String phoneRedis = redisHelper.get(lockKey);
            //加锁,一分钟限制。
            if (StringUtils.isNotEmpty(phoneRedis)) {
                log.error("同一时刻,获取分布式锁失败,phone:{}", phone);
                throw new BusinessException("请勿重复发送验证码");
            }
            SendAuthCodeRqt codeRqt = new SendAuthCodeRqt();
            codeRqt.setPhone(phone);
            codeRqt.setType(ActivityConstant.SYSTEM_TYPE);
            codeRqt.setSystem(UserTypeEnum.MASTER.type);
            Integer integer = accountServiceApi.sendAuthCode(codeRqt);
            redisHelper.set(lockKey, phone, CacheKeyConstant.ONE_MINUTE_SECOND);
            if (integer > 0) {
                return true;
            }
        } catch (ApiAccessException e) {
            throw new BusinessException("sendMessageCode", super.getErrorMsg(e.getMessage()));
        }

        return false;
    }

    /***
     * @Description：手机号验证并入库
     * @param: sendMessageCodeRqt
     * @return：
     * 1.未注册
     * 2.已注册并已绑定关系
     * 3.已注册没有绑定关系
     */
    @Override
    public Integer checkPhoneAndSave(CheckPhoneAndSaveRqt checkPhoneAndSaveRqt) {
        Integer integer = 1;
        String phone = checkPhoneAndSaveRqt.getPhone();
        if (StringUtils.isEmpty(phone) || !ActivityUtil.isSurePhone(phone)) {
            throw new BusinessException("手机号不正确，请检查");
        }

        try {
            //1.校验是否存在绑定关系 true 存在，false 不存在
            BindInviteByPhoneRqt bindInviteByPhoneRqt = new BindInviteByPhoneRqt();
            bindInviteByPhoneRqt.setPhone(phone);
            bindInviteByPhoneRqt.setUserClass(checkPhoneAndSaveRqt.getInviteeUserClass());
            BindInviteByPhoneResp isBindInviteResp = landingPageServiceApi.getBooleanBindInviteByPhone(bindInviteByPhoneRqt);
            //2.检查手机号是否注册  0是未注册，1是注册过
            CheckPhoneRqt checkPhoneRqt = new CheckPhoneRqt();
            checkPhoneRqt.setPhone(phone);
            Integer checkPhoneNum = accountApi.checkPhone(checkPhoneRqt);
            if (checkPhoneNum == 1) {
                if (isBindInviteResp.getIsBind()) {
                    integer = 2;
                } else {
                    integer = 3;
                }
                return integer;
            }
            //3.生成手机信息记录
            CreatePhoneRecordRqt createPhoneRecordRqt = new CreatePhoneRecordRqt();
            BeanUtils.copyProperties(checkPhoneAndSaveRqt, createPhoneRecordRqt);
            //解析邀请码
            InviteCodeBeanBo inviteCodeBeanBo = coverUtils.coverInviteCodeBeanBo(checkPhoneAndSaveRqt.getInviteCode());
            Assert.notNull(inviteCodeBeanBo, "邀请码解析失败，请确认是否正确");
            createPhoneRecordRqt.setInviterActivityId(inviteCodeBeanBo.getInviterActivityId());
            createPhoneRecordRqt.setInviterUserId(inviteCodeBeanBo.getInviterUserId());
            landingPageServiceApi.createPhoneRecord(createPhoneRecordRqt);

            return integer;
        } catch (Exception e) {
            throw new BusinessException("checkPhoneAndSave", super.getErrorMsg(e.getMessage()));
        }
    }

    /***
     * @Description：获取隐私政策
     * @param:
     * @return：
     */
    @Override
    public GetCurrentAgreementInfoApiResp getPrivacyPolicy() {
        //  加活动id
        String lockKey = String.format(CacheKeyConstant.PRIVACY_POLICY_MASTER_INVITE, ActivityConstant.IS_PRIVACY_POLICY);

        String getCurrentAgreementInfoApiRespStr = redisHelper.get(lockKey);
        if (getCurrentAgreementInfoApiRespStr != null) {
            GetCurrentAgreementInfoApiResp getCurrentAgreementInfoApiResp =
                    JSON.parseObject(getCurrentAgreementInfoApiRespStr, GetCurrentAgreementInfoApiResp.class);
            if (getCurrentAgreementInfoApiResp.getAgreementContent() != null) {
                return getCurrentAgreementInfoApiResp;
            }
        }

        GetCurrentAgreementInfoApiResp getCurrentAgreementInfoApiRespNew = new GetCurrentAgreementInfoApiResp();
        GetCurrentAgreementResp getCurrentAgreementInfoResp = agreementApi.masterPrivacyPolicy();
        if (getCurrentAgreementInfoResp != null) {
            BeanUtils.copyProperties(getCurrentAgreementInfoResp, getCurrentAgreementInfoApiRespNew);
            redisHelper.set(lockKey, JSON.toJSONString(getCurrentAgreementInfoApiRespNew), CacheKeyConstant.ONE_DAY_SECOND);
        }

        return getCurrentAgreementInfoApiRespNew;
    }

    /***
     * @Description：
     * @param: getSettledStateRqt
     * @return：
     */
    @Override
    @UserLoginInfo
    public GetAccountStatusApiResp getSettledState(GetSettledStateRqt getSettledStateRqt) {

        Long userId = getSettledStateRqt.getUserId();

        GetAccountStatusResp getAccountStatusResp = accountApi.getAccountStatus(userId);

        GetAccountStatusApiResp getAccountStatusApiResp = new GetAccountStatusApiResp();

        BeanUtils.copyProperties(getAccountStatusResp, getAccountStatusApiResp);

        return getAccountStatusApiResp;
    }

    /***
     * @Description：接受邀请被注册生成绑定关系
     * @param: setPhoneInsertRqt
     * @return：
     */
    @Override
    public boolean setPhoneInsert(SetPhoneInsertApiRqt setPhoneInsertApiRqt) {
        try {
            //1.正则校验。
            String phone = setPhoneInsertApiRqt.getPhone();
            if (StringUtils.isEmpty(phone) || !ActivityUtil.isSurePhone(phone)) {
                throw new BusinessException("手机号不正确，请检查,phone={}", phone);
            }
            InviteCodeBeanBo inviteCodeBeanBo = coverUtils.coverInviteCodeBeanBo(setPhoneInsertApiRqt.getInviteCode());

            //2.注册师傅，并返回masterId
            AccountRegisterSubmitRqt accountRegisterSubmitRqt = new AccountRegisterSubmitRqt();
            BeanUtils.copyProperties(setPhoneInsertApiRqt, accountRegisterSubmitRqt);
            AccountRegisterResultResp accountRegisterResultResp = accountApi.registerAccount(accountRegisterSubmitRqt);
            if (accountRegisterResultResp == null) {
                throw new BusinessException("手机号注册失败，请检查,phone={}", phone);
            }
            //3.生成绑定关系。
            //解析邀请码
            SetPhoneInsertRqt setPhoneInsertRqt = this.convenInvitePhone(inviteCodeBeanBo, phone, accountRegisterResultResp.getMasterId());
            SetPhoneInsertResp setInsertResult = landingPageServiceApi.setPhoneInsert(setPhoneInsertRqt);
            if (setInsertResult.getInviteeActivityId() != null) {
                //2.广告投放人群
                ImportInviteeReq importInviteeReq = new ImportInviteeReq();
                importInviteeReq.setActivityId(setInsertResult.getInviteeActivityId());
                importInviteeReq.setUserId(accountRegisterResultResp.getMasterId());
                importInviteeReq.setUserClass(setPhoneInsertRqt.getInviteeUserClass());
                Integer integer = adBaseInfoApi.importInvitee(importInviteeReq);
            }

            return true;
        } catch (Exception e) {
            throw new BusinessException("setPhoneInsert_fail", super.getErrorMsg(e.getMessage()));
        }
    }

    /***
     * @Description：构建对象
     * @param: inviteCodeBeanBo
     * @return：
     */
    private SetPhoneInsertRqt convenInvitePhone(InviteCodeBeanBo inviteCodeBeanBo, String phone, Long masterId) {
        //查询邀请人的手机号
        GetMasterInfoRqtBean getMasterInfoRqtBean = new GetMasterInfoRqtBean();
        getMasterInfoRqtBean.setMasterId(inviteCodeBeanBo.getInviterUserId());
        GetMasterInfoRspBean getMasterInfoRspBean = masterInfoApi.getMasterInfo(getMasterInfoRqtBean);
        SetPhoneInsertRqt setPhoneInsertRqt = new SetPhoneInsertRqt();
        setPhoneInsertRqt.setInviteePhone(phone);
        setPhoneInsertRqt.setInviterPhone(getMasterInfoRspBean.getPhone());
        setPhoneInsertRqt.setInviterActivityId(inviteCodeBeanBo.getInviterActivityId());
        setPhoneInsertRqt.setInviterUserId(inviteCodeBeanBo.getInviterUserId());
        setPhoneInsertRqt.setInviterUserClass(inviteCodeBeanBo.getInviterUserClass());
        setPhoneInsertRqt.setInviteeUserId(masterId);
        setPhoneInsertRqt.setInviteeUserClass(inviteCodeBeanBo.getInviteeUserClass());
        return setPhoneInsertRqt;
    }

    /***
     * @Description：获取邀请人详情落地页
     * @param: masterInviterTaskDetailApiRqt
     * @return：
     */
    @Override
    @UserLoginInfo
    public MasterInviterTaskDetailApiResp getInviterTaskLandingPageDetail(MasterInviterTaskDetailApiRqt masterInviterTaskDetailApiRqt) {
        boolean isApp = ActivityConstant.IS_APP.equals(masterInviterTaskDetailApiRqt.getIsWeb());
        if (!appEnable && isApp) {
            return null;
        }
        Long userId = masterInviterTaskDetailApiRqt.getUserId();
        //实例化活动详情
        MasterInviterTaskDetailApiResp getActivityDetailApiResp = new MasterInviterTaskDetailApiResp();
        MasterInviterTaskDetailResp activityDetail = null;
        //判断用户登陆状态、
        MasterInviterTaskDetailRqt getActivityDetailRqt = new MasterInviterTaskDetailRqt();
        BeanUtils.copyProperties(masterInviterTaskDetailApiRqt, getActivityDetailRqt);
        getActivityDetailRqt.setUserId(userId);
        getActivityDetailApiResp.setLogin(true);
        try {
            activityDetail = landingPageServiceApi.getInviterTaskLandingPageDetail(getActivityDetailRqt);
        } catch (Exception e) {
            throw new BusinessException("getInviterTaskLandingPageDetail", super.getErrorMsg(e.getMessage()));
        }
        try {
            //基础信息
            MasterInviterTaskDetailApiResp.ActivityBaseInfo activityBaseInfo = new MasterInviterTaskDetailApiResp.ActivityBaseInfo();
            MasterInviterTaskDetailResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));

            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);


            //落地页配置信息
            MasterInviterTaskDetailApiResp.LandingPageInfo landingPageInfo = new MasterInviterTaskDetailApiResp.LandingPageInfo();
            MasterInviterTaskDetailResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            //被邀请人落地页
            MasterInviterTaskDetailApiResp.InviteeLandingPageInfo inviteeLandingPageInfo = new MasterInviterTaskDetailApiResp.InviteeLandingPageInfo();
            MasterInviterTaskDetailResp.InviteeLandingPageInfo inviteeLandingPageInfo1 = activityDetail.getInviteeLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);
            BeanUtils.copyProperties(inviteeLandingPageInfo1, inviteeLandingPageInfo);

            //其他邀请活动
            List<MasterInviterTaskDetailResp.OtherInviteActivity> otherRewardInfoLists = activityDetail.getOtherInviteActivitiList();

            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = activityDetail.getActivityTaskLists().stream().map(MasterInviterTaskDetailResp.ActivityTaskBo::getRewardImageAid).filter(Objects::nonNull).collect(Collectors.toList());

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    imageAidList.add(Long.valueOf(aid));
                }
            }
            if (CollectionUtils.isNotEmpty(otherRewardInfoLists)) {
                List<Long> otherImageAidList = new ArrayList<>();
                otherRewardInfoLists.forEach(otherInviteActivity -> {
                    otherImageAidList.add(otherInviteActivity.getCollectPreviewImageAid());
                    if (otherInviteActivity.getOtherInviteeLandingPageInfo() != null) {
                        otherImageAidList.add(otherInviteActivity.getOtherInviteeLandingPageInfo().getAppTopImageAid());
                        otherImageAidList.add(otherInviteActivity.getOtherInviteeLandingPageInfo().getShareImageAid());
                    }
                });
                imageAidList.addAll(otherImageAidList);

            }
            Collections.addAll(imageAidList,
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getRewardImageAid(),
                    inviteeLandingPageInfo1.getAppTopImageAid(),
                    inviteeLandingPageInfo1.getShareImageAid()
            );

            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());
            //服务map<服务id,服务名>
            List<MasterInviterTaskDetailResp.ActivityTaskBo> activityTaskLists1 = activityDetail.getActivityTaskLists();
            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setAppTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));
            landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));

            inviteeLandingPageInfo.setAppTopImageUrl(imageResultMap.get(inviteeLandingPageInfo1.getAppTopImageAid()));
            inviteeLandingPageInfo.setShareImageUrl(imageResultMap.get(inviteeLandingPageInfo1.getShareImageAid()));

            landingPageInfo.setRewardTimageUrl(imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            List<String> activityDetailList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    activityDetailList.add(imageResultMap.getOrDefault(Long.valueOf(aid), ""));
                }
            }

            landingPageInfo.setActivityModelDetail(activityDetailList);

            getActivityDetailApiResp.setLandingPageInfo(landingPageInfo);
            getActivityDetailApiResp.setInviteeLandingPageInfo(inviteeLandingPageInfo);

            //实例化任务列表
            List<MasterInviterTaskDetailApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();
            //提前批量查询现金券及卷包
            List<ActivityTaskVoucherBo> extraIdList = activityDetail.getActivityTaskLists().stream().
                    filter(f -> (RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) || RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()))
                            && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                    map(m -> {
                        ActivityTaskVoucherBo activityTaskVoucherBo = new ActivityTaskVoucherBo();
                        activityTaskVoucherBo.setActivityTaskId(m.getActivityTaskId());
                        activityTaskVoucherBo.setRewardExtraId(m.getRewardExtraId());
                        activityTaskVoucherBo.setRewardSymbol(m.getRewardSymbol());

                        return activityTaskVoucherBo;
                    }).distinct().collect(Collectors.toList());
            Map<Long, VoucherRewardBo> voucherRewardBoMap = this.voucherBatchGetRewardValueSuper(extraIdList, UserTypeEnum.MERCHANT.type);

            List<MasterInviterTaskDetailResp.InviteeRewardInfo> inviterRewardInfo = new ArrayList<>();
            //任务列表 再处理
            for (MasterInviterTaskDetailResp.ActivityTaskBo activityTaskList : activityDetail.getActivityTaskLists()) {
                MasterInviterTaskDetailApiResp.ActivityTaskList activityTaskListTmp = new MasterInviterTaskDetailApiResp.ActivityTaskList();

                if (activityTaskList.getRewardImageAid() != null) {
                    activityTaskListTmp.setRewardImageUrl(imageResultMap.get(activityTaskList.getRewardImageAid()));
                }
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);
                //奖励价值赋值，优惠券类型
                if (voucherRewardBoMap != null && activityTaskList.getRewardExtraId() != null && activityTaskList.getRewardExtraId() > 0) {
                    VoucherRewardBo voucherVo = voucherRewardBoMap.get(activityTaskList.getRewardExtraId());
                    if (voucherVo != null) {
                        activityTaskListTmp.setVoucherType(voucherVo.getVoucherType());
                        activityTaskListTmp.setRewardValue(voucherVo.getRewardValue());
                    }
                } else {
                    //非优惠券奖励类型
                    activityTaskListTmp.setRewardValue(activityTaskList.getRewardGiveValue());
                }
                if (activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER.type)
                        || activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER_PACK.type)) {
                    activityTaskListTmp.setRewardGiveValue(activityTaskListTmp.getRewardValue());
                }
                activityTaskListTmp.setRewardType(activityTaskList.getRewardConfigId());
                activityTaskListTmp.setRewardSymbol(activityTaskList.getRewardSymbol());

                //设置任务描述 最多可领取3次
                activityTaskListTmp.setTitleDesc(this.getTaskDescByTask(activityTaskList.getActivityTaskInfo()));

                //没报名倒计时返回0
                if (activityBaseInfo.getIsApply() == 0) {
                    activityTaskListTmp.setSecondsRemain(0L);
                }
                activityTaskListTmp.setIsShowOrderLimit(1);
                if (!org.springframework.util.StringUtils.isEmpty(activityTaskListTmp.getTargetAmountValueRange())) {
                    activityTaskListTmp.setTargetAmountValueRange(activityTaskListTmp.getTargetAmountValueRange().replace("[", "").replace("]", ""));
                }


                //设置奖励文案
                TagTextBo tagTextBo = this.splitTagText(activityTaskList.getTagText());
                if (tagTextBo != null) {
                    activityTaskListTmp.setTargetText(tagTextBo.getTargetText());
                }
                inviterRewardInfo.add(createInviterRewardInfo(activityTaskListTmp));
                activityTaskLists.add(activityTaskListTmp);
            }
            //设置 被邀请人总奖励信息
            List<MasterInviterTaskDetailApiResp.InviteeRewardInfo> inviteeRewardInfo = this.setMasterInviterInviteeRewardInfo(activityDetail.getInviteeRewardConfigInfo());
            //设置 邀请人总奖励信息
            List<MasterInviterTaskDetailApiResp.InviteeRewardInfo> inviterInviteeRewardInfo = this.setMasterInviterInviteeRewardInfo(inviterRewardInfo);
            //设置 其他邀请活动
            List<MasterInviterTaskDetailApiResp.OtherInviteActivity> inviterInviteeRewardInfo1 = this.setMasterInviterInviteeOtherInfo(otherRewardInfoLists, imageResultMap);

            //邀请人信息
            List<String> listNames = super.getListNames(userId);
            getActivityDetailApiResp.setFirstName(listNames.get(0));
            getActivityDetailApiResp.setSecondName(listNames.get(1));
            getActivityDetailApiResp.setMasterId(userId);
            //任务列表
            getActivityDetailApiResp.setActivityTaskLists(activityTaskLists);


            MasterInviterTaskDetailApiResp.ActivityBaseInfo activityBaseInfo2 = getActivityDetailApiResp.getActivityBaseInfo();

            getActivityDetailApiResp.setInviterRewardInfo(inviterInviteeRewardInfo);
            getActivityDetailApiResp.setInviteeRewardInfo(inviteeRewardInfo);
            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo2);
            getActivityDetailApiResp.setOtherInviteActivitiList(inviterInviteeRewardInfo1);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);

            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("getInviterTaskLandingPageDetail", super.getErrorMsg(e.getMessage()));
        }
    }

    private List<OtherInviteActivity> setMasterInviterInviteeOtherInfo(
            List<MasterInviterTaskDetailResp.OtherInviteActivity> inviterRewardInfo, Map<Long, String> imageResultMap) {

        if (CollectionUtils.isEmpty(inviterRewardInfo)) {
            return null;
        }
        List<OtherInviteActivity> otherInviteActivities = BeanCopyUtil.copyListProperties(inviterRewardInfo, MasterInviterTaskDetailApiResp.OtherInviteActivity::new, (taskBo1, taskVo1) -> {
            taskVo1.setCollectPreviewImageUrl(imageResultMap.get(taskBo1.getCollectPreviewImageAid()));

            MasterInviterTaskDetailApiResp.InviteeLandingPageInfo inviteeLandingPageInfo = new InviteeLandingPageInfo();
            if (taskBo1.getOtherInviteeLandingPageInfo() != null) {
                BeanUtils.copyProperties(taskBo1.getOtherInviteeLandingPageInfo(), inviteeLandingPageInfo);
                inviteeLandingPageInfo.setShareImageUrl(imageResultMap.get(inviteeLandingPageInfo.getShareImageAid()));
                inviteeLandingPageInfo.setAppTopImageUrl(imageResultMap.get(inviteeLandingPageInfo.getAppTopImageAid()));

                taskVo1.setInviteeLandingPageInfo(inviteeLandingPageInfo);
            }

        });
        return otherInviteActivities;
    }

    @Override
    @UserLoginInfo
    public MasterInviterTaskMyRewardApiResp getInviterRewardInfoTaskPage(MasterInviterTaskDetailMyRewardApiRqt masterInviterTaskDetailApiRqt) {
        //todo 我的奖励屏蔽掉
        boolean isApp = ActivityConstant.IS_APP.equals(masterInviterTaskDetailApiRqt.getIsWeb());
        if (!appEnable && isApp) {
            return null;
        }
        Long userId = masterInviterTaskDetailApiRqt.getUserId();
        Long activityId = masterInviterTaskDetailApiRqt.getActivityId();
        //实例化活动详情
        MasterInviterTaskMyRewardApiResp getActivityDetailApiResp = new MasterInviterTaskMyRewardApiResp();
        MasterInviterTaskMyRewardResp activityDetail = null;
        //判断用户登陆状态、
        MasterInviterRewardInfoTaskDetailRqt getActivityDetailRqt = new MasterInviterRewardInfoTaskDetailRqt();
        BeanUtils.copyProperties(masterInviterTaskDetailApiRqt, getActivityDetailRqt);
        getActivityDetailRqt.setUserId(userId);
        try {
            activityDetail = landingPageServiceApi.getInviterRewardInfoTaskPage(getActivityDetailRqt);
        } catch (Exception e) {
            throw new BusinessException("getInviterRewardInfoTaskPage", super.getErrorMsg(e.getMessage()));
        }
        if (activityDetail == null) {
            return getActivityDetailApiResp;
        }
        //邀请人信息
        List<String> listNames = super.getListNames(userId);

        getActivityDetailApiResp.setFirstName(listNames.get(0));
        getActivityDetailApiResp.setSecondName(listNames.get(1));
        getActivityDetailApiResp.setInviterUserId(userId);
        try {
            RewardInfoBo rewardInfoBo = this.getInviteCollectPageMyRewardInfo(userId, activityId);
            getActivityDetailApiResp.setHasReward(rewardInfoBo.getHasRewardInfo());
            getActivityDetailApiResp.setWaitUnLockReward(rewardInfoBo.getLockRewardInfo());
        } catch (Exception e) {
            log.warn(e.getMessage());
            log.warn("大数据获取我的奖励数据失败");
        }
        Integer returnRewardRangeTimeStateResult = activityDetail.getReturnRewardRangeTimeStateResult();
        if (returnRewardRangeTimeStateResult != null && returnRewardRangeTimeStateResult.equals(ActivityRewardRangeTimeStateEnum.OVER.code)) {
            getActivityDetailApiResp.setReturnRewardRangeTimeStateResult(activityDetail.getReturnRewardRangeTimeStateResult());
            return getActivityDetailApiResp;
        }

        List<InviteeUserActivityTaskInfo> inviteeUserActivityTaskInfos = BeanCopyUtil.copyListProperties(activityDetail.getInviteeUserActivityTaskList(), MasterInviterTaskMyRewardApiResp.InviteeUserActivityTaskInfo::new, (taskBo1, taskVo1) -> {
            List<InviteeUserActiviyTask> activityTaskInfos = BeanCopyUtil.copyListProperties(taskBo1.getInviteeUserActivityTaskList(), MasterInviterTaskMyRewardApiResp.InviteeUserActivityTaskInfo.InviteeUserActiviyTask::new);

            taskVo1.setInviteeUserId(taskBo1.getInviteeUserId());
            taskVo1.setInviteeUserActivityTaskList(activityTaskInfos);
        });
        //批量获取师傅信息
        List<Long> inviterUserIdList = inviteeUserActivityTaskInfos.stream().map(InviteeUserActivityTaskInfo::getInviteeUserId).collect(Collectors.toList());
        GetTechnologyInfoRqt getTechnologyInfoRqt = new GetTechnologyInfoRqt();
        getTechnologyInfoRqt.setMasterIds(inviterUserIdList);

        List<MasterHeadImgVo> masterHeadImgVoList = masterInfoApi.batchGetMasterIcon(getTechnologyInfoRqt);
        List<String> inviterAidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(masterHeadImgVoList)) {
            masterHeadImgVoList.forEach(masterHeadImgVo -> {
                if (masterHeadImgVo.getAid() != null && masterHeadImgVo.getAid() != 0) {
                    inviterAidList.add(String.valueOf(masterHeadImgVo.getAid()));
                }
            });
            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(inviterAidList)) {
                imageResultMap = httpImageUtils.sendPostRequest(inviterAidList);
            }
            Map<Long, String> finalImageResultMap = imageResultMap;
            inviteeUserActivityTaskInfos.forEach(inviteeUserActivityTaskInfo -> {
                MasterHeadImgVo masterHeadImgVo = masterHeadImgVoList.stream().filter(base -> base.getMasterId().equals(inviteeUserActivityTaskInfo.getInviteeUserId())).findFirst().orElseThrow(() -> new RuntimeException("师傅信息不存在"));
                inviteeUserActivityTaskInfo.setMasterName(masterHeadImgVo.getMasterName());
                inviteeUserActivityTaskInfo.setMasterPhone(masterHeadImgVo.getPhone());
                if (MapUtils.isNotEmpty(finalImageResultMap)) {
                    inviteeUserActivityTaskInfo.setInviteeUrl(finalImageResultMap.get(masterHeadImgVo.getAid()));
                }
            });
        }
        getActivityDetailApiResp.setPageNum(activityDetail.getPageNum());
        getActivityDetailApiResp.setPageSize(activityDetail.getPageSize());
        getActivityDetailApiResp.setTotal(activityDetail.getTotal());
        getActivityDetailApiResp.setInviteeUserActivityTaskList(inviteeUserActivityTaskInfos);
        return getActivityDetailApiResp;

    }

    private MasterInviterTaskDetailResp.InviteeRewardInfo createInviterRewardInfo(ActivityTaskList activityTaskListTmp) {
        MasterInviterTaskDetailResp.InviteeRewardInfo inviteeRewardInfo = new MasterInviterTaskDetailResp.InviteeRewardInfo();
        BeanUtils.copyProperties(activityTaskListTmp, inviteeRewardInfo);
        return inviteeRewardInfo;

    }

    private List<InviteeRewardInfo> setMasterInviterInviteeRewardInfo(List<MasterInviterTaskDetailResp.InviteeRewardInfo> inviteeRewardConfigInfo) {
        Map<String, List<MasterInviterTaskDetailResp.InviteeRewardInfo>> listMap = inviteeRewardConfigInfo.stream().collect(Collectors.groupingBy(MasterInviterTaskDetailResp.InviteeRewardInfo::getRewardSymbol));

        List<InviteeRewardInfo> inviteeRewardInfoList = new ArrayList<>(listMap.size());
        listMap.forEach((key, value) -> {
            InviteeRewardInfo inviteeRewardInfo = new InviteeRewardInfo();
            BeanUtils.copyProperties(value.get(0), inviteeRewardInfo);
            BigDecimal sum = value.stream().map(MasterInviterTaskDetailResp.InviteeRewardInfo::getRewardGiveValue).reduce(BigDecimal.ZERO, BigDecimal::add);
            inviteeRewardInfo.setRewardGiveValue(sum);
            inviteeRewardInfoList.add(inviteeRewardInfo);

        });

        return inviteeRewardInfoList;
    }

    /***
     * @Description：获取被邀请人落地页详情（应用外）
     * @param: masterInviteeTaskOutDetailApiRqt
     * @return：
     */
    @Override
    public MasterInviteeTaskOutDetailApiResp getInviteeTaskOutLandingPageDetail(MasterInviteeTaskOutDetailApiRqt masterInviteeTaskOutDetailApiRqt) {
        boolean isApp = ActivityConstant.IS_APP.equals(masterInviteeTaskOutDetailApiRqt.getIsWeb());
        if (!appEnable && isApp) {
            return null;
        }
        //实例化活动详情
        MasterInviteeTaskOutDetailApiResp getActivityDetailApiResp = new MasterInviteeTaskOutDetailApiResp();
        MasterInviteeTaskOutDetailResp activityDetail = null;
        InviteCodeBeanBo inviteCodeBeanBo = null;
        try {
            //解析邀请码
            inviteCodeBeanBo = coverUtils.coverInviteCodeBeanBo(masterInviteeTaskOutDetailApiRqt.getActivityUrl());
            if (null != inviteCodeBeanBo.getInviterActivityId() && null == masterInviteeTaskOutDetailApiRqt.getTemplateVersion()) {
                throw new BusinessException("模板版本号不能为空");
            }
            //判断用户登陆状态、
            MasterInviteeTaskOutDetailRqt getActivityDetailRqt = new MasterInviteeTaskOutDetailRqt();
            //解析对象
            BeanUtils.copyProperties(masterInviteeTaskOutDetailApiRqt, getActivityDetailRqt);
            getActivityDetailRqt.setActivityId(inviteCodeBeanBo.getInviterActivityId());
            getActivityDetailRqt.setInviterId(inviteCodeBeanBo.getInviterUserId());
            getActivityDetailRqt.setInviterUserClass(inviteCodeBeanBo.getInviterUserClass());
            getActivityDetailRqt.setInviteSource(inviteCodeBeanBo.getInviteWay());
            getActivityDetailRqt.setInviteCode(masterInviteeTaskOutDetailApiRqt.getActivityUrl());
            activityDetail = landingPageServiceApi.getInviteeTaskOutLandingPageDetail(getActivityDetailRqt);
        } catch (Exception e) {
            throw new BusinessException("getInviteeTaskOutLandingPageDetail", super.getErrorMsg(e.getMessage()));
        }
        try {
            //邀请人信息
            List<String> listNames = super.getListNames(inviteCodeBeanBo.getInviterUserId());
            getActivityDetailApiResp.setFirstName(listNames.get(0));
            getActivityDetailApiResp.setSecondName(listNames.get(1));
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);

            if (activityDetail == null || activityDetail.getActivityBaseInfo() == null) {
                return getActivityDetailApiResp;
            }
            //基础信息
            MasterInviteeTaskOutDetailApiResp.ActivityBaseInfo activityBaseInfo = new MasterInviteeTaskOutDetailApiResp.ActivityBaseInfo();
            MasterInviteeTaskOutDetailResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));

            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);


            //落地页配置信息
            MasterInviteeTaskOutDetailApiResp.LandingPageInfo landingPageInfo = new MasterInviteeTaskOutDetailApiResp.LandingPageInfo();
            MasterInviteeTaskOutDetailResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);

            //其他邀请活动
            List<MasterInviteeTaskOutDetailResp.ShowOtherRewardInfo> otherRewardInfoLists = activityDetail.getShowOtherRewardInfoList();
            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    imageAidList.add(Long.valueOf(aid));
                }
            }

            if (CollectionUtils.isNotEmpty(otherRewardInfoLists)) {
                List<Long> otherImageAidList = otherRewardInfoLists.stream().map(MasterInviteeTaskOutDetailResp.ShowOtherRewardInfo::getPublicityBannerImageAid).collect(Collectors.toList());
                imageAidList.addAll(otherImageAidList);
            }
            Collections.addAll(imageAidList,
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getPublicityBannerImageAid(),
                    landingPageInfo1.getShareImageAid()
            );

            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());


            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setAppTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));
            landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));
            landingPageInfo.setPublicityBannerImageUrl(imageResultMap.get(landingPageInfo1.getPublicityBannerImageAid()));
            landingPageInfo.setShareImageUrl(imageResultMap.get(landingPageInfo1.getShareImageAid()));


            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            List<String> activityDetailList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    activityDetailList.add(imageResultMap.getOrDefault(Long.valueOf(aid), ""));
                }
            }
            //设置 其他邀请活动
            List<MasterInviteeTaskOutDetailApiResp.ShowOtherRewardInfo> showOtherRewardInfoList = this.setShowOtherRewardInfo(otherRewardInfoLists, imageResultMap);

            landingPageInfo.setActivityModelDetail(activityDetailList);

            getActivityDetailApiResp.setLandingPageInfo(landingPageInfo);

            MasterInviteeTaskOutDetailApiResp.ActivityBaseInfo activityBaseInfo2 = getActivityDetailApiResp.getActivityBaseInfo();


            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo2);
            getActivityDetailApiResp.setShowOtherRewardInfoList(showOtherRewardInfoList);

            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("getInviteeTaskOutLandingPageDetail", super.getErrorMsg(e.getMessage()));
        }
    }

    /***
     * @Description：被邀请人，应用外，其他邀请活动信息
     * @param: otherRewardInfoLists
     * @return：
     */
    private List<ShowOtherRewardInfo> setShowOtherRewardInfo(
            List<MasterInviteeTaskOutDetailResp.ShowOtherRewardInfo> otherRewardInfoLists, Map<Long, String> imageResultMap) {
        if (CollectionUtils.isEmpty(otherRewardInfoLists)) {
            return null;
        }
        List<ShowOtherRewardInfo> showOtherRewardInfoList = BeanCopyUtil.copyListProperties(otherRewardInfoLists, MasterInviteeTaskOutDetailApiResp.ShowOtherRewardInfo::new, (taskBo1, taskVo1) -> {
            taskVo1.setPublicityBannerImageUrl(imageResultMap.get(taskBo1.getPublicityBannerImageAid()));
        });
        return showOtherRewardInfoList;
    }

    /***
     * @Description：   * 获取被邀请人落地页详情（应用内）
     * @param: masterInviteeTaskInDetailApiRqt
     * @return：
     */
    @Override
    @UserLoginInfo
    public MasterInviteeTaskInDetailApiResp getInviteeTaskInLandingPageDetail(MasterInviteeTaskInDetailApiRqt masterInviteeTaskInDetailApiRqt) {
        boolean isApp = ActivityConstant.IS_APP.equals(masterInviteeTaskInDetailApiRqt.getIsWeb());
        if (!appEnable && isApp) {
            return null;
        }
        Long userId = masterInviteeTaskInDetailApiRqt.getUserId();
        //实例化活动详情
        MasterInviteeTaskInDetailApiResp getActivityDetailApiResp = new MasterInviteeTaskInDetailApiResp();
        MasterInviteeTaskInDetailResp activityDetail = null;
        //判断用户登陆状态、
        MasterInviteeTaskInDetailRqt getActivityDetailRqt = new MasterInviteeTaskInDetailRqt();
        BeanUtils.copyProperties(masterInviteeTaskInDetailApiRqt, getActivityDetailRqt);
        getActivityDetailRqt.setUserId(userId);
        getActivityDetailApiResp.setLogin(true);
        try {
            activityDetail = landingPageServiceApi.getInviteeTaskInLandingPageDetail(getActivityDetailRqt);
        } catch (Exception e) {
            throw new BusinessException("getInviteeTaskInLandingPageDetail", super.getErrorMsg(e.getMessage()));
        }
        try {
            //基础信息
            MasterInviteeTaskInDetailApiResp.ActivityBaseInfo activityBaseInfo = new MasterInviteeTaskInDetailApiResp.ActivityBaseInfo();
            MasterInviteeTaskInDetailResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo.getActivityStartTime(), activityBaseInfo.getActivityEndTime()));

            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);


            //落地页配置信息
            MasterInviteeTaskInDetailApiResp.LandingPageInfo landingPageInfo = new MasterInviteeTaskInDetailApiResp.LandingPageInfo();
            MasterInviteeTaskInDetailResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);

            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = activityDetail.getActivityTaskLists().stream().map(MasterInviteeTaskInDetailResp.ActivityTaskBo::getRewardImageAid).filter(Objects::nonNull).collect(Collectors.toList());

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    imageAidList.add(Long.valueOf(aid));
                }
            }

            Collections.addAll(imageAidList,
                    landingPageInfo1.getActivityCenterPreImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getRewardImageAid()
            );

            //过滤0，null，使用stream过滤
            List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
                return aLong != null && aLong != 0;
            })).map(String::valueOf).collect(Collectors.toList());
            //服务map<服务id,服务名>
            List<MasterInviteeTaskInDetailResp.ActivityTaskBo> activityTaskLists1 = activityDetail.getActivityTaskLists();

            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setActivityCenterPreImageUrl(imageResultMap.get(landingPageInfo1.getActivityCenterPreImageAid()));
            landingPageInfo.setAppTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));
            landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));

            landingPageInfo.setRewardTimageUrl(imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            List<String> activityDetailList = new ArrayList<>();

            if (!org.springframework.util.StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    activityDetailList.add(imageResultMap.getOrDefault(Long.valueOf(aid), ""));
                }
            }

            landingPageInfo.setActivityModelDetail(activityDetailList);

            getActivityDetailApiResp.setLandingPageInfo(landingPageInfo);

            //实例化任务列表
            List<MasterInviteeTaskInDetailApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();
            //提前批量查询现金券及卷包
            List<ActivityTaskVoucherBo> extraIdList = activityDetail.getActivityTaskLists().stream().
                    filter(f -> (RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) || RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()))
                            && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                    map(m -> {
                        ActivityTaskVoucherBo activityTaskVoucherBo = new ActivityTaskVoucherBo();
                        activityTaskVoucherBo.setActivityTaskId(m.getActivityTaskId());
                        activityTaskVoucherBo.setRewardExtraId(m.getRewardExtraId());
                        activityTaskVoucherBo.setRewardSymbol(m.getRewardSymbol());

                        return activityTaskVoucherBo;
                    }).distinct().collect(Collectors.toList());
            Map<Long, VoucherRewardBo> voucherRewardBoMap = this.voucherBatchGetRewardValueSuper(extraIdList, UserTypeEnum.MERCHANT.type);

            //任务列表 再处理
            for (MasterInviteeTaskInDetailResp.ActivityTaskBo activityTaskList : activityDetail.getActivityTaskLists()) {
                MasterInviteeTaskInDetailApiResp.ActivityTaskList activityTaskListTmp = new MasterInviteeTaskInDetailApiResp.ActivityTaskList();

                if (activityTaskList.getRewardImageAid() != null) {
                    activityTaskListTmp.setRewardImageUrl(imageResultMap.get(activityTaskList.getRewardImageAid()));
                }
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);
                activityTaskListTmp.setRewardType(activityTaskList.getRewardConfigId());

                //没报名倒计时返回0
                if (activityBaseInfo.getIsApply() == 0) {
                    activityTaskListTmp.setSecondsRemain(0L);
                }
                activityTaskListTmp.setIsShowOrderLimit(1);
                if (!org.springframework.util.StringUtils.isEmpty(activityTaskListTmp.getTargetAmountValueRange())) {
                    activityTaskListTmp.setTargetAmountValueRange(activityTaskListTmp.getTargetAmountValueRange().replace("[", "").replace("]", ""));
                }
                //是否展示订单限制
                activityTaskListTmp.setIsShowOrderLimit(TaskSymbolEnum.isShowOrderLimit(activityTaskListTmp.getTaskSymbol()));


                //设置奖励文案
                TagTextBo tagTextBo = this.splitTagText(activityTaskList.getTagText());
                if (tagTextBo != null) {
                    activityTaskListTmp.setTargetText(tagTextBo.getTargetText());
                }
                activityTaskLists.add(activityTaskListTmp);
            }

            //任务列表
            getActivityDetailApiResp.setActivityTaskLists(activityTaskLists);


            MasterInviteeTaskInDetailApiResp.ActivityBaseInfo activityBaseInfo2 = getActivityDetailApiResp.getActivityBaseInfo();


            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo2);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);

            //判断奖励是否都领完
            List<MasterInviteeTaskInDetailApiResp.ActivityTaskList> rewardCompleted = activityTaskLists.stream().filter(f -> f.getButtonStatus().equals(ActivityButtonStateEnum.Collected.code)
                    || f.getButtonStatus().equals(ActivityButtonStateEnum.CollectedComplete.code)
                    || f.getButtonStatus().equals(ActivityButtonStateEnum.CollectedCompleteALl.code)
            ).collect(Collectors.toList());

            if (rewardCompleted.size() == activityTaskLists.size()) {
                getActivityDetailApiResp.setRewardCompleted(1);
            }


            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("getInviteeTaskInLandingPageDetail", super.getErrorMsg(e.getMessage()));
        }
    }

    /***
     * @Description：师傅邀请活动==集合页
     * @param: masterInviteTaskCollectPageDetailApiRqt
     * @return：
     */
    @Override
    @UserLoginInfo
    public MasterInviteTaskCollectPageDetailApiResp getInviteTaskCollectPageDetail(MasterInviteTaskCollectPageDetailApiRqt masterInviteTaskCollectPageDetailApiRqt) {
        Long userId = masterInviteTaskCollectPageDetailApiRqt.getUserId();
        MasterInviteTaskCollectPageDetailApiResp masterInviteTaskCollectPageDetailApiResp = new MasterInviteTaskCollectPageDetailApiResp();

        MasterInviteTaskCollectPageDetailRqt masterInviteTaskCollectPageDetailRqt = new MasterInviteTaskCollectPageDetailRqt();
        masterInviteTaskCollectPageDetailRqt.setUserId(userId);
        masterInviteTaskCollectPageDetailRqt.setUserClass(UserTypeEnum.MASTER.type);
        MasterInviteTaskCollectPageDetailResp masterInviteTaskCollectPageDetailResp = null;
        try {
            masterInviteTaskCollectPageDetailResp = landingPageServiceApi.getInviteTaskCollectPageDetail(masterInviteTaskCollectPageDetailRqt);
        } catch (Exception e) {
            throw new BusinessException("getInviteTaskCollectPageDetail", super.getErrorMsg(e.getMessage()));
        }
        List<MasterInviteTaskApiActivity> masterInviteTaskApiActivities = BeanCopyUtil.copyListProperties(masterInviteTaskCollectPageDetailResp.getActivityList(), MasterInviteTaskCollectPageDetailApiResp.MasterInviteTaskApiActivity::new, (taskBo1, taskVo1) -> {
            // 添加
            MasterInviteTaskApiActivity.ActivityInfo activityInfo = new ActivityInfo();
            BeanUtils.copyProperties(taskBo1.getActivityInfo(), activityInfo);

            MasterInviteTaskApiActivity.LandingPageInfo landingPageInfo = new MasterInviteTaskApiActivity.LandingPageInfo();
            BeanUtils.copyProperties(taskBo1.getLandingPageInfo(), landingPageInfo);

            MasterInviteTaskApiActivity.InviteeLandingPageInfo inviteeLandingPageInfo = new MasterInviteTaskApiActivity.InviteeLandingPageInfo();
            BeanUtils.copyProperties(taskBo1.getInviteeLandingPageInfo(), inviteeLandingPageInfo);

            List<MasterInviteTaskApiActivity.ActivityTaskInfo> activityTaskInfos = BeanCopyUtil.copyListProperties(taskBo1.getActivityTaskList(), MasterInviteTaskCollectPageDetailApiResp.MasterInviteTaskApiActivity.ActivityTaskInfo::new);
            taskVo1.setActivityInfo(activityInfo);
            taskVo1.setLandingPageInfo(landingPageInfo);
            taskVo1.setInviteeLandingPageInfo(inviteeLandingPageInfo);
            taskVo1.setActivityTaskList(activityTaskInfos);
        });
        //图片解析
        List<Long> imageAidList = masterInviteTaskApiActivities.stream().map(masterInviteTaskApiActivity -> {
            return masterInviteTaskApiActivity.getLandingPageInfo().getCollectPreviewImageAid();
        }).collect(Collectors.toList());
        //分享图片
        List<Long> shareImageAidList = masterInviteTaskApiActivities.stream().map(masterInviteTaskApiActivity -> {
            return masterInviteTaskApiActivity.getInviteeLandingPageInfo().getShareImageAid();
        }).collect(Collectors.toList());
        imageAidList.addAll(shareImageAidList);
        //顶部素材图
        List<Long> appTopImageAidList = masterInviteTaskApiActivities.stream().map(masterInviteTaskApiActivity -> {
            return masterInviteTaskApiActivity.getInviteeLandingPageInfo().getAppTopImageAid();
        }).collect(Collectors.toList());
        imageAidList.addAll(appTopImageAidList);
        //过滤0，null，使用stream过滤
        List<String> imageAidListNotNull = imageAidList.stream().filter((aLong -> {
            return aLong != null && aLong != 0;
        })).map(String::valueOf).collect(Collectors.toList());

        Map<Long, String> imageResultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
            imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
        }
        Map<Long, String> finalImageResultMap = imageResultMap;
        masterInviteTaskApiActivities.forEach(masterInviteTaskApiActivity -> {
            MasterInviteTaskApiActivity.LandingPageInfo landingPageInfo = masterInviteTaskApiActivity.getLandingPageInfo();
            MasterInviteTaskApiActivity.InviteeLandingPageInfo inviteeLandingPageInfo = masterInviteTaskApiActivity.getInviteeLandingPageInfo();
            landingPageInfo.setCollectPreviewImageUrl(finalImageResultMap.get(landingPageInfo.getCollectPreviewImageAid()));
            inviteeLandingPageInfo.setShareImageUrl(finalImageResultMap.get(inviteeLandingPageInfo.getShareImageAid()));
            inviteeLandingPageInfo.setAppTopImageUrl(finalImageResultMap.get(inviteeLandingPageInfo.getAppTopImageAid()));
        });
        try {
            RewardInfoBo rewardInfoBo = this.getInviteCollectPageMyRewardInfo(userId, null);
            masterInviteTaskCollectPageDetailApiResp.setHasRewardInfo(rewardInfoBo.getHasRewardInfo());
            masterInviteTaskCollectPageDetailApiResp.setNoRewardInfo(rewardInfoBo.getLockRewardInfo());
        } catch (Exception e) {
            log.warn(e.getMessage());
            log.warn("大数据获取我的奖励数据失败");
        }
        //邀请人信息
        List<String> listNames = super.getListNames(userId);

        masterInviteTaskCollectPageDetailApiResp.setFirstName(listNames.get(0));
        masterInviteTaskCollectPageDetailApiResp.setSecondName(listNames.get(1));
        masterInviteTaskCollectPageDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);
        masterInviteTaskCollectPageDetailApiResp.setWaitNum(masterInviteTaskCollectPageDetailResp.getWaitNum());
        masterInviteTaskCollectPageDetailApiResp.setUserId(userId);
        masterInviteTaskCollectPageDetailApiResp.setActivityList(masterInviteTaskApiActivities);
        return masterInviteTaskCollectPageDetailApiResp;
    }

    /***
     * @Description：集合页--大数据的我的奖励
     * @param:
     * @return：
     */
    private RewardInfoBo getInviteCollectPageMyRewardInfo(Long userId, Long activityId) {
        //先查缓存
        String redisKey = String.format(CacheKeyConstant.INVITER_COLLECT_PAGE_MYREWARDINFO, userId, activityId);

        //如果存在
        if (redisHelper.exists(redisKey)) {
            return JSONObject.parseObject(redisHelper.get(redisKey), RewardInfoBo.class);
        }

        RewardInfoBo rewardInfoBo = new RewardInfoBo();
        //目前只需要查询单个师傅的  todo  加个t+1缓存，缓存一个小时
        String userIds = String.valueOf(userId);
        Long defaultActivityId = ActivityConstant.BIG_DATA_DEFAULT;
        if (activityId != null) {
            defaultActivityId = activityId;
        }
        List<MstInviteActivityDataResp> mstInviteActivityDataRespList = bigDataAliYunPersonaApi.getMstInviteActivityData(appcode, defaultActivityId, userIds);
        if (CollectionUtils.isEmpty(mstInviteActivityDataRespList)) {
            log.error("大数据用户数据查询为空,userId={}", userIds);
            return rewardInfoBo;
        }
        MstInviteActivityDataResp mstInviteActivityDataResp = mstInviteActivityDataRespList.stream().filter(base -> base.getMasterId().equals(userId)).findFirst().orElseThrow(() -> new RuntimeException("我的奖励信息不存在"));

        RewardInfoBo.RewardInfo hasRewardInfo = new RewardInfoBo.RewardInfo();
        RewardInfoBo.RewardInfo noRewardInfo = new RewardInfoBo.RewardInfo();

        hasRewardInfo.setBonusAmt(mstInviteActivityDataResp.getGetBonusAmt());
        hasRewardInfo.setIntegralNum(mstInviteActivityDataResp.getGetIntegralNum());

        noRewardInfo.setBonusAmt(mstInviteActivityDataResp.getWaitBonusAmt());
        noRewardInfo.setIntegralNum(mstInviteActivityDataResp.getWaitIntegralNum());

        rewardInfoBo.setHasRewardInfo(hasRewardInfo);
        rewardInfoBo.setLockRewardInfo(noRewardInfo);

        //写入缓存
        redisHelper.set(redisKey, JSON.toJSONString(rewardInfoBo), CacheKeyConstant.ONE_HOUR_SECOND);

        return rewardInfoBo;
    }

    /***
     * @Description：师傅活动
     * @param: masterInviteTaskCollectPageMyRwardInfoApiRqt
     * @return：
     */
    @Override
    @UserLoginInfo
    public MasterInviteTaskCollectPageMyRwardInfoApiResp getInviteTaskCollectPageMyRewardInfo(MasterInviteTaskCollectPageMyRwardInfoApiRqt masterInviteTaskCollectPageMyRwardInfoApiRqt) {
        Long userId = masterInviteTaskCollectPageMyRwardInfoApiRqt.getUserId();
        //组装我的奖励  获取大数据接口返回。
        RewardConfigRqt rewardConfigRqt = new RewardConfigRqt();
        rewardConfigRqt.setActivityModel(ActivityTypeEnum.INVITE_TASK.getActivityType());
        rewardConfigRqt.setUserClass(UserTypeEnum.MASTER.type);
        List<RewardConfigResp> rewardConfigResps = activityBackendServiceApi.getRewardConfig(rewardConfigRqt);
        //目前只需要查询单个师傅的
        String userIds = String.valueOf(userId);
        List<MstInviteActivityDataResp> mstInviteActivityDataRespList = bigDataAliYunPersonaApi.getMstInviteActivityData(appcode, ActivityConstant.BIG_DATA_DEFAULT, userIds);
        if (CollectionUtils.isEmpty(mstInviteActivityDataRespList)) {
            log.error("大数据用户数据查询为空,userId={}", userIds);
            return null;
        }
        List<MasterInviteTaskCollectPageMyRwardInfoApiResp> list = mstInviteActivityDataRespList.stream().map(mstInviteActivityDataResp -> {
            MasterInviteTaskCollectPageMyRwardInfoApiResp myRwardInfo = new MasterInviteTaskCollectPageMyRwardInfoApiResp();
            myRwardInfo.setUserId(mstInviteActivityDataResp.getMasterId());
            MasterInviteTaskCollectPageMyRwardInfoApiResp.RewardInfo hasRewardInfo = new RewardInfo();
            MasterInviteTaskCollectPageMyRwardInfoApiResp.RewardInfo noRewardInfo = new RewardInfo();
            if (mstInviteActivityDataResp.getGetBonusAmt() != null) {
                RewardConfigResp rewardConfigResp = rewardConfigResps.stream().filter(base -> base.getRewardSymbol().equals(RewardSymbol.BONUS.type)).findFirst().orElseThrow(() -> new RuntimeException("奖励信息不存在"));

                hasRewardInfo.setRewardConfigId(rewardConfigResp.getRewardConfigId());
                hasRewardInfo.setRewardValue(mstInviteActivityDataResp.getGetBonusAmt());
            } else if (mstInviteActivityDataResp.getGetIntegralNum() != null) {
                RewardConfigResp rewardConfigResp = rewardConfigResps.stream().filter(base -> base.getRewardSymbol().equals(RewardSymbol.INTEGRAL.type)).findFirst().orElseThrow(() -> new RuntimeException("奖励信息不存在"));

                hasRewardInfo.setRewardConfigId(rewardConfigResp.getRewardConfigId());
                hasRewardInfo.setRewardValue(mstInviteActivityDataResp.getGetIntegralNum());
            }
            myRwardInfo.setHasRewardInfo(hasRewardInfo);
            myRwardInfo.setNoRewardInfo(noRewardInfo);
            return myRwardInfo;
        }).collect(Collectors.toList());
        MasterInviteTaskCollectPageMyRwardInfoApiResp myRwardInfoApiResp = list.stream().filter(rewardInfo -> rewardInfo.getUserId().equals(userId)).findFirst().orElseThrow(() -> new RuntimeException("用户信息不存在"));

        return myRwardInfoApiResp;
    }

    /***
     * @Description：批量获取现金券的相关信息
     * @param: activityTaskLists
     * @return：
     */
    private List<ActivityTaskVoucherBo> getExtadIdList(List<MerchantRechargeTaskResp.ActivityTaskBo> activityTaskLists) {
        List<MerchantRechargeTaskResp.ActivityTaskBo.TaskRewardResp> taskRewardRespList1 = new ArrayList<>();

        //判断是否为组合奖励
        activityTaskLists.stream().
                filter(f -> (RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) || RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()) || (RewardSymbol.COMBINE.type.equals(f.getRewardSymbol()))))
                .forEach(
                        activityTaskBo -> {
                            taskRewardRespList1.addAll(activityTaskBo.getTaskRewardRespList());
                        }
                );

        //提前批量查询现金券及卷包
        List<ActivityTaskVoucherBo> extraIdList = taskRewardRespList1.stream().
                filter(f -> (RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) || RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()))
                        && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> {
                    ActivityTaskVoucherBo activityTaskVoucherBo = new ActivityTaskVoucherBo();
                    activityTaskVoucherBo.setRewardExtraId(m.getRewardExtraId());
                    activityTaskVoucherBo.setRewardSymbol(m.getRewardSymbol());

                    return activityTaskVoucherBo;
                }).distinct().collect(Collectors.toList());
        return extraIdList;
    }

    /***
     * @Description：构建奖励返回列表参数
     * @param: taskRewardRespList
     * @return：
     */
    private List<TaskRewardResp> createTaskRewardRespList(
            List<MerchantRechargeTaskResp.ActivityTaskBo.TaskRewardResp> taskRewardRespList,
            Map<Long, VoucherRewardBo> voucherRewardBoMap,
            MerchantRechargeTaskResp.ActivityTaskBo activityTaskList,
            List<RewardConfigResp> rewardConfigApiResps) {
        List<MerchantRechargeTaskApiResp.ActivityTaskList.TaskRewardResp> taskApiRewardRespList = BeanCopyUtil.copyListProperties(taskRewardRespList, MerchantRechargeTaskApiResp.ActivityTaskList.TaskRewardResp::new);

        //区分组合奖励
        taskApiRewardRespList.forEach(
                taskRewardResp -> {
                    //奖励价值赋值，优惠券类型
                    if (voucherRewardBoMap != null && taskRewardResp.getRewardExtraId() != null && taskRewardResp.getRewardExtraId() > 0) {
                        VoucherRewardBo voucherVo = voucherRewardBoMap.get(taskRewardResp.getRewardExtraId());
                        if (voucherVo != null) {
                            taskRewardResp.setVoucherType(voucherVo.getVoucherType());
                            //如果是折扣券，换乘10.
                            taskRewardResp.setRewardValue(ActivityConstant.VOUCHER_DISCOUNT.equals(voucherVo.getVoucherType()) ?
                                    voucherVo.getRewardValue().multiply(new BigDecimal(10)) : voucherVo.getRewardValue());
                        }
                    } else {
                        //非优惠券奖励类型
                        taskRewardResp.setRewardValue(taskRewardResp.getRewardGiveValue());
                    }
//                    if (activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER.type)
//                            || activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER_PACK.type)) {
//                        taskRewardResp.setRewardGiveValue(taskRewardResp.getRewardValue());
//                    }
                    // todo symbol可以直接判断奖励类型
                    RewardConfigResp rewardConfigResp = rewardConfigApiResps.stream()
                            .filter(config -> config.getRewardSymbol().equals(taskRewardResp.getRewardSymbol())).findFirst().orElseThrow(() -> new BusException("奖励配置数据不存在"));

                    taskRewardResp.setRewardType(rewardConfigResp.getRewardConfigId());
                }
        );
        return taskApiRewardRespList;
    }

    /**
     * 师傅有奖任务进入落地页之前的预请求接口
     */
    @Override
    @UserLoginInfo
    public PrizeTaskPreResp getPrizeTaskPre(PrizeTaskPreRqt prizeTaskPreRqt) {
        Long activityId = prizeTaskPreRqt.getActivityId();
        Long masterId = prizeTaskPreRqt.getUserId();
        PrizeTaskPreResp prizeTaskPreResp = new PrizeTaskPreResp();
        try {
            PrizeTaskPreServiceRqt prizeTaskPreServiceRqt = new PrizeTaskPreServiceRqt();
            prizeTaskPreServiceRqt.setActivityId(activityId);
            prizeTaskPreServiceRqt.setMasterId(masterId);

            PrizeTaskPreServiceResp prizeTask = landingPageServiceApi.getPrizeTask(prizeTaskPreServiceRqt);
            BeanUtils.copyProperties(prizeTask, prizeTaskPreResp);
            //兼容同比判断
            if (prizeTask.getIsRatio() == 0 && prizeTask.getIsYoy() == 1) {
                prizeTaskPreResp.setIsRatio(1);
            }
            return prizeTaskPreResp;
        } catch (ApiAccessException e) {
            throw new BusinessException("get_prize_task_pre", e.getRetMesg());
        }
    }

    /***
     * @Description：组装技能树数据
     * @param: techniques
     * @param: notMatchlevel1ids
     * @return：
     * @param techniqueList
     */
    private List<Technology> createTechnology(List<Technique> techniqueList) {
        List<Technology> technologyList = techniqueList.stream().map(
                technique -> {
                    Technology technology = new Technology();
                    technology.setLevel(technique.getLevel());
                    technology.setTechniqueId(technique.getTechniqueId());
                    technology.setParentId(technique.getParentId());
                    technology.setName(technique.getTechniqueName());
                    technology.setLevel1Id(technique.getLevel1Id());
                    technology.setLevel1Name(technique.getLevel1Name());
                    return technology;
                }
        ).collect(Collectors.toList());
        //处理成- 多级树状list
        Map<Long, List<Technology>> listMap = technologyList.stream()
                .collect(Collectors.groupingBy(Technology::getParentId));
        //截取前五位
        Map<Long, List<Technology>> listlevel1MapNew = new HashMap<>();
        for (Long key : listMap.keySet()) {
            listlevel1MapNew.put(key, listMap.get(key));
            if (listlevel1MapNew.size() > 5) {
                break;
            }
        }
        List<Technology> respList = new ArrayList<>();
        listlevel1MapNew.entrySet().stream().forEach(entrySet -> {
            Technology technology = new Technology();
            technology.setLevel(1);
            technology.setTechniqueId(entrySet.getKey());
            List<Technology> childList = entrySet.getValue();
            technology.setName(childList.get(0).getLevel1Name());
            technology.setChildren(childList);
            respList.add(technology);
        });

        //gc
        technologyList = null;
        listMap = null;
        listlevel1MapNew = null;

        return respList;
    }

    /***
     * @Description：组装技能树数据
     * @param: techniques
     * @param: notMatchlevel1ids
     * @return：
     * @param techniqueList
     */
    private List<Technology> createTechnologyTwo(List<Technique> techniqueList) {
        List<Technology> technologyList = techniqueList.stream().map(
                technique -> {
                    Technology technology = new Technology();
                    technology.setLevel(technique.getLevel());
                    technology.setTechniqueId(technique.getTechniqueId());
                    technology.setParentId(technique.getParentId());
                    technology.setName(technique.getTechniqueName());
                    technology.setLevel1Id(technique.getLevel1Id());
                    technology.setLevel1Name(technique.getLevel1Name());
                    return technology;
                }
        ).collect(Collectors.toList());
        //处理成- 多级树状list
        Map<Long, List<Technology>> listMap = technologyList.stream()
                .collect(Collectors.groupingBy(Technology::getParentId));
        //截取前五位
        Map<Long, List<Technology>> listlevel1MapNew = new HashMap<>();
        for (Long key : listMap.keySet()) {
            listlevel1MapNew.put(key, listMap.get(key));
        }
        List<Technology> respList = new ArrayList<>();
        listlevel1MapNew.entrySet().stream().forEach(entrySet -> {
            Technology technology = new Technology();
            technology.setLevel(1);
            technology.setTechniqueId(entrySet.getKey());
            List<Technology> childList = entrySet.getValue();
            technology.setName(childList.get(0).getLevel1Name());
            technology.setChildren(childList);
            respList.add(technology);
        });

        //gc
        technologyList = null;
        listMap = null;
        listlevel1MapNew = null;

        return respList;
    }

    private CustomOrderTagBo createCustomOrderTagBo(CustomOrderTagRqt customOrderTagRqt,
                                                    List<GetFilterOrdersResp> filterOrderList, Long masterId, Long activityId) {
        CustomOrderTagBo customOrderTagBo = new CustomOrderTagBo();
        List<FilterOrderInfoBo> orderList = filterOrderList.stream().map(
                getFilterOrdersResp -> {
                    FilterOrderInfoBo filterOrderInfoBo = new FilterOrderInfoBo();
                    BeanUtils.copyProperties(getFilterOrdersResp, filterOrderInfoBo);
                    filterOrderInfoBo.setServerIds(getFilterOrdersResp.getServeIds());
                    //其他过滤条件，订单包属性
//                    filterOrderInfoBo.setOtherOrderFilterCondition(ActivityUtil.dealOtherFilterCondition(super.createOrderFilterConditionBoByValue(getFilterOrdersResp.getOrderPaceage)));
                    return filterOrderInfoBo;
                }
        ).collect(Collectors.toList());
        customOrderTagBo.setFilterOrderInfoBos(orderList);
        customOrderTagBo.setReturnNumber(returnNumber);
        customOrderTagBo.setUserId(masterId);
        customOrderTagBo.setActivityId(activityId);
        return customOrderTagBo;
    }

    /**
     * 活动是否开始，before
     */
    public Integer checkActivityStart(Date startTime, Date endTime) {

        long activityStartStamp = startTime.getTime() / 1000;

        long activityEndStamp = endTime.getTime() / 1000;

        long nowStamp = System.currentTimeMillis() / 1000;

        if (nowStamp > activityStartStamp && nowStamp < activityEndStamp) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * //设置任务文案，xxx内完成1单 登陆状态
     */
    public String getTaskTitle(final String taskSymbol, UserActivityTask userActivityTask,
                               String customizeTargetText, BigDecimal targetQuantityValue) {
        if (userActivityTask == null) {
            return "";
        }
        if (StringUtils.isNotEmpty(customizeTargetText)) {
            //判断是不是包含特殊字符。{$},需要判断，并且替换
            return customizeTargetText.replace(ActivityConstant.REPLACE_STR, targetQuantityValue.intValue() + "");
        }
        switch (taskSymbol) {
            case ActivityConstant.quoted:
                return "报价" + userActivityTask.getTargetQuantityValue() + "单";
            case ActivityConstant.appointed:
                if (UserTypeEnum.MERCHANT.type.equals(userActivityTask.getUserClass())) {
                    return "指派" + userActivityTask.getTargetQuantityValue() + "单";
                } else if (UserTypeEnum.MASTER.type.equals(userActivityTask.getUserClass())) {
                    return "被指派" + userActivityTask.getTargetQuantityValue() + "单";
                }
            case ActivityConstant.serve_complete:
                return "完工" + userActivityTask.getTargetQuantityValue() + "单";
            default:
                return null;
        }
    }

    /**
     * //设置任务文案，xxx内完成1单 登陆状态
     */
    public String getTaskTitleNoLogin(String taskSymbol, String userClass) {
        switch (taskSymbol) {
            case ActivityConstant.quoted:
                return ActivityConstant.quoted_default;
            case ActivityConstant.appointed:
                if (UserTypeEnum.MERCHANT.type.equals(userClass)) {
                    return ActivityConstant.appointed_default_1;
                } else if (UserTypeEnum.MASTER.type.equals(userClass)) {
                    return ActivityConstant.appointed_default;
                }
            case ActivityConstant.serve_complete:
                return ActivityConstant.serve_complete_default;
            default:
                return null;
        }
    }

    /**
     * 返回时间剩余秒数
     */
    public Long secondsRemain(Date time,
                              Long timeLimits, Date applyTime) {

        //活动结束时间
        long activityEndTime = time.getTime() / 1000;

        //任务限时
        long timeLimit = timeLimits;

        long applyTimeStamp = applyTime.getTime() / 1000;

        //当前时间戳
        long nowTime = (new Date()).getTime() / 1000;

        if (timeLimit > 0 && activityEndTime - nowTime > timeLimit) {
            //如果有限时，并且当前时间-活动结束时间差大于限时时间就返回限时时间
            return timeLimit + applyTimeStamp - nowTime;
        } else {
            if (activityEndTime - nowTime > 0) {
                return activityEndTime - nowTime;
            } else {
                return 0L;
            }
        }
    }

    /***
     * @Description：根据class转化对象
     * @param: activityDetailNoLogin
     * @return：
     */
    private Object coverObject(PageGetActivityDetailResp activityDetailResp, Class className) {
        if (ObjectUtils.isEmpty(activityDetailResp)) {
            throw new BusException("返回对象为空");
        }
        ;
        if (MasterOrderTaskResp.class.equals(className)) {
            return this.createMasterOrderTaskResp(activityDetailResp);

        } else if (MerchantPrizeTaskResp.class.equals(className)) {
            return this.createMerchantPrizeTaskResp(activityDetailResp);

        } else if (MerchantRechargeTaskResp.class.equals(className)) {
            return this.createMerchantRechargeTaskResp(activityDetailResp);
        } else if (LotteryTurntableLandingPageDetailResp.class.equals(className)) {
            return this.createLotteryTurntableLandingPageDetailResp(activityDetailResp);
        }
        return null;
    }

    private LotteryTurntableLandingPageDetailResp createLotteryTurntableLandingPageDetailResp(PageGetActivityDetailResp activityDetailResp) {
        PageGetActivityDetailResp.ActivityBaseInfo activityBaseInfo = activityDetailResp.getActivityBaseInfo();
        ActivityBaseExt activityBaseExt = activityDetailResp.getActivityBaseExt();
        PageGetActivityDetailResp.LandingPageInfo landingPageInfo = activityDetailResp.getLandingPageInfo();
        List<PageGetActivityDetailResp.ActivityTaskBo> activityTaskBoList = activityDetailResp.getActivityTaskLists();

        LotteryTurntableLandingPageDetailResp lotteryTurntableLandingPageDetailResp = new LotteryTurntableLandingPageDetailResp();
        LotteryTurntableLandingPageDetailResp.ActivityBaseInfo activityBaseInfo1 = new LotteryTurntableLandingPageDetailResp.ActivityBaseInfo();

        BeanUtils.copyProperties(activityBaseInfo, activityBaseInfo1);
        lotteryTurntableLandingPageDetailResp.setActivityBaseExt(activityBaseExt);
        activityBaseInfo1.setApplyCondition(activityBaseExt.getApplyCondition());

        lotteryTurntableLandingPageDetailResp.setActivityBaseInfo(activityBaseInfo1);
        LotteryTurntableLandingPageDetailResp.LandingPageInfo landingPageInfo1 = new LotteryTurntableLandingPageDetailResp.LandingPageInfo();
        BeanUtils.copyProperties(landingPageInfo, landingPageInfo1);
        lotteryTurntableLandingPageDetailResp.setLandingPageInfo(landingPageInfo1);
        List<LotteryTurntableLandingPageDetailResp.ActivityTaskBo> activityTaskBoList1 = BeanCopyUtil.copyListProperties(activityTaskBoList, LotteryTurntableLandingPageDetailResp.ActivityTaskBo::new, (taskBo, taskVo) -> {

            List<LotteryTurntableLandingPageDetailResp.ActivityTaskBo.TaskRewardResp> taskRewardRespList = BeanCopyUtil.copyListProperties(taskBo.getTaskRewardRespList(), LotteryTurntableLandingPageDetailResp.ActivityTaskBo.TaskRewardResp::new, (taskBo1, taskVo1) -> {
                // 添加
                LotteryTurntableLandingPageDetailResp.ActivityTaskBo.TaskRewardResp taskRewardResp = new LotteryTurntableLandingPageDetailResp.ActivityTaskBo.TaskRewardResp();
                BeanUtils.copyProperties(taskBo1, taskRewardResp);
            });
            taskVo.setTaskRewardRespList(taskRewardRespList);
            //未登录时设置默认值为0
            taskVo.setButtonStatus(0);
        });
        lotteryTurntableLandingPageDetailResp.setAwarding(activityDetailResp.getAwarding());
        lotteryTurntableLandingPageDetailResp.setHasAwarding(activityDetailResp.getHasAwarding());
        lotteryTurntableLandingPageDetailResp.setActivityTaskLists(activityTaskBoList1);
        activityDetailResp = null;
        return lotteryTurntableLandingPageDetailResp;
    }

    /***
     * @Description：充值专用落地页
     * @param: activityDetailResp
     * @return：
     */
    private MerchantRechargeTaskResp createMerchantRechargeTaskResp(PageGetActivityDetailResp activityDetailResp) {
        PageGetActivityDetailResp.ActivityBaseInfo activityBaseInfo = activityDetailResp.getActivityBaseInfo();
        ActivityBaseExt activityBaseExt = activityDetailResp.getActivityBaseExt();
        PageGetActivityDetailResp.LandingPageInfo landingPageInfo = activityDetailResp.getLandingPageInfo();
        List<PageGetActivityDetailResp.ActivityTaskBo> activityTaskBoList = activityDetailResp.getActivityTaskLists();

        MerchantRechargeTaskResp merchantRechargeTaskResp = new MerchantRechargeTaskResp();
        MerchantRechargeTaskResp.ActivityBaseInfo activityBaseInfoOrderTask = new MerchantRechargeTaskResp.ActivityBaseInfo();
        activityBaseInfoOrderTask.setApplyCondition(activityBaseExt.getApplyCondition());
        activityBaseInfoOrderTask.setRewardUserActivityLimit(activityBaseExt.getRewardUserActivityLimit());
        BeanUtils.copyProperties(activityBaseInfo, activityBaseInfoOrderTask);
        merchantRechargeTaskResp.setActivityBaseInfo(activityBaseInfoOrderTask);

        List<MerchantRechargeTaskResp.ActivityTaskBo> activityTaskBoList1 = BeanCopyUtil.copyListProperties(activityTaskBoList, MerchantRechargeTaskResp.ActivityTaskBo::new, (taskBo, taskVo) -> {
            // 添加limit对象的设置  todo 商家充值专用落地页不需要返回订单限制对象
            MerchantRechargeTaskResp.ActivityTaskBo.TaskOrderLimit taskOrderLimit = new MerchantRechargeTaskResp.ActivityTaskBo.TaskOrderLimit();
            BeanUtils.copyProperties(taskBo.getTaskOrderLimit(), taskOrderLimit);
            taskVo.setTaskOrderLimit(taskOrderLimit);
            List<MerchantRechargeTaskResp.ActivityTaskBo.TaskRewardResp> taskRewardRespList = BeanCopyUtil.copyListProperties(taskBo.getTaskRewardRespList(), MerchantRechargeTaskResp.ActivityTaskBo.TaskRewardResp::new, (taskBo1, taskVo1) -> {
                // 添加
                MerchantRechargeTaskResp.ActivityTaskBo.TaskRewardResp taskRewardResp = new MerchantRechargeTaskResp.ActivityTaskBo.TaskRewardResp();
                BeanUtils.copyProperties(taskBo1, taskRewardResp);
            });
            taskVo.setTaskRewardRespList(taskRewardRespList);
            //未登录时设置默认值为1
            taskVo.setButtonStatus(1);
        });

        merchantRechargeTaskResp.setActivityTaskLists(activityTaskBoList1);

        MerchantRechargeTaskResp.LandingPageInfo landingPageInfo1 = new MerchantRechargeTaskResp.LandingPageInfo();
        BeanUtils.copyProperties(landingPageInfo, landingPageInfo1);
        merchantRechargeTaskResp.setLandingPageInfo(landingPageInfo1);

        merchantRechargeTaskResp.setAwarding(activityDetailResp.getAwarding());
        merchantRechargeTaskResp.setHasAwarding(activityDetailResp.getHasAwarding());
        return merchantRechargeTaskResp;
    }

    /***
     * @Description：闯将商家有奖任务落地页详情
     * @param: activityDetailResp
     * @return：
     */
    private MerchantPrizeTaskResp createMerchantPrizeTaskResp(PageGetActivityDetailResp activityDetailResp) {
        PageGetActivityDetailResp.ActivityBaseInfo activityBaseInfo = activityDetailResp.getActivityBaseInfo();
        ActivityBaseExt activityBaseExt = activityDetailResp.getActivityBaseExt();
        PageGetActivityDetailResp.LandingPageInfo landingPageInfo = activityDetailResp.getLandingPageInfo();
        List<PageGetActivityDetailResp.ActivityTaskBo> activityTaskBoList = activityDetailResp.getActivityTaskLists();


        MerchantPrizeTaskResp merchantPrizeTaskResp = new MerchantPrizeTaskResp();
        MerchantPrizeTaskResp.ActivityBaseInfo activityBaseInfoOrderTask = new MerchantPrizeTaskResp.ActivityBaseInfo();
        activityBaseInfoOrderTask.setApplyCondition(activityBaseExt.getApplyCondition());
        BeanUtils.copyProperties(activityBaseInfo, activityBaseInfoOrderTask);
        merchantPrizeTaskResp.setActivityBaseInfo(activityBaseInfoOrderTask);

        List<MerchantPrizeTaskResp.ActivityTaskBo> activityTaskBoList1 = BeanCopyUtil.copyListProperties(activityTaskBoList, MerchantPrizeTaskResp.ActivityTaskBo::new, (taskBo, taskVo) -> {

            ActivityTaskBo.TaskRewardResp taskRewardResp = taskBo.getTaskRewardRespList().get(0);
            taskVo.setRewardExtraId(taskRewardResp.getRewardExtraId());
            taskVo.setRewardSymbol(taskRewardResp.getRewardSymbol());
            taskVo.setRewardGiveType(taskRewardResp.getRewardGiveType());
            taskVo.setRewardGiveValue(taskRewardResp.getRewardGiveValue());
        });

        merchantPrizeTaskResp.setActivityTaskLists(activityTaskBoList1);

        MerchantPrizeTaskResp.LandingPageInfo landingPageInfo1 = new MerchantPrizeTaskResp.LandingPageInfo();
        BeanUtils.copyProperties(landingPageInfo, landingPageInfo1);
        merchantPrizeTaskResp.setLandingPageInfo(landingPageInfo1);

        merchantPrizeTaskResp.setAwarding(activityDetailResp.getAwarding());
        merchantPrizeTaskResp.setHasAwarding(activityDetailResp.getHasAwarding());
        return merchantPrizeTaskResp;
    }

    /***
     * @Description：创建师傅订单详情落地页返回
     * @param: activityDetailResp
     * @return：
     */
    private MasterOrderTaskResp createMasterOrderTaskResp(PageGetActivityDetailResp activityDetailResp) {
        PageGetActivityDetailResp.ActivityBaseInfo activityBaseInfo = activityDetailResp.getActivityBaseInfo();
        ActivityBaseExt activityBaseExt = activityDetailResp.getActivityBaseExt();
        PageGetActivityDetailResp.LandingPageInfo landingPageInfo = activityDetailResp.getLandingPageInfo();
        List<PageGetActivityDetailResp.ActivityTaskBo> activityTaskBoList = activityDetailResp.getActivityTaskLists();

        MasterOrderTaskResp masterOrderTaskResp = new MasterOrderTaskResp();
        MasterOrderTaskResp.ActivityBaseInfo activityBaseInfoOrderTask = new MasterOrderTaskResp.ActivityBaseInfo();
        activityBaseInfoOrderTask.setApplyCondition(activityBaseExt.getApplyCondition());
        BeanUtils.copyProperties(activityBaseInfo, activityBaseInfoOrderTask);
        masterOrderTaskResp.setActivityBaseInfo(activityBaseInfoOrderTask);

        MasterOrderTaskResp.LandingPageInfo landingPageInfo1 = new MasterOrderTaskResp.LandingPageInfo();
        BeanUtils.copyProperties(landingPageInfo, landingPageInfo1);
        masterOrderTaskResp.setLandingPageInfo(landingPageInfo1);

        masterOrderTaskResp.setAwarding(activityDetailResp.getAwarding());
        masterOrderTaskResp.setHasAwarding(activityDetailResp.getHasAwarding());
        activityDetailResp = null;
        return masterOrderTaskResp;
    }

    /**
     * 拆解有奖任务奖励标签
     *
     * @param tagText
     */
    @Override
    public TagTextBo splitTagText(String tagText) {
        if (com.wanshifu.framework.utils.StringUtils.isBlank(tagText)) {
            return null;
        }
        TagTextBo tagTextBo = JSON.parseObject(tagText, TagTextBo.class);
        return tagTextBo;
    }

    /**
     * 优惠券批量查询券值
     * 卷包暂时无批量查询，过滤相同券包，减少查询次数
     *
     * @return
     */
    public Map<Long, VoucherRewardBo> voucherBatchGetRewardValueOfMerchantAd(List<MerchantActivityDetailAdResp.ActivityTaskBo> activityTaskList, String userClass) {

        Map<Long, VoucherRewardBo> voucherRewardBoMap = new HashMap<>();
        if (!UserTypeEnum.MERCHANT.type.equals(userClass) || CollectionUtils.isEmpty(activityTaskList)) {
            return null;
        }
        //复制方面后面删除操作
        List<MerchantActivityDetailAdResp.ActivityTaskBo> activityTaskListNew = new ArrayList<>();
        for (MerchantActivityDetailAdResp.ActivityTaskBo vo : activityTaskList) {
            MerchantActivityDetailAdResp.ActivityTaskBo bo = new MerchantActivityDetailAdResp.ActivityTaskBo();
            BeanUtils.copyProperties(vo, bo);
            activityTaskListNew.add(bo);
        }
        List<Integer> extraIdList = activityTaskListNew.stream().
                filter(f -> (RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) || RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()))
                        && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(extraIdList)) {
            return null;
        }
        /**
         * 先查缓存，缓存没有再往后查
         */
        List<String> cacheReqList = extraIdList.stream().map(m -> String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, m)).collect(Collectors.toList());
        List<String> voucherRewardCacheList = httpImageUtils.getBatchRedisKeyValue(cacheReqList);
        if (CollectionUtils.isNotEmpty(voucherRewardCacheList)) {
            for (String str : voucherRewardCacheList) {
                VoucherRewardBo bo = JSON.parseObject(str, VoucherRewardBo.class);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
            }
            Iterator<MerchantActivityDetailAdResp.ActivityTaskBo> iter = activityTaskListNew.iterator();
            while (iter.hasNext()) {
                MerchantActivityDetailAdResp.ActivityTaskBo bo = iter.next();
                if (voucherRewardBoMap.get(bo.getRewardExtraId()) != null) {
                    iter.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(activityTaskListNew)) {
            return voucherRewardBoMap;
        }
        /**
         * 优惠券
         */
        List<Integer> rewardExtraIdList = activityTaskListNew.stream().
                filter(f -> RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        List<VoucherEvent> voucherEventList = null;
        if (CollectionUtils.isNotEmpty(rewardExtraIdList)) {

            GetVoucherEventListReqBean getVoucherEventListReqBean = new GetVoucherEventListReqBean();
            getVoucherEventListReqBean.setEventIdList(rewardExtraIdList);
            voucherEventList = voucherEventApi.getVoucherEventList(getVoucherEventListReqBean);

        }
        if (!CollectionUtils.isEmpty(voucherEventList)) {
            voucherEventList.stream().forEach(voucherEvent -> {
                VoucherRewardBo bo = new VoucherRewardBo();
                //优惠券类型 （money_off:满减-默认 、discount: 折扣）
                bo.setVoucherType(voucherEvent.getMold());
                BigDecimal resultAmount = BigDecimal.ZERO;
                if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherEvent.getMold())) {
                    resultAmount = voucherEvent.getDiscountRatio() == null ? new BigDecimal("0") : voucherEvent.getDiscountRatio();
                    String amountWeight = voucherEvent.getAmountWeight();
                    bo.setDiscountMaxValue(new BigDecimal(amountWeight.split(":")[0]));
                } else {
                    String amountWeight = voucherEvent.getAmountWeight();
                    if (!org.springframework.util.StringUtils.isEmpty(amountWeight)) {
                        if (amountWeight.contains(":")) {
                            resultAmount = new BigDecimal(amountWeight.split(":")[0]);
                        } else {
                            resultAmount = new BigDecimal(amountWeight);
                        }
                    }
                }
                bo.setRewardExtraId(voucherEvent.getEventId().longValue());
                bo.setRewardValue(resultAmount);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                redisHelper.set(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, bo.getRewardExtraId()), JSON.toJSONString(bo), voucherCacheTime);
            });
        }

        /**
         * 券包：满减券面额之和+折扣券最高抵扣金额之和
         */
        List<Integer> packExtraIdList = activityTaskListNew.stream().
                filter(f -> RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packExtraIdList)) {
            BatchQueryTotalAmountByIdReq queryTotalAmountByIdReq1 = new BatchQueryTotalAmountByIdReq();
            queryTotalAmountByIdReq1.setVoucherPackIdList(packExtraIdList);
            BatchQueryTotalAmountByIdResp queryTotalAmountByIdResp1 = voucherEventApi.batchQueryTotalAmountById(queryTotalAmountByIdReq1);
            if (queryTotalAmountByIdResp1 != null && queryTotalAmountByIdResp1.getPackIdForTotalAmount() != null) {
                Map<Integer, BigDecimal> resultMap = queryTotalAmountByIdResp1.getPackIdForTotalAmount();
                packExtraIdList.stream().forEach(packExtraId -> {
                    if (!ObjectUtils.isEmpty(resultMap.get(packExtraId))) {
                        VoucherRewardBo bo = new VoucherRewardBo();
                        bo.setRewardExtraId(packExtraId.longValue());
                        bo.setRewardValue(resultMap.get(packExtraId));
                        voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                        redisHelper.set(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, bo.getRewardExtraId()), JSON.toJSONString(bo), voucherCacheTime);
                    }
                });
            }

        }
        activityTaskListNew = null;
        return voucherRewardBoMap;
    }
}