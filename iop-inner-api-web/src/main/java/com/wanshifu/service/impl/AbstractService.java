package com.wanshifu.service.impl;
import com.wanshifu.bo.LoginUserInfoBo;
import com.wanshifu.iop.activity.domain.api.response.RewardInfoResp;
import com.wanshifu.iop.activity.domain.api.response.center.MerchantCenterRewardListSubResp;
import com.wanshifu.iop.activity.domain.bo.OptionalRewardInfoBo;
import com.wanshifu.iop.activity.domain.enums.ActivityTypeEnum;
import com.wanshifu.iop.ad.api.AdConfigInfoApi;
import com.wanshifu.iop.ad.domain.bo.AdForwardUrlBo;
import com.wanshifu.iop.equity.domain.api.request.prizeWheelBusiness.ListRemainNumReq;
import com.wanshifu.iop.equity.domain.api.response.ListRemainNumResp;
import com.wanshifu.iop.equity.service.api.PrizeWheelBusinessServiceApi;
import com.wanshifu.iop.equity.service.api.VipEquityBusinessServiceApi;
import com.wanshifu.iop.inner.api.domains.bo.entity.TagTextBo;
import com.wanshifu.iop.inner.api.domains.bo.mq.master.QuotationOrderMqBo;
import com.wanshifu.iop.inner.api.domains.enums.MasterOrderLabelEnum;
import com.wanshifu.iop.inner.api.domains.enums.TaskSymbolEnum;
import com.wanshifu.iop.inner.api.domains.request.bigData.*;
import com.wanshifu.iop.inner.api.domains.response.OptionalRewardInfoVo;
import com.wanshifu.iop.inner.api.domains.response.RewardInfoVo;
import com.wanshifu.iop.inner.api.domains.response.bigdata.CustomerGroupIdResp;
import com.wanshifu.iop.inner.api.domains.vo.resp.CustomerUserInfoWebDtoResp;
import com.wanshifu.iop.inner.api.domains.vo.resp.UserInfoWebDtoResp;
import com.wanshifu.iop.marketing.config.service.api.MarketingActivityServiceApi;
import com.wanshifu.iop.play.method.api.FreeVoucherGetBackendServiceApi;
import com.wanshifu.master.innerapi.domains.request.order.push.BatchGetPushDistanceRqt;
import com.wanshifu.master.innerapi.domains.response.ocs.BatchGetPushDistanceResp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.InterfaceFeign.MasterInterface;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.constant.CacheKeyConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.bean.BatchRedisSetBean;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.activity.domain.api.request.RewardConfigRqt;
import com.wanshifu.iop.activity.domain.api.response.RewardConfigResp;
import com.wanshifu.iop.activity.domain.bo.ActivityTaskBeanBo;
import com.wanshifu.iop.activity.domain.enums.RewardSymbol;
import com.wanshifu.iop.activity.service.api.*;
import com.wanshifu.iop.ad.api.AdBaseInfoApi;
import com.wanshifu.iop.ad.api.AdPullApi;
import com.wanshifu.iop.inner.api.domains.bo.*;
import com.wanshifu.iop.inner.api.domains.enums.UserTypeApiEnum;
import com.wanshifu.iop.inner.api.domains.request.master.OrderInfoExtTemp;
import com.wanshifu.iop.inner.api.domains.request.master.OrderInfoTemp;
import com.wanshifu.iop.inner.api.domains.response.bigdata.MasterGroupIdResp;
import com.wanshifu.iop.inner.api.domains.response.bigdata.UserGroupIdResp;
import com.wanshifu.iop.inner.api.domains.vo.resp.MasterInfoDtoResp;
import com.wanshifu.master.innerapi.domains.request.activity.ioc.GetOrderListForFulfillment;
import com.wanshifu.master.innerapi.domains.request.activity.ioc.GetOrderListForWaitOffer;
import com.wanshifu.master.innerapi.domains.request.masterInfo.GetMasterInfoRqtBean;
import com.wanshifu.master.innerapi.domains.response.activity.ioc.GetOrderListForFulfillmentResp;
import com.wanshifu.master.innerapi.domains.response.activity.ioc.GetOrderListForWaitOfferResp;
import com.wanshifu.master.innerapi.domains.response.master.GetMasterInfoRspBean;
import com.wanshifu.master.innerapi.service.api.master.MasterInfoApi;
import com.wanshifu.mq.producer.MerchantDelayVisitProducer;
import com.wanshifu.order.config.api.ServeServiceApi;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdSetReq;
import com.wanshifu.order.offer.domains.api.request.offer.OrderExclusiveTagResp;
import com.wanshifu.sdk.BigDataAliYunPersonaApi;
import com.wanshifu.sdk.BigDataBatchGroupIdApi;
import com.wanshifu.sdk.BigDataGroupIdApi;
import com.wanshifu.sdk.BigDataOrderApi;
import com.wanshifu.service.CustomerService;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import com.wanshifu.toc.user.voucher.api.TocCouponApi;
import com.wanshifu.toc.user.voucher.api.TocCouponPackApi;
import com.wanshifu.user.vip.service.api.toc.TocVipCouponApi;
import com.wanshifu.user.voucher.api.VoucherEventApi;
import com.wanshifu.user.voucher.api.VoucherPackApi;
import com.wanshifu.user.voucher.domain.api.request.BatchQueryTotalAmountByIdReq;
import com.wanshifu.user.voucher.domain.api.request.GetVoucherEventListReqBean;
import com.wanshifu.user.voucher.domain.api.request.GetVoucherPackByIdsReq;
import com.wanshifu.user.voucher.domain.api.response.BatchQueryTotalAmountByIdResp;
import com.wanshifu.user.voucher.domain.po.VoucherEvent;
import com.wanshifu.user.voucher.domain.po.VoucherPackConfig;
import com.wanshifu.util.*;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.wanshifu.iop.activity.domain.enums.RewardSymbol.VOUCHER;

@Slf4j
public abstract class AbstractService {

    @Resource
    public ActivityBusinessServiceApi activityBusinessServiceApi;

    @Resource
    public ServeServiceApi serveServiceApi;
    @Resource
    public AdConfigInfoApi adConfigInfoApi;


    @Resource
    public RedisExUtil redisExUtil;
    @Resource
    public VoucherGiveUtil voucherGiveUtil;

    @Resource
    public HttpImageUtils httpImageUtils;

    @Resource
    public AdBaseInfoApi adBaseInfoApi;

    @Resource
    public AdPullApi adPullApi;

    @Resource
    public CustomerService customerService;

    @Resource
    public VoucherEventApi voucherEventApi;

    @Resource
    public VoucherPackApi voucherPackApi;
    @Resource
    public TocCouponPackApi tocCouponPackApi;
    @Resource
    public TocCouponApi tocCouponApi;
    @Resource
    public BigDataAliYunPersonaApi bigDataAliYunPersonaApi;

    @Resource
    public RedisHelper redisHelper;

    @Resource
    public MasterActivityBusinessServiceApi masterActivityBusinessServiceApi;
    @Resource
    public LandingPageServiceApi landingPageServiceApi;

    @Resource
    public CombinedPageServiceApi combinedPageServiceApi;

    @Resource
    public MerchantActivityServiceApi merchantActivityServiceApi;

    @Resource
    public ActivityBackendServiceApi activityBackendServiceApi;
    @Resource
    public MerhcnatActivityCenterServiceApi activityCenterServiceApi;
    @Resource
    public MarketingActivityServiceApi marketingActivityServiceApi;

    @Resource
    public FreeVoucherGetBackendServiceApi freeVoucherGetBackendServiceApi;

    @Resource
    public FreeVoucherActivityServiceApi freeVoucherActivityServiceApi;

    @Resource
    public TocVipCouponApi tocVipCouponApi;

    @Resource
    public VipEquityBusinessServiceApi vipEquityBusinessServiceApi;

    @Resource
    public FeishuUtils feishuUtils;

    @Resource
    public CoverUtils coverUtils;
    @Value("${wanshifu.common-bigdata-openapi.appcode}")
    public String bigToken;
    @Value("${wanshifu.bigdataGroup.cacheTime:0}")
    public Integer bigdataGroupCacheTime;
    /**
     * 师傅有奖任务链接
     */
    @Value("${activity.master.awardTask.url}")
    private String awardTaskUrl;

    /**
     * 师傅订单活动
     */
    @Value("${wanshifu.master.orderActivity.url}")
    private String orderActivityUrl;
    @Resource
    public MerchantDelayVisitProducer merchantDelayVisitProducer;
    @Resource
    public MasterInfoApi masterInfoApi;
    @Resource
    private HttpServletRequest httpServletRequest;
    @Resource
    private BigDataGroupIdApi bigDataGroupIdApi;

    /**
     * 优惠券/券包缓存时间
     */
    @Value("${wanshifu.voucherReward.redisCacheTime:10}")
    private Integer voucherCacheTime;
    //拉取师傅开屏广告优先级缓存时间：单位秒
    @Value("${wanshifu.priorityForPullingAds.redisCacheTime:0}")
    public Integer priorityForPullingAdsTime;
    /**
     * 广告热启动配置，针对不同的广告位有不同的参数
     */
    @Value("${wanshifu.ad.Hot.start.param}")
    public String adHotStartParam;
    @Resource
    private BigDataBatchGroupIdApi bigDataBatchGroupIdApi;
    @Resource
    public PrizeWheelBusinessServiceApi prizeWheelBusinessServiceApi;
    @Resource
    private BigDataOrderApi bigDataOrderApi;
    @Resource
    private MasterUtil masterUtil;

    protected String getErrorMsg(String msg) {
        String[] split = msg.split("【业务异常】");

        if (split.length > 1) {
            return split[1].replace("]", "");
        } else {
            return msg;
        }
    }

    protected String getErrorMsgNew(String msg) {
        String[] split;
        if(!msg.contains("【业务异常】")){
            split = msg.split("retMsg : ");
        } else {
            split = msg.split("【业务异常】");
        }


        if (split.length > 1) {
            return split[1].replace("]", "");
        } else {
            return msg;
        }
    }
    protected String getErrorMsg2(String msg) {
        //格式：：接口：[http://dev-iop-activity-service.wanshifu.com/activity/user/taskGiveReward] 调用异常！ 返回：[retCode : -99999  retData : null retMsg : 发放服务抽奖机会调用失败]
        if(msg.contains("-99999")){

            return msg.substring(msg.lastIndexOf(": ")+1,msg.lastIndexOf("]"));
        }
        return msg;

    }

    @Resource
    public MasterInterface masterInterface;

    /**
     * 获取订单信息
     */
    public List<OrderInfoTemp> returnOrderBaseInfo(List<Long> orderIdList, Long userId) {
        List<OrderInfoTemp> orderBaseInfoList = new ArrayList<>();

        GetOrderListForWaitOffer getOrderListForWaitOffer = new GetOrderListForWaitOffer();
        getOrderListForWaitOffer.setOrderIdList(orderIdList);
        getOrderListForWaitOffer.setMasterId(userId);
        GetOrderListForWaitOfferResp orderListForWaitOffer = null;
        try{
            orderListForWaitOffer = masterInterface.getOrderBaseInfos(getOrderListForWaitOffer);
        }catch (Exception e){
            log.error("获取订单信息异常，请求参数={}, 异常信息={}",JSON.toJSONString(orderIdList),e.getMessage());
        }
        if(orderListForWaitOffer==null){
            return orderBaseInfoList;
        }

        for (GetOrderListForWaitOfferResp.OrderItem getOrderListForWaitOfferResp : orderListForWaitOffer.getOrderList()) {
            OrderInfoTemp orderInfoTemp = new OrderInfoTemp();
            BeanUtils.copyProperties(getOrderListForWaitOfferResp,orderInfoTemp);
            orderInfoTemp.setCreateOrderTime(getOrderListForWaitOfferResp.getOrderCreateTime());
            //2023年12月6日21:22:35 代码调整，入参是"masterId": 30305900147,orderId：42396249035，发现数据类型不一致，导致订单没有标签展示
            if(null!=getOrderListForWaitOfferResp.getOfferNum()){
                orderInfoTemp.setOfferNum(Long.valueOf(getOrderListForWaitOfferResp.getOfferNum()));
            }
            if(CollectionUtils.isEmpty(getOrderListForWaitOfferResp.getOrderExclusiveTagResps())){
                orderInfoTemp.setIsOrderShop(0);
                orderBaseInfoList.add(orderInfoTemp);
                continue;
            }
            List<OrderExclusiveTagResp> orderExclusiveTagResps = getOrderListForWaitOfferResp.getOrderExclusiveTagResps();
            Optional<OrderExclusiveTagResp> optional = orderExclusiveTagResps.stream().filter(o -> o.getTagType().equals(MasterOrderLabelEnum.MASTER_SHOP.label)).findFirst();
            if(optional.isPresent()){
                orderInfoTemp.setIsOrderShop(1);
            } else {
                orderInfoTemp.setIsOrderShop(0);
            }

            orderInfoTemp.setPropertyList(masterUtil.buildPropertyList(orderExclusiveTagResps));

            orderBaseInfoList.add(orderInfoTemp);
        }
        return orderBaseInfoList;
    }

    /**
     * 返回免打扰开始时间
     * 如果位于20点-24点 就返回下单时间
     * 如果位于第二天0点到第二天8点 返回下单时间
     * */
    private Date returnFreeStartTime(GetOrderListForFulfillmentResp.OrderItem getOrderListForWaitOfferResp){
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        LocalDateTime twentyOClock = now.toLocalDate().atTime(20, 0);
        // 将LocalDateTime转换为Date
        Date date20 = Date.from(twentyOClock.atZone(ZoneId.systemDefault()).toInstant());

            // 获取第二天0点时间，即当天24点时间
        LocalDateTime twentyFourOClock = now.toLocalDate().plusDays(1).atTime(0, 0);
        Date date24 = Date.from(twentyFourOClock.atZone(ZoneId.systemDefault()).toInstant());

        // 获取第二天8点时间
        LocalDateTime tomorrowEightOClock = now.toLocalDate().atTime(8, 0);
        Date date18 = Date.from(tomorrowEightOClock.atZone(ZoneId.systemDefault()).toInstant());

        //如果位于20点-24点 就返回下单时间
        if(getOrderListForWaitOfferResp.getOrderCreateTime().after(date20) && getOrderListForWaitOfferResp.getOrderCreateTime().before(date24)){
            return getOrderListForWaitOfferResp.getOrderCreateTime();
        }
        //如果位于第二天0点到第二天8点 返回下单时间
        if(getOrderListForWaitOfferResp.getOrderCreateTime().after(date24) && getOrderListForWaitOfferResp.getOrderCreateTime().before(date18)){
            return getOrderListForWaitOfferResp.getOrderCreateTime();
        }
        return getOrderListForWaitOfferResp.getFreeStartTime();
    }



    /**
     * 获取订单信息 完整信息
     */
    public List<OrderInfoExtTemp> returnOrderBaseExtInfo(List<Long> orderIdList,Long userId) {
        List<OrderInfoExtTemp> orderBaseInfoList = new ArrayList<>();
        GetOrderListForFulfillment getOrderListForWaitOffer = new GetOrderListForFulfillment();
        getOrderListForWaitOffer.setOrderIdList(orderIdList);
        getOrderListForWaitOffer.setMasterId(userId);
        GetOrderListForFulfillmentResp orderListForWaitOffer = null;
        try{
            orderListForWaitOffer = masterInterface.getOrderBaseInfos(getOrderListForWaitOffer);
        }catch (Exception e){
            log.error("获取订单信息异常，请求参数={}, 异常信息={}",JSON.toJSONString(orderIdList),e.getMessage());
        }
        if(orderListForWaitOffer==null){
            return orderBaseInfoList;
        }

        //获取技能是否相关
        Map<String, Integer> skillRelatedStateMap = this.getSkillRelatedState(userId, orderIdList);
        //获取订单标签
        List<OrderExclusiveTagResp> masterOrderTagList = masterUtil.getMasterOrderTagList(orderIdList, userId);
        Map<Long, List<OrderExclusiveTagResp>> orderTagMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(masterOrderTagList)){
            orderTagMap = masterOrderTagList.stream().collect(Collectors.groupingBy(t -> t.getOrderId()));
        }
        for (GetOrderListForFulfillmentResp.OrderItem getOrderListForWaitOfferResp : orderListForWaitOffer.getOrderList()) {
            OrderInfoExtTemp orderInfoTemp = new OrderInfoExtTemp();
            BeanUtils.copyProperties(getOrderListForWaitOfferResp,orderInfoTemp);
            orderInfoTemp.setOfferNum(0L);
            orderInfoTemp.setCreateOrderTime(getOrderListForWaitOfferResp.getOrderCreateTime());
            if(orderInfoTemp.getFreeStartTime()==null && orderInfoTemp.getFreeEndTime()==null){
                // 获取当前日期
                LocalDateTime now = LocalDateTime.now();
                // 设置时间为23点58分
                LocalDateTime today2358 = now.toLocalDate().atTime(23, 58);
                LocalDateTime today2359 = now.toLocalDate().atTime(23, 59);
                // 将LocalDateTime转换为Date
                Date dateStart = Date.from(today2358.atZone(ZoneId.systemDefault()).toInstant());
                Date dateEnd = Date.from(today2359.atZone(ZoneId.systemDefault()).toInstant());
                orderInfoTemp.setFreeStartTime(dateStart);
                orderInfoTemp.setFreeEndTime(dateEnd);
            }else{
                orderInfoTemp.setFreeStartTime(this.returnFreeStartTime(getOrderListForWaitOfferResp));
            }
            List<String> masterOrderLabelList = getOrderListForWaitOfferResp.getMasterOrderLabelList();
            //是否是师傅店铺订单
            if(CollectionUtils.isNotEmpty(masterOrderLabelList) && masterOrderLabelList.contains(MasterOrderLabelEnum.MASTER_SHOP.label)){
                orderInfoTemp.setIsOrderShop(1);
            } else {
                orderInfoTemp.setIsOrderShop(0);
            }

            orderInfoTemp.setPropertyList(masterUtil.buildPropertyList(orderTagMap.get(orderInfoTemp.getOrderId())));

            //技能是否相关
            orderInfoTemp.setSkillRelatedState(skillRelatedStateMap.get(String.valueOf(orderInfoTemp.getOrderId())));
            orderBaseInfoList.add(orderInfoTemp);
        }
        return orderBaseInfoList;
    }

    /***
     * @Description：根据传入的技能组，合并成一个Json 字符串
     * @param: firstSkillIds
     * @param: secondSkillIds
     * @return：
     */
    public String createSkillStrByExtend(String firstSkillIds, String secondSkillIds) {
        // first json array
        JSONArray resultJson = new JSONArray();
        if (com.wanshifu.framework.utils.StringUtils.isNotBlank(firstSkillIds)) {
            JSONArray first = JSONArray.parseArray(firstSkillIds);
            resultJson.addAll(first);
            first = null;
        }
        if (com.wanshifu.framework.utils.StringUtils.isNotBlank(secondSkillIds)) {
            JSONArray second = JSONArray.parseArray(secondSkillIds);
            resultJson.addAll(second);
            second = null;
        }
        if (resultJson.isEmpty()) {
            return null;
        } else {
            return resultJson.toJSONString();
        }
    }

    public List<String> getListNames(Long userId) {
        GetMasterInfoRqtBean getMasterInfoRqtBean = new GetMasterInfoRqtBean();
        getMasterInfoRqtBean.setMasterId(userId);
        GetMasterInfoRspBean getMasterInfoResp = masterInfoApi.getMasterInfo(getMasterInfoRqtBean);
        if (getMasterInfoResp == null || com.wanshifu.framework.utils.StringUtils.isBlank(getMasterInfoResp.getContact())) {
            throw new BusException("师傅信息查询为空");
        }
        //截取姓名
        List<String> listNames = ActivityUtil.nameSplit(getMasterInfoResp.getContact());
        return listNames;
    }

    /**
     * 获取单个人群信息返回信息组装
     */
    public TargetGroupIdRespBo getTargetGroupId(String userId, String personaId) {

        TargetGroupIdRespBo targetGroupIdRespBo = new TargetGroupIdRespBo();

        UserGroupIdResp userGroupIdResp = null;
        MasterGroupIdResp masterGroupIdResp = null;

        try {
            if ("1".equals(personaId)) {
                //商家
                UserSingleGroupIdReq userSingleGroupIdReq = new UserSingleGroupIdReq();
                userSingleGroupIdReq.setUserId(Long.valueOf(userId));
                userSingleGroupIdReq.setAppId(ActivityConstant.APPID);
                userGroupIdResp = bigDataGroupIdApi.userSingleGroupId(userSingleGroupIdReq);
                targetGroupIdRespBo.setTargetId(userGroupIdResp.getUserId());
                targetGroupIdRespBo.setGroupIds(userGroupIdResp.getGroupIds());
            } else if ("2".equals(personaId)){
                //师傅
                MasterSingleGroupIdReq masterSingleGroupIdReq = new MasterSingleGroupIdReq();
                masterSingleGroupIdReq.setMasterId(Long.valueOf(userId));
                masterSingleGroupIdReq.setAppId(ActivityConstant.APPID);
                masterGroupIdResp = bigDataGroupIdApi.masterSingleGroupId(masterSingleGroupIdReq);
                targetGroupIdRespBo.setTargetId(masterGroupIdResp.getMasterId());
                targetGroupIdRespBo.setGroupIds(masterGroupIdResp.getGroupIds());
            } else if ("3".equals(personaId)){
                //家庭
                CustomerSingleGroupIdReq customerSingleGroupIdReq = new CustomerSingleGroupIdReq();
                customerSingleGroupIdReq.setUserId(Long.valueOf(userId));
                customerSingleGroupIdReq.setAppId(ActivityConstant.APPID);
                CustomerGroupIdResp customerGroupIdResp = bigDataGroupIdApi.customerSingleGroupId(customerSingleGroupIdReq);
                targetGroupIdRespBo.setTargetId(customerGroupIdResp.getUserId());
                targetGroupIdRespBo.setGroupIds(customerGroupIdResp.getGroupIds());
            } else if ("4".equals(personaId)){
                //c端师傅
                MasterSingleGroupIdReq masterSingleGroupIdReq = new MasterSingleGroupIdReq();
                masterSingleGroupIdReq.setMasterId(Long.valueOf(userId));
                masterSingleGroupIdReq.setAppId(ActivityConstant.APPID);
                masterGroupIdResp = bigDataGroupIdApi.tocMasterSingleGroupId(masterSingleGroupIdReq);
                targetGroupIdRespBo.setTargetId(masterGroupIdResp.getMasterId());
                targetGroupIdRespBo.setGroupIds(masterGroupIdResp.getGroupIds());
            }

            return targetGroupIdRespBo;
        } catch (RetryableException e) {
            return targetGroupIdRespBo;
        } catch (ApiAccessException e) {
            e.printStackTrace();
            throw new BusException("获取大数据人群请求失败");
        }


    }

    /**
     * 返回标签信息
     * 画像类型ID,personaId（1、商家画像2、师傅画像 其他待补充）
     */
    public List<Long> getUserGroupIds(String userId, String personaId) {


        String redisKey = String.format(CacheKeyConstant.USER_GROUP_KEY_V2, userId);

        //如果存在,get方法无需提前判断exist
        String cacheValue = redisHelper.get(redisKey);
        if (!StringUtils.isEmpty(cacheValue)) {
            List<Long> cacheList = JSONObject.parseArray(cacheValue, Long.class);
            if (!CollectionUtils.isEmpty(cacheList)) {
                return cacheList;
            }
        }

        List<Long> labelIdList = new ArrayList<>();

        //单个用户请求
        TargetGroupIdRespBo targetGroupId = this.getTargetGroupId(userId, personaId);

        if (targetGroupId != null) {
            String groupids = targetGroupId.getGroupIds();
            if (!StringUtils.isEmpty(groupids)) {
                labelIdList = Arrays.stream(groupids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(labelIdList) && bigdataGroupCacheTime > 0) {
                    redisHelper.set(redisKey, JSON.toJSONString(labelIdList), bigdataGroupCacheTime);
                }
            }
        }
        return labelIdList;
    }

    /**
     * 批量查询用户id
     */
    public Map<Long, List<Long>> batchSelectUserIdList(List<Long> userIdList) {

        Map<Long, List<Long>> result = new HashMap<>();

        //缓存没有数据的userIdList
        List<Long> redisNoExistUserIdList = new ArrayList<>();
        List<String> userIdRedisStringList = userIdList.stream().map(e -> String.format(CacheKeyConstant.USER_GROUP_KEY_V2, e)).collect(Collectors.toList());
        HashMap<String, String> redisCrowIdMap = redisHelper.batchGet(userIdRedisStringList);
        userIdList.forEach(userId -> {
            String key = String.format(CacheKeyConstant.USER_GROUP_KEY_V2, userId);
            String crowIdListString = redisCrowIdMap.get(key);

            //如果缓存中没有
            if (StringUtils.isEmpty(crowIdListString)) {
                redisNoExistUserIdList.add(userId);
            }
            List<Long> list = JSONObject.parseArray(crowIdListString, Long.class);
            result.put(userId, list);
        });
        //判断缓存中没有userId的list是否为空
        if (CollectionUtils.isEmpty(redisNoExistUserIdList)) {
            return result;
        }

        //否则需要请求大数据接口
        Map<Long, List<Long>> longListMap = this.batchBigCrowdMap(redisNoExistUserIdList);
        if (longListMap.size() == 0) {
            return result;
        }
        longListMap.forEach((k, v) -> {
            result.put(k, v);
        });

        return result;
    }

    @Value("${wanshifu.merchantBatch.open}")
    private Boolean merchantBatchOpen;

    /**
     * 批量请求大数据接口获取用户人群数据
     */
    public Map<Long, List<Long>> batchBigCrowdMap(List<Long> userIdList) {

        Map<Long, List<Long>> result = new HashMap<>();
        if(!merchantBatchOpen){
            return result;
        }


        UserBatchGroupIdReq userBatchGroupIdReq = new UserBatchGroupIdReq();
        userBatchGroupIdReq.setUserIds(userIdList);
        userBatchGroupIdReq.setAppId(ActivityConstant.APPID);
        List<UserGroupIdResp> userGroupIdResps = new ArrayList<>();
        try {

            userGroupIdResps = bigDataBatchGroupIdApi.userBatchGroupId( userBatchGroupIdReq);
        } catch (Exception e) {
            log.info("商家人群批量接口请求超时/dataApi/getList/getPersonaGroupIdsByUserIds");
        }

        if (CollectionUtils.isNotEmpty(userGroupIdResps)) {
            for (UserGroupIdResp userGroupIdResp : userGroupIdResps) {
                if(!StringUtils.isEmpty(userGroupIdResp.getGroupIds())){
                    List<Long> userCrowIdList = new ArrayList<>();
                    userCrowIdList = Arrays.stream(userGroupIdResp.getGroupIds().split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    result.put(userGroupIdResp.getUserId(), userCrowIdList);
                    //存入缓存
                    this.saveRedis(String.format(CacheKeyConstant.USER_GROUP_KEY_V2, userGroupIdResp.getUserId()),
                            userCrowIdList);
                }
            }
        }
        return result;
    }

    /**
     * 数据存入缓存
     */
    public void saveRedis(String key, List<Long> labelIdList) {
        if(bigdataGroupCacheTime > 0){
            redisHelper.set(key, JSONObject.toJSONString(labelIdList), bigdataGroupCacheTime);
        }
    }

    /**
     * 执行频率文案
     * 设置任务描述，最多领取三次
     */
    public String getTaskTitleDesc(ActivityTaskBeanBo activityTask) {

        try {
            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");

            //任务执行类型[single:单次,cycle:持续循环,day:每天,week:每周,month:每月]
            switch (activityTask.getExecuteType()) {
                case "single":
                    return ActivityConstant.single_text1;
                case "cycle":
                    return String.format(ActivityConstant.cycle_text1, activityTask.getRewardUserUpperLimit());
                case "day":
                    String startHour = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()));
                    String endHour = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()));
                    if (ActivityConstant.day_start.equals(activityTask.getExecuteTimeRangeStart()) && ActivityConstant.day_end.equals(activityTask.getExecuteTimeRangeEnd())) {
                        return String.format(ActivityConstant.day_text1, activityTask.getRewardUserDayUpperLimit());
                    }
                    return String.format(ActivityConstant.day_text2, startHour, endHour, activityTask.getRewardUserDayUpperLimit());
                case "week":
                    String executeRange = activityTask.getExecuteRange();
                    List<Integer> weeks = JSONObject.parseArray(executeRange, Integer.class);

                    Map<Integer, String> weekMap = new HashMap<>();
                    weekMap.put(1, "周一");
                    weekMap.put(2, "周二");
                    weekMap.put(3, "周三");
                    weekMap.put(4, "周四");
                    weekMap.put(5, "周五");
                    weekMap.put(6, "周六");
                    weekMap.put(7, "周日");

                    List<String> weekstr = new ArrayList<>();
                    for (Integer week : weeks) {
                        weekstr.add(weekMap.get(week));
                    }
                    String join = String.join("、", weekstr);
                    if (activityTask.getRewardUserDayUpperLimit() != 0) {
                        return "每" + join + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每天封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
                    } else {
                        return "每" + join + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每周封顶" + activityTask.getRewardUserWeekUpperLimit() + "次";
                    }
                case "month":
                    List<Integer> months = JSONObject.parseArray(activityTask.getExecuteRange(), Integer.class);
                    String monthStr = "";

                    for (Integer object : months) {
                        if (object != null) {
                            monthStr += object + "、";
                        }
                    }
                    monthStr = StringUtils.trimTrailingCharacter(monthStr, '、');

                    if (activityTask.getRewardUserDayUpperLimit() != 0) {
                        return "每月" + monthStr + "号" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每日封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
                    } else {
                        return "每月" + monthStr + "号" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每月封顶" + activityTask.getRewardUserMonthUpperLimit() + "次";
                    }
                default:
                    return null;
            }

        } catch (Exception e) {
            throw new BusException("data_parse_data_error", e.getMessage());
        }
    }

    public Long getUserIdByTokenAndUserClass(String token, String userClass) {
        //商家
        Long userId = redisExUtil.checkLoginStatus(token, userClass);
        return userId;
    }

    public Long getMasterIdBySignature(String signature) {
        //师傅
        String valueBykey = redisExUtil.getValueBykey(signature, UserTypeApiEnum.MASTER.type);
        JSONObject userInfo = JSONObject.parseObject(valueBykey);

        MasterInfoDtoResp masterInfoWebDtoResp = JSONObject.toJavaObject(userInfo, MasterInfoDtoResp.class);
        return masterInfoWebDtoResp.getMasterId();
    }

    public Long getMerchantUserId(String token, String appToken) {
        //商家
        Long userId = null;
        if (!StringUtils.isEmpty(appToken)) {
            userId = redisExUtil.checkLoginStatus(appToken, UserTypeApiEnum.MERCHANT.type);
        } else if (!StringUtils.isEmpty(token)) {
            userId = redisExUtil.checkLoginStatus(token, UserTypeApiEnum.MERCHANT.type);
        } else {
            return null;
        }
        return userId;
    }

    public String getCookiesToken() {
        Cookie[] cookies = httpServletRequest.getCookies();
        String token = null;
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("wsf_user_token".equals(cookie.getName())) {
                    token = cookie.getValue();
                }
            }
        }
        return token;
    }

    public LoginUserInfoBo getUserLoginInfo(String token, String userClass) {

        LoginUserInfoBo result = new LoginUserInfoBo();

        UserTypeApiEnum userTypeApiEnum = UserTypeApiEnum.fromType(userClass);

        String valueBykey = redisExUtil.getValueBykey(token, userTypeApiEnum.type);
        JSONObject userInfo = JSONObject.parseObject(valueBykey);

        switch (userTypeApiEnum) {
            case MERCHANT:
                UserInfoWebDtoResp userInfoWebDtoResp = JSONObject.toJavaObject(userInfo, UserInfoWebDtoResp.class);
                result.setUserId(userInfoWebDtoResp.getUserId());
                result.setName("");
                result.setPhone("");
                return result;
            case MASTER:
                MasterInfoDtoResp masterInfoWebDtoResp = JSONObject.toJavaObject(userInfo, MasterInfoDtoResp.class);
                result.setUserId(masterInfoWebDtoResp.getMasterId());
                result.setName(masterInfoWebDtoResp.getMasterName());
                result.setPhone(masterInfoWebDtoResp.getPhone());
                return result;
            case CUSTOMER:
                CustomerUserInfoWebDtoResp customerUserInfoWebDtoResp = JSONObject.toJavaObject(userInfo, CustomerUserInfoWebDtoResp.class);
                result.setName(customerUserInfoWebDtoResp.getUser().getNickname());
                result.setUserId(customerUserInfoWebDtoResp.getUser().getUserId());
                result.setPhone(customerUserInfoWebDtoResp.getUser().getMobile());
                return result;
            default:
                return result;
        }
    }
    /**
     * 获取服务信息
     */
    public Map<Long, String> getServeIdsMap(List<Long> serveIdList, Long activityId, Long activityTaskId,Long businessLineId) {

        Map<Long, String> serveNameMap = new HashMap<>();

        //键
        String cashKey = String.format(CacheKeyConstant.ACTIVITY_PAGE_SERVE_1, activityId,activityTaskId, businessLineId);

        String serveString = redisHelper.get(cashKey);
        if (!StringUtils.isEmpty(serveString)) {
            Map map = JSON.parseObject(serveString, Map.class);
            map.forEach((k, v) -> {
                serveNameMap.put(Long.valueOf(k.toString()), v.toString());
            });
            return serveNameMap;
        }
        Set<Long> serveIdSet = new HashSet<>();
        serveIdSet.addAll(serveIdList);

        List<ServeBaseInfoResp> serves = new ArrayList<>();
        if (serveIdSet.size() > 0) {
            ServeIdSetReq serveIdSetReq = new ServeIdSetReq();
            serveIdSetReq.setServeIdSet(serveIdSet);
            serveIdSetReq.setBusinessLineId(businessLineId);
            serves = serveServiceApi.getServeBaseInfo(serveIdSetReq);
        }

        if (CollectionUtils.isEmpty(serves)) {
            return serveNameMap;
        }
        //查询所有三级的二级
        Set<Long> secondServeIdFromThird = serves.stream().filter(f -> f.getLevel2Id() > 0 && f.getLevel() == 3).map(ServeBaseInfoResp::getLevel2Id).collect(Collectors.toSet());
        for (ServeBaseInfoResp serve : serves) {
            //过滤所有三级的二级
            if (CollectionUtils.isNotEmpty(secondServeIdFromThird) && secondServeIdFromThird.contains(serve.getServeId())) {
                continue;
            }
            serveNameMap.put(serve.getServeId(), serve.getName());
        }
        if (serveNameMap.size() > 0) {
            redisHelper.set(cashKey, JSON.toJSONString(serveNameMap), CacheKeyConstant.ACTIVITY_PAGE_SERVE_TIME);
        }

        return serveNameMap;
    }

    //设置任务描述，最多领取三次
    public String getTaskDescByTask(ActivityTaskBeanBo activityTask) {

        try {
            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");

            //任务执行类型[single:单次,cycle:持续循环,day:每天,week:每周,month:每月]
            switch (activityTask.getExecuteType()) {
                case "single":
                    return "活动期间最多可领取1次";
                case "cycle":
                    return "活动期间最多可领取" + activityTask.getRewardUserUpperLimit() + "次";
                case "day":
                    return "每天" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                            + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                            + "可领取" + activityTask.getRewardUserDayUpperLimit() + "次";
                case "week":
                    String executeRange = activityTask.getExecuteRange();
                    List<Integer> weeks = JSONObject.parseArray(executeRange, Integer.class);

                    Map<Integer, String> weekMap = new HashMap<>();
                    weekMap.put(1, "周一");
                    weekMap.put(2, "周二");
                    weekMap.put(3, "周三");
                    weekMap.put(4, "周四");
                    weekMap.put(5, "周五");
                    weekMap.put(6, "周六");
                    weekMap.put(7, "周日");

                    List<String> weekstr = new ArrayList<>();
                    for (Integer week : weeks) {
                        weekstr.add(weekMap.get(week));
                    }
                    String join = String.join("、", weekstr);
                    if (activityTask.getRewardUserDayUpperLimit() != 0) {
                        return "每" + join + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每天封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
                    } else {
                        return "每" + join + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每周封顶" + activityTask.getRewardUserWeekUpperLimit() + "次";
                    }
                case "month":
                    List<Integer> months = JSONObject.parseArray(activityTask.getExecuteRange(), Integer.class);
                    String monthStr = "";

                    for (Integer object : months) {
                        if (object != null) {
                            monthStr += object + "、";
                        }
                    }
                    monthStr = StringUtils.trimTrailingCharacter(monthStr, '、');

                    if (activityTask.getRewardUserDayUpperLimit() != 0) {
                        return "每月" + monthStr + "号" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每日封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
                    } else {
                        return "每月" + monthStr + "号" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每月封顶" + activityTask.getRewardUserMonthUpperLimit() + "次";
                    }
                default:
                    return null;
            }

        } catch (Exception e) {
            throw new BusException("data_parse_data_error", e.getMessage());
        }
    }

    /**
     * 优惠券批量查询券值
     * 卷包暂时无批量查询，过滤相同券包，减少查询次数
     *
     * @return
     */
    public Map<Long, VoucherRewardBo> voucherBatchGetRewardValueSuper(List<ActivityTaskVoucherBo> extraIdList, String userClass) {
        if (CollectionUtils.isEmpty(extraIdList)) {
            return null;
        }
        Map<Long, VoucherRewardBo> voucherRewardBoMap = new HashMap<>();

        /**
         * 先查缓存，缓存没有再往后查
         */
        List<String> cacheReqList = extraIdList.stream().map(m -> String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, m)).collect(Collectors.toList());
        List<String> voucherRewardCacheList = httpImageUtils.getBatchRedisKeyValue(cacheReqList);
        if (CollectionUtils.isNotEmpty(voucherRewardCacheList)) {
            for (String str : voucherRewardCacheList) {
                VoucherRewardBo bo = JSON.parseObject(str, VoucherRewardBo.class);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
            }
            Iterator<ActivityTaskVoucherBo> iter = extraIdList.iterator();
            while (iter.hasNext()) {
                ActivityTaskVoucherBo extraIdMap = iter.next();
                if (voucherRewardBoMap.get(extraIdMap.getRewardExtraId()) != null) {
                    iter.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(extraIdList)) {
            return voucherRewardBoMap;
        }
        /**
         * 优惠券
         */
        List<Integer> rewardExtraIdList = extraIdList.stream().
                filter(f -> VOUCHER.type.equals(f.getRewardSymbol()) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        List<VoucherEvent> voucherEventList = null;
        if (CollectionUtils.isNotEmpty(rewardExtraIdList)) {

            GetVoucherEventListReqBean getVoucherEventListReqBean = new GetVoucherEventListReqBean();
            getVoucherEventListReqBean.setEventIdList(rewardExtraIdList);
            voucherEventList = voucherEventApi.getVoucherEventList(getVoucherEventListReqBean);

        }
        if (!CollectionUtils.isEmpty(voucherEventList)) {
            voucherEventList.stream().forEach(voucherEvent -> {
                VoucherRewardBo bo = new VoucherRewardBo();
                //优惠券类型 （money_off:满减-默认 、discount: 折扣）
                bo.setVoucherType(voucherEvent.getMold());
                BigDecimal resultAmount = BigDecimal.ZERO;
                if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherEvent.getMold())) {
                    resultAmount = voucherEvent.getDiscountRatio() == null ? new BigDecimal("0") : voucherEvent.getDiscountRatio();
                    //乘以10
                    resultAmount = resultAmount.multiply(new BigDecimal(10));
                } else {
                    String amountWeight = voucherEvent.getAmountWeight();
                    if (!StringUtils.isEmpty(amountWeight)) {
                        if (amountWeight.contains(":")) {
                            resultAmount = new BigDecimal(amountWeight.split(":")[0]);
                        } else {
                            resultAmount = new BigDecimal(amountWeight);
                        }
                    }
                }
                bo.setRewardExtraId(voucherEvent.getEventId().longValue());
                bo.setVoucherName(voucherEvent.getEventName());
                bo.setExtraStartDate(new Date(voucherEvent.getStartDate() * 1000L));
                bo.setExtraEndDate(new Date(voucherEvent.getEndDate() * 1000L));
                bo.setBeginUse(voucherEvent.getBeginUse());
                bo.setExpireTimeLength(voucherEvent.getExpireTimeLength());
                bo.setExpireDate(voucherEvent.getExpireDate().longValue());
                bo.setMinTradeAmount(this.getMinTradeAmount(voucherEvent.getTradeAmountMinRequire()));
                bo.setRewardValue(resultAmount);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                redisHelper.set(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, bo.getRewardExtraId()), JSON.toJSONString(bo), voucherCacheTime);
            });
        }

        /**
         * 券包：满减券面额之和+折扣券最高抵扣金额之和
         */
        List<Integer> packExtraIdList = extraIdList.stream().
                filter(f -> RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packExtraIdList)) {
            BatchQueryTotalAmountByIdReq queryTotalAmountByIdReq1 = new BatchQueryTotalAmountByIdReq();
            queryTotalAmountByIdReq1.setVoucherPackIdList(packExtraIdList);
            BatchQueryTotalAmountByIdResp queryTotalAmountByIdResp1 = voucherEventApi.batchQueryTotalAmountById(queryTotalAmountByIdReq1);
            Map<Integer, BigDecimal> resultMap = queryTotalAmountByIdResp1.getPackIdForTotalAmount();

            Map<Integer, VoucherPackItemBo> voucherPackItemBoMap = this.getVoucherPackById(packExtraIdList);

            packExtraIdList.stream().forEach(packExtraId -> {
                BigDecimal totalAmount = resultMap.get(packExtraId);
                VoucherPackItemBo voucherPackItemBo = voucherPackItemBoMap.get(packExtraId);
                if (!ObjectUtils.isEmpty(totalAmount)) {
                    VoucherRewardBo bo = new VoucherRewardBo();
                    bo.setRewardExtraId(packExtraId.longValue());
                    bo.setRewardValue(totalAmount);
                    bo.setVoucherBos(voucherPackItemBo.getVoucherRewardBos());
                    voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                    redisHelper.set(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, bo.getRewardExtraId()), JSON.toJSONString(bo), voucherCacheTime);
                }
            });
        }
        return voucherRewardBoMap;
    }

    /**
     * 根据券包id获取券详情
     *
     * @param packExtraIdList
     * @return
     */
    public Map<Integer, VoucherPackItemBo> getVoucherPackById(List<Integer> packExtraIdList) {
        //获取券包详情信息
        GetVoucherPackByIdsReq getVoucherPackByIdsReq = new GetVoucherPackByIdsReq();
        String packIdsStr = com.wanshifu.framework.utils.StringUtils.listToCommaSplit(
                packExtraIdList.stream().map(String::valueOf).collect(Collectors.toList())
        );
        getVoucherPackByIdsReq.setPackIds(packIdsStr);
        List<VoucherPackConfig> voucherPackConfigList = voucherPackApi.getVoucherPackByIds(getVoucherPackByIdsReq);
        //解析优惠券数据，获取全部的券id
        List<VoucherRewardBo> voucherBoList = new ArrayList<>();
        List<VoucherPackItemBo> voucherPackItemBoList = new ArrayList<>();
        voucherPackConfigList.forEach(voucherPackConfig -> {
            List<VoucherRewardBo> voucherBos = JSONArray.parseArray(voucherPackConfig.getVoucher(), VoucherRewardBo.class);
            VoucherPackItemBo voucherPackItemBo = new VoucherPackItemBo();
            List<Integer> rewardExtraIdList = voucherBos.stream().map(VoucherRewardBo::getEventId).collect(Collectors.toList());

            voucherPackItemBo.setVoucherIds(rewardExtraIdList);
            voucherPackItemBo.setPackId(voucherPackConfig.getId());
            voucherPackItemBo.setVoucherRewardBos(voucherBos);

            voucherPackItemBoList.add(voucherPackItemBo);
            voucherBoList.addAll(voucherBos);
        });

        List<Integer> rewardExtraIdList = voucherBoList.stream().map(VoucherRewardBo::getEventId).collect(Collectors.toList());
        GetVoucherEventListReqBean getVoucherEventListReqBean = new GetVoucherEventListReqBean();
        getVoucherEventListReqBean.setEventIdList(rewardExtraIdList);
        List<VoucherEvent> voucherEventList = voucherEventApi.getVoucherEventList(getVoucherEventListReqBean);

        voucherPackItemBoList.forEach(voucherPackItemBo -> {
            List<VoucherRewardBo> voucherBos = voucherPackItemBo.getVoucherRewardBos();
            for (VoucherRewardBo voucherRewardBo : voucherBos) {
                Optional<VoucherEvent> first = voucherEventList.stream().filter(f -> f.getEventId().equals(voucherRewardBo.getEventId())).findFirst();
                if (first.isPresent()) {
                    VoucherEvent voucherEvent = first.get();
                    //优惠券类型 （money_off:满减-默认 、discount: 折扣）
                    voucherRewardBo.setVoucherType(voucherEvent.getMold());
                    BigDecimal resultAmount = BigDecimal.ZERO;
                    if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherEvent.getMold())) {
                        resultAmount = voucherEvent.getDiscountRatio() == null ? new BigDecimal("0") : voucherEvent.getDiscountRatio();
                        //乘以10
                        resultAmount = resultAmount.multiply(new BigDecimal(10));
                    } else {
                        String amountWeight = voucherEvent.getAmountWeight();
                        if (!StringUtils.isEmpty(amountWeight)) {
                            if (amountWeight.contains(":")) {
                                resultAmount = new BigDecimal(amountWeight.split(":")[0]);
                            } else {
                                resultAmount = new BigDecimal(amountWeight);
                            }
                        }
                    }
                    voucherRewardBo.setRewardExtraId(voucherEvent.getEventId().longValue());
                    voucherRewardBo.setVoucherName(voucherEvent.getEventName());
                    voucherRewardBo.setExtraStartDate(new Date(voucherEvent.getStartDate() * 1000L));
                    voucherRewardBo.setExtraEndDate(new Date(voucherEvent.getEndDate() * 1000L));
                    voucherRewardBo.setExpireDate(voucherEvent.getExpireDate().longValue());
                    voucherRewardBo.setMinTradeAmount(this.getMinTradeAmount(voucherEvent.getTradeAmountMinRequire()));
                    voucherRewardBo.setRewardValue(resultAmount);
                }
            }
        });

        Map<Integer, VoucherPackItemBo> voucherPackItemBoMap = voucherPackItemBoList.stream().collect(Collectors.toMap(VoucherPackItemBo::getPackId, voucherPackItemBo -> voucherPackItemBo));

        return voucherPackItemBoMap;
    }

    private BigDecimal getMinTradeAmount(String tradeAmountMinRequire) {
        if (StringUtils.isEmpty(tradeAmountMinRequire)) {
            return BigDecimal.ZERO;
        }
        String[] strings = StringUtils.split(tradeAmountMinRequire, ":");
        return new BigDecimal(strings[1]);
    }

    public String getAppToken() {
        //tokenApp
        String tokenApp = httpServletRequest.getHeader("appToken");
        return tokenApp;
    }

    public List<RewardConfigResp> getRewardConfigList(String userClass, String activityModel) {
        RewardConfigRqt rewardConfigRqt = new RewardConfigRqt();
        rewardConfigRqt.setActivityModel(activityModel);
        rewardConfigRqt.setUserClass(userClass);
        List<RewardConfigResp> rewardConfig = activityBackendServiceApi.getRewardConfig(rewardConfigRqt);
        return rewardConfig;
    }

    /**
     * 批量查询优惠券信息：现金券+折扣券
     *
     * @return
     */
    public Map<Long, VoucherRewardBo> getOnlyVoucherBatchGetReward(List<Integer> extraIdList) {
        Map<Long, VoucherRewardBo> voucherRewardBoMap = new HashMap<>();
        if (CollectionUtils.isEmpty(extraIdList)) {
            return voucherRewardBoMap;
        }
        /**
         * 先查缓存，缓存没有再往后查
         */
        List<String> cacheReqList = extraIdList.stream().map(m -> String.format(CacheKeyConstant.VOUCHER_REWARD_KEY_V2, m)).collect(Collectors.toList());
        List<String> voucherRewardCacheList = httpImageUtils.getBatchRedisKeyValue(cacheReqList);
        if (CollectionUtils.isNotEmpty(voucherRewardCacheList)) {
            for (String str : voucherRewardCacheList) {
                VoucherRewardBo bo = JSON.parseObject(str, VoucherRewardBo.class);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
            }
            extraIdList = extraIdList.stream().filter(f -> ObjectUtils.isEmpty(voucherRewardBoMap.get(f.longValue()))).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(extraIdList)) {
            return voucherRewardBoMap;
        }
        GetVoucherEventListReqBean getVoucherEventListReqBean = new GetVoucherEventListReqBean();
        getVoucherEventListReqBean.setEventIdList(extraIdList);
        List<VoucherEvent> voucherEventList = voucherEventApi.getVoucherEventList(getVoucherEventListReqBean);
        if (CollectionUtils.isEmpty(voucherEventList)) {
            return voucherRewardBoMap;
        }
        List<BatchRedisSetBean> batchRedisSetBeans = new ArrayList<>();
        voucherEventList.stream().forEach(voucherEvent -> {
            VoucherRewardBo bo = new VoucherRewardBo();
            BigDecimal resultAmount = BigDecimal.ZERO;
            if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherEvent.getMold())) {
                resultAmount = voucherEvent.getDiscountRatio() == null ? new BigDecimal("0") : voucherEvent.getDiscountRatio();
                String amountWeight = voucherEvent.getAmountWeight();
                bo.setDiscountMaxValue(new BigDecimal(amountWeight.split(":")[0]));
            } else {
                String amountWeight = voucherEvent.getAmountWeight();
                if (!StringUtils.isEmpty(amountWeight)) {
                    if (amountWeight.contains(":")) {
                        resultAmount = new BigDecimal(amountWeight.split(":")[0]);
                    } else {
                        resultAmount = new BigDecimal(amountWeight);
                    }
                }
            }
            //优惠券类型 （money_off:满减-默认 、discount: 折扣）
            bo.setVoucherType(voucherEvent.getMold());
            bo.setRewardExtraId(voucherEvent.getEventId().longValue());
            bo.setRewardValue(resultAmount);
            voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
            BatchRedisSetBean bean = new BatchRedisSetBean();
            bean.setKey(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY_V2, bo.getRewardExtraId()));
            bean.setValue(JSON.toJSONString(bo));
            bean.setCacheSeconds(voucherCacheTime);
            batchRedisSetBeans.add(bean);
        });
        //批量set进redis
        redisHelper.batchSet(batchRedisSetBeans);
        return voucherRewardBoMap;
    }

    /**
     * @param isOrderPackage
     * @return
     */
    public OrderFilterConditionBo createOrderFilterConditionBoByValue(boolean isOrderPackage) {
        OrderFilterConditionBo orderFilterConditionBo = new OrderFilterConditionBo();
        orderFilterConditionBo.setOrderPackage(isOrderPackage);

        return orderFilterConditionBo;
    }

    public List<BatchGetPushDistanceResp> getPushDistance(Long userId, List<Long> orderIds) {
        BatchGetPushDistanceRqt batchGetPushDistanceRqt = new BatchGetPushDistanceRqt();
        batchGetPushDistanceRqt.setMasterId(userId);
        batchGetPushDistanceRqt.setOrderIds(orderIds);
        List<BatchGetPushDistanceResp> batchGetPushDistanceResps = masterInterface.batchGetPushDistance(batchGetPushDistanceRqt);
        return batchGetPushDistanceResps;
    }

    /**
     * 批量获取订单是否技能相关
     * @param userId
     * @param orderIds
     * @return
     */
    public Map<String, Integer> getSkillRelatedState(Long userId, List<Long> orderIds){
        if(Objects.isNull(userId) || CollectionUtils.isEmpty(orderIds)){
            return new HashMap<>();
        }
        GetPushtechnologFlagRqt rqt = new GetPushtechnologFlagRqt();
        List<String> orderIdStringList = orderIds.stream().map(o -> String.valueOf(o)).collect(Collectors.toList());
        rqt.setMasterId(String.valueOf(userId));
        rqt.setOrderIds(orderIdStringList);
        Map<String, Integer> result = bigDataOrderApi.getPushtechnologFlag(rqt);
        log.info("getSkillRelatedState resp:{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 券包：满减券面额之和+折扣券最高抵扣金额之和
     *
     * @param voucherPackIdList
     * @return
     */
    public Map<Long, VoucherRewardBo> batchQueryVoucherPackValue(List<Integer> voucherPackIdList) {
        Map<Long, VoucherRewardBo> voucherRewardBoMap = new HashMap<>();
        if (CollectionUtils.isEmpty(voucherPackIdList)) {
            return voucherRewardBoMap;
        }
        /**
         * 先查缓存
         */
        List<String> cacheReqList = voucherPackIdList.stream().map(m -> String.format(CacheKeyConstant.VOUCHER_PACK_KEY, m)).collect(Collectors.toList());
        List<String> voucherRewardCacheList = httpImageUtils.getBatchRedisKeyValue(cacheReqList);
        if(CollectionUtils.isNotEmpty(voucherRewardCacheList)){
            for (String str : voucherRewardCacheList) {
                VoucherRewardBo bo = JSON.parseObject(str, VoucherRewardBo.class);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                voucherPackIdList.remove(bo.getRewardExtraId());
            }
        }
        if(CollectionUtils.isEmpty(voucherPackIdList)){
            return voucherRewardBoMap;
        }
        /**
         * 缓存中没有的继续往后查
         */
        BatchQueryTotalAmountByIdReq queryTotalAmountByIdReq1 = new BatchQueryTotalAmountByIdReq();
        queryTotalAmountByIdReq1.setVoucherPackIdList(voucherPackIdList);
        BatchQueryTotalAmountByIdResp queryTotalAmountByIdResp1 = voucherEventApi.batchQueryTotalAmountById(queryTotalAmountByIdReq1);

        if (queryTotalAmountByIdResp1 != null && queryTotalAmountByIdResp1.getPackIdForTotalAmount() != null) {
            Map<Integer, BigDecimal> resultMap = queryTotalAmountByIdResp1.getPackIdForTotalAmount();
            List<BatchRedisSetBean> batchRedisSetBeans = new ArrayList<>();
            voucherPackIdList.stream().forEach(packExtraId -> {
                if (!ObjectUtils.isEmpty(resultMap.get(packExtraId))) {
                    VoucherRewardBo bo = new VoucherRewardBo();
                    bo.setRewardExtraId(packExtraId.longValue());
                    bo.setRewardValue(resultMap.get(packExtraId));
                    voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                    BatchRedisSetBean bean = new BatchRedisSetBean();
                    bean.setKey(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, bo.getRewardExtraId()));
                    bean.setValue(JSON.toJSONString(bo));
                    bean.setCacheSeconds(voucherCacheTime);
                }
            });
        //批量set进redis
        redisHelper.batchSet(batchRedisSetBeans);
        }
        return voucherRewardBoMap;
    }

    public List<ListRemainNumResp> getListRemainNumResp(List<Long> pIds) {
        if(CollectionUtils.isEmpty(pIds)){
            return null;
        }
        ListRemainNumReq listRemainNumReq = new ListRemainNumReq();
        listRemainNumReq.setPrizeWheelIds(pIds);
        List<ListRemainNumResp> resultRespList = prizeWheelBusinessServiceApi.listRemainNumByPrizeWheelIds(listRemainNumReq);
        return resultRespList;
    }

    public BigDecimal getRewardValueOfRuleConfig(BigDecimal rewardValue, BigDecimal value,
            String rewardSymbol, Long ruleConfigId, BigDecimal ruleConfigResultValue) {
        //是否设置了奖励公式
        if(rewardSymbol.equals(RewardSymbol.VOUCHER_PACK.type)){
            if(ruleConfigId!=null && null != ruleConfigResultValue){
                rewardValue = rewardValue.multiply(ruleConfigResultValue);
            }else{
                rewardValue = rewardValue.multiply(value);
            }
        }
        return rewardValue;
    }
    /**
     * 返回落地页链接
     */
    public String returnLandingPageUrl(String activityModel, Long activityId) {
        if (activityModel.equals(ActivityTypeEnum.ORDER_TASK.getActivityType())) {
            return orderActivityUrl + activityId;
        } else {
            return awardTaskUrl + activityId;
        }
    }
    /**
     * 拆解有奖任务奖励标签
     *
     * @param tagText
     */
    public TagTextBo splitTagText(String tagText) {
        if (com.wanshifu.framework.utils.StringUtils.isBlank(tagText)) {
            return null;
        }
        TagTextBo tagTextBo = JSON.parseObject(tagText, TagTextBo.class);
        return tagTextBo;
    }

    public String getTaskSymbolName(String taskSymbol) {
        TaskSymbolEnum taskSymbolEnum = TaskSymbolEnum.getTaskSymBolEnumBySymbol(taskSymbol);
        String taskName = "";
        switch (taskSymbolEnum) {
            case CREATE_ORDER:
                taskName = "下单";
                break;
            case APPOINT_MASTER:
                taskName = "指派";

                break;
            case ORDER_PAY:
                taskName = "付款";
                break;
            case ORDER_CHECK:
                taskName = "验收";
                break;
            default:
                break;
        }
        return taskName;
    }

    /**
     * 构建领奖信息
     * @param rewardInfoRespList
     * @param userClass
     * @return
     */
    public List<RewardInfoVo> buildRewardInfo(List<RewardInfoResp> rewardInfoRespList, String userClass){
        if(CollectionUtils.isEmpty(rewardInfoRespList)){
            return new ArrayList<>();
        }

        List<RewardInfoVo> rewardInfoList = new ArrayList<>();
        //优惠券及券包信息查询
        Map<Integer, VoucherEvent> voucherMap = new HashMap<>();
        Map<Integer, BigDecimal> voucherPackAmountMap = new HashMap<>();
        List<Integer> voucherIdList = new ArrayList<>();
        List<Integer> voucherPackIdList = new ArrayList<>();
        for (RewardInfoResp rewardInfoResp : rewardInfoRespList) {
            List<OptionalRewardInfoBo> optionalRewardInfoList = rewardInfoResp.getOptionalRewardInfoList();
            if(CollectionUtils.isEmpty(optionalRewardInfoList)){
                continue;
            }
            for(OptionalRewardInfoBo infoBo : optionalRewardInfoList){
                if(RewardSymbol.VOUCHER.type.equals(infoBo.getRewardSymbol())){
                    voucherIdList.add(infoBo.getRewardExtraId().intValue());
                } else if (RewardSymbol.VOUCHER_PACK.type.equals(infoBo.getRewardSymbol())){
                    voucherPackIdList.add(infoBo.getRewardExtraId().intValue());
                }

            }
        }
        //优化券信息
        if(CollectionUtils.isNotEmpty(voucherIdList)){
            GetVoucherEventListReqBean reqBean = new GetVoucherEventListReqBean();
            reqBean.setEventIdList(voucherIdList);
            List<VoucherEvent> voucherEventList = voucherEventApi.getVoucherEventList(reqBean);
            voucherMap = voucherEventList.stream().collect(Collectors.toMap(v -> v.getEventId(), v -> v));
        }
        //券包信息
        if(CollectionUtils.isNotEmpty(voucherPackIdList)){
            BatchQueryTotalAmountByIdReq queryReq = new BatchQueryTotalAmountByIdReq();
            queryReq.setVoucherPackIdList(voucherPackIdList);
            BatchQueryTotalAmountByIdResp voucherPackAmountList = voucherEventApi.batchQueryTotalAmountById(queryReq);
            voucherPackAmountMap = voucherPackAmountList.getPackIdForTotalAmount();
        }



        for (RewardInfoResp rewardInfoResp : rewardInfoRespList) {
            RewardInfoVo rewardInfoVo = new RewardInfoVo();
            BeanUtils.copyProperties(rewardInfoResp, rewardInfoVo);

            List<OptionalRewardInfoBo> optionalRewardInfoList = rewardInfoResp.getOptionalRewardInfoList();
            if(CollectionUtils.isEmpty(optionalRewardInfoList)){
                rewardInfoList.add(rewardInfoVo);
                continue;
            }

            List<OptionalRewardInfoVo> optionalRewardInfoVoList = new ArrayList<>();
            //如果多选一奖励里有优惠券/券包,需要设置相应信息
            for(OptionalRewardInfoBo infoBo : optionalRewardInfoList){
                OptionalRewardInfoVo vo = new OptionalRewardInfoVo();
                BeanUtils.copyProperties(infoBo, vo);
                if((!RewardSymbol.VOUCHER.type.equals(vo.getRewardSymbol()) && !RewardSymbol.VOUCHER_PACK.type.equals(vo.getRewardSymbol())) ||
                        (Objects.isNull(vo.getRewardExtraId())) || vo.getRewardExtraId() == 0){
                    optionalRewardInfoVoList.add(vo);
                    continue;
                }

                if(RewardSymbol.VOUCHER_PACK.type.equals(vo.getRewardSymbol())){
                    BigDecimal value = voucherPackAmountMap.get(vo.getRewardExtraId().intValue());
                    if(Objects.nonNull(value)){
                        vo.setRewardValue(value);
                    }
                    optionalRewardInfoVoList.add(vo);
                    continue;
                }
                if(RewardSymbol.VOUCHER.type.equals(vo.getRewardSymbol())){
                    VoucherEvent voucherEvent = voucherMap.get(vo.getRewardExtraId().intValue());
                    if(Objects.isNull(voucherEvent)){
                        optionalRewardInfoVoList.add(vo);
                        continue;
                    }
                    BigDecimal resultAmount = new BigDecimal("0");
                    //优惠券类型 （money_off:满减-默认 、discount: 折扣）
                    if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherEvent.getMold())) {
                        vo.setRewardUnit("折");
                        resultAmount = voucherEvent.getDiscountRatio() == null ? new BigDecimal("0") : voucherEvent.getDiscountRatio();
                        //乘以10
                        resultAmount = resultAmount.multiply(new BigDecimal(10));
                    } else {
                        String amountWeight = voucherEvent.getAmountWeight();
                        if (!StringUtils.isEmpty(amountWeight)) {
                            if (amountWeight.contains(":")) {
                                resultAmount = new BigDecimal(amountWeight.split(":")[0]);
                            } else {
                                resultAmount = new BigDecimal(amountWeight);
                            }
                        }
                    }
                    vo.setRewardValue(resultAmount);
                    optionalRewardInfoVoList.add(vo);
                }
            }
            rewardInfoVo.setOptionalRewardInfoList(optionalRewardInfoVoList);
            rewardInfoList.add(rewardInfoVo);
        }
        return rewardInfoList;
    }

    /**
     * 获取优惠券值
     * @param voucherRewardBo
     * @return
     */
    public BigDecimal getRewardValue(VoucherRewardBo voucherRewardBo){
        BigDecimal rewardValue = voucherRewardBo.getRewardValue();
        if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherRewardBo.getVoucherType())) {
            //折扣需要先乘10
            String rewardValueStr = MathUtils.decimalCutZero(rewardValue.multiply(new BigDecimal(10)));
            BigDecimal remainder = new BigDecimal(rewardValueStr).remainder(new BigDecimal(10));
            if (remainder.compareTo(BigDecimal.ZERO) == 0) {
                rewardValue = new BigDecimal(rewardValue.intValue());
            } else {
                rewardValue = new BigDecimal(rewardValueStr);
            }
        }
        return rewardValue;
    }

    /**
     * 判断是否是师傅店铺订单
     * @param masterOrderLabelList
     * @return
     */
    public boolean isMasterOrderShop(List<String> masterOrderLabelList){
        if (CollectionUtils.isNotEmpty(masterOrderLabelList) && masterOrderLabelList.contains(MasterOrderLabelEnum.MASTER_SHOP.label)) {
            return true;
        }
        return false;
    }

    /**
     * 获取广告跳转地址bo
     * @param adForwardUrlBo
     * @return
     */
    public ForwardUrlBo getAdForwardUrlInfo(AdForwardUrlBo adForwardUrlBo){
        if(Objects.isNull(adForwardUrlBo)){
            return null;
        }
        //链接信息
        ForwardUrlBo forwardUrlBo = new ForwardUrlBo();
        BeanUtils.copyProperties(adForwardUrlBo, forwardUrlBo);
        if(Objects.isNull(forwardUrlBo.getUrlType())){
            forwardUrlBo.setUrlType(0);
        }
        //小程序信息
        AdForwardUrlBo.AppletInfo appletInfo = adForwardUrlBo.getAppletInfo();
        if(Objects.nonNull(appletInfo)){
            ForwardUrlBo.AppletInfo targetAppletInfo = new ForwardUrlBo.AppletInfo();
            BeanUtils.copyProperties(appletInfo, targetAppletInfo);
            forwardUrlBo.setAppletInfo(targetAppletInfo);
        }

        return forwardUrlBo;
    }
}
