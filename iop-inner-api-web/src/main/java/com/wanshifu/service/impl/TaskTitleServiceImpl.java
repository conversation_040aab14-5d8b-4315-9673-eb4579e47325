package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.iop.activity.domain.bo.ActivityTaskBeanBo;
import com.wanshifu.iop.activity.domain.po.UserActivityTask;
import com.wanshifu.service.TaskTitleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Implementation of TaskTitleService
 * Handles task title and description generation logic
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class TaskTitleServiceImpl implements TaskTitleService {

    private static final String TASK_VISIT = "visit";
    private static final String TASK_CREATE_ORDER = "create_order";
    private static final String TASK_APPOINT_MASTER = "appoint_master";
    private static final String TASK_ORDER_PAY = "order_pay";
    private static final String TASK_ORDER_CHECK = "order_check";
    private static final String TASK_MANUAL_RATE = "manual_rate";

    @Override
    public String generateTaskTitle(String taskSymbol, UserActivityTask userActivityTask) {
        if (userActivityTask == null) {
            return "";
        }

        switch (taskSymbol) {
            case TASK_VISIT:
                return generateVisitTaskTitle(userActivityTask);
            case TASK_CREATE_ORDER:
                return generateCreateOrderTaskTitle(userActivityTask);
            case TASK_APPOINT_MASTER:
                return generateAppointMasterTaskTitle(userActivityTask);
            case TASK_ORDER_PAY:
                return generateOrderPayTaskTitle(userActivityTask);
            case TASK_ORDER_CHECK:
                return generateOrderCheckTaskTitle(userActivityTask);
            case TASK_MANUAL_RATE:
                return generateManualRateTaskTitle(userActivityTask);
            default:
                return "";
        }
    }

    @Override
    public String generateTaskTitleForAnonymous(String taskSymbol) {
        switch (taskSymbol) {
            case TASK_VISIT:
                return "完成指定次数登录";
            case TASK_CREATE_ORDER:
                return "完成指定次数下单";
            case TASK_APPOINT_MASTER:
                return "完成指定次数指派";
            case TASK_ORDER_PAY:
                return "完成指定托管费用";
            case TASK_ORDER_CHECK:
                return "完成指定次数/金额订单验收";
            case TASK_MANUAL_RATE:
                return "完成指定次数评价";
            default:
                return "";
        }
    }

    @Override
    public String generateTaskDescription(ActivityTaskBeanBo activityTask) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");

            switch (activityTask.getExecuteType()) {
                case "single":
                    return "活动期间最多可领取1次";
                case "cycle":
                    return "活动期间最多可领取" + activityTask.getRewardUserUpperLimit() + "次";
                case "day":
                    return generateDayTaskDescription(activityTask, formatter);
                case "week":
                    return generateWeekTaskDescription(activityTask, formatter);
                case "month":
                    return generateMonthTaskDescription(activityTask, formatter);
                default:
                    return "";
            }
        } catch (Exception e) {
            throw new BusException("data_parse_data_error", e.getMessage());
        }
    }

    @Override
    public Long calculateRemainingSeconds(Date activityEndTime, Long timeLimit, Date applyTime) {
        long activityEndTimeStamp = activityEndTime.getTime() / 1000;
        long applyTimeStamp = applyTime.getTime() / 1000;
        long nowTime = System.currentTimeMillis() / 1000;

        if (timeLimit > 0 && activityEndTimeStamp - nowTime > timeLimit) {
            return timeLimit + applyTimeStamp - nowTime;
        } else {
            return Math.max(0L, activityEndTimeStamp - nowTime);
        }
    }

    private String generateVisitTaskTitle(UserActivityTask userActivityTask) {
        return "完成" + userActivityTask.getTargetQuantityValue() + "次登录";
    }

    private String generateCreateOrderTaskTitle(UserActivityTask userActivityTask) {
        String prefix = userActivityTask.getTimeLimit() > 0 ? "内完成" : "完成";
        return prefix + userActivityTask.getTargetQuantityValue() + "次下单";
    }

    private String generateAppointMasterTaskTitle(UserActivityTask userActivityTask) {
        String prefix = userActivityTask.getTimeLimit() > 0 ? "内完成" : "完成";
        return prefix + userActivityTask.getTargetQuantityValue() + "次指派";
    }

    private String generateOrderPayTaskTitle(UserActivityTask userActivityTask) {
        int targetQuantity = userActivityTask.getTargetQuantityValue();
        int targetAmount = userActivityTask.getTargetAmountValue().intValue();
        int timeLimit = userActivityTask.getTimeLimit();

        if (targetQuantity > 1 && targetAmount > 0 && timeLimit > 0) {
            return "内完成" + targetQuantity + "次费用托管，且金额满" + targetAmount + "元";
        } else if (targetQuantity == 1 && targetAmount > 0 && timeLimit > 0) {
            return "内累计托管费用满" + targetAmount + "元";
        } else if (targetQuantity > 1 && targetAmount == 0 && timeLimit > 0) {
            return "内完成" + targetQuantity + "次费用托管";
        } else if (targetQuantity == 1 && targetAmount > 0 && timeLimit == 0) {
            return "累计托管费用满" + targetAmount + "元";
        } else if (targetQuantity > 1 && targetAmount > 0 && timeLimit == 0) {
            return "完成" + targetQuantity + "次费用托管，且金额满" + targetAmount + "元";
        } else if (targetQuantity > 1 && targetAmount == 0 && timeLimit == 0) {
            return "完成" + targetQuantity + "次费用托管";
        }
        return "";
    }

    private String generateOrderCheckTaskTitle(UserActivityTask userActivityTask) {
        int targetQuantity = userActivityTask.getTargetQuantityValue();
        int targetAmount = userActivityTask.getTargetAmountValue().intValue();
        int timeLimit = userActivityTask.getTimeLimit();

        if (targetQuantity > 1 && targetAmount > 0 && timeLimit > 0) {
            return "内完成" + targetQuantity + "次订单验收，且金额满" + targetAmount + "元";
        } else if (targetQuantity == 1 && targetAmount > 0 && timeLimit > 0) {
            return "内累计验收订单金额满" + targetAmount + "元";
        } else if (targetQuantity > 1 && targetAmount == 0 && timeLimit > 0) {
            return "内完成" + targetQuantity + "次验收";
        } else if (targetQuantity == 1 && targetAmount > 0 && timeLimit == 0) {
            return "累计验收订单金额满" + targetAmount + "元";
        } else if (targetQuantity > 1 && targetAmount > 0 && timeLimit == 0) {
            return "完成" + targetQuantity + "次验收，且金额满" + targetAmount + "元";
        } else if (targetQuantity > 1 && targetAmount == 0 && timeLimit == 0) {
            return "完成" + targetQuantity + "次订单验收";
        }
        return "";
    }

    private String generateManualRateTaskTitle(UserActivityTask userActivityTask) {
        String prefix = userActivityTask.getTimeLimit() > 0 ? "内完成" : "完成";
        return prefix + userActivityTask.getTargetQuantityValue() + "次评价";
    }

    private String generateDayTaskDescription(ActivityTaskBeanBo activityTask, SimpleDateFormat formatter) throws Exception {
        String startTime = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()));
        String endTime = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()));
        return "每天" + startTime + "到" + endTime + "可领取" + activityTask.getRewardUserDayUpperLimit() + "次";
    }

    private String generateWeekTaskDescription(ActivityTaskBeanBo activityTask, SimpleDateFormat formatter) throws Exception {
        String executeRange = activityTask.getExecuteRange();
        List<Integer> weeks = JSONObject.parseArray(executeRange, Integer.class);

        Map<Integer, String> weekMap = createWeekMap();
        List<String> weekStrings = new ArrayList<>();
        for (Integer week : weeks) {
            weekStrings.add(weekMap.get(week));
        }
        String weekJoin = String.join("、", weekStrings);

        String startTime = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()));
        String endTime = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()));

        if (activityTask.getRewardUserDayUpperLimit() != 0) {
            return "每" + weekJoin + startTime + "到" + endTime + "完成任务可领，每天封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
        } else {
            return "每" + weekJoin + startTime + "到" + endTime + "完成任务可领，每周封顶" + activityTask.getRewardUserWeekUpperLimit() + "次";
        }
    }

    private String generateMonthTaskDescription(ActivityTaskBeanBo activityTask, SimpleDateFormat formatter) throws Exception {
        List<Integer> months = JSONObject.parseArray(activityTask.getExecuteRange(), Integer.class);
        StringBuilder monthStr = new StringBuilder();

        for (Integer month : months) {
            if (month != null) {
                monthStr.append(month).append("、");
            }
        }
        String monthString = StringUtils.trimTrailingCharacter(monthStr.toString(), '、');

        String startTime = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()));
        String endTime = formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()));

        if (activityTask.getRewardUserDayUpperLimit() != 0) {
            return "每月" + monthString + "号" + startTime + "到" + endTime + "完成任务可领，每日封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
        } else {
            return "每月" + monthString + "号" + startTime + "到" + endTime + "完成任务可领，每月封顶" + activityTask.getRewardUserMonthUpperLimit() + "次";
        }
    }

    private Map<Integer, String> createWeekMap() {
        Map<Integer, String> weekMap = new HashMap<>();
        weekMap.put(1, "周一");
        weekMap.put(2, "周二");
        weekMap.put(3, "周三");
        weekMap.put(4, "周四");
        weekMap.put(5, "周五");
        weekMap.put(6, "周六");
        weekMap.put(7, "周日");
        return weekMap;
    }
}
