package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.wanshifu.annotation.ExceptionHandle;
import com.wanshifu.annotation.UserLoginInfo;
import com.wanshifu.base.address.api.AddressApi;
import com.wanshifu.base.address.api.GdGeoCodeApi;
import com.wanshifu.base.address.domain.gaode.vo.GeoCodeVo;
import com.wanshifu.base.address.domain.gd.form.GdGeoCodeQueryForm;
import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.bo.*;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.constant.AdConstant;
import com.wanshifu.constant.CacheKeyConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.redis.autoconfigure.bean.BatchRedisSetBean;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.DateUtils;
import com.wanshifu.iop.activity.domain.api.request.*;
import com.wanshifu.iop.activity.domain.api.request.bigdata.IsUsedByTryIdServiceRqt;
import com.wanshifu.iop.activity.domain.api.request.business.*;
import com.wanshifu.iop.activity.domain.api.request.combined.BatchSelectRqt;
import com.wanshifu.iop.activity.domain.api.request.customer.CheckUserQualificationApiRqt;
import com.wanshifu.iop.activity.domain.api.request.landingPage.GetMasterActivityDetailAdRqt;
import com.wanshifu.iop.activity.domain.api.request.master.MasterRewardListRqt;
import com.wanshifu.iop.activity.domain.api.response.*;
import com.wanshifu.iop.activity.domain.api.response.GetActivityDetailResp.ActivityTaskBo;
import com.wanshifu.iop.activity.domain.api.response.bigdata.IsUsedByTryIdServiceResp;
import com.wanshifu.iop.activity.domain.api.response.landingPage.GetMasterActivityDetailAdResp;
import com.wanshifu.iop.activity.domain.api.response.master.ActivityLandPageBatchResp;
import com.wanshifu.iop.activity.domain.api.response.master.MasterRewardListResp;
import com.wanshifu.iop.activity.domain.api.response.merchant.GetPayPageVoucherListResp;
import com.wanshifu.iop.activity.domain.api.response.merchant.MatchOrderActivityDetailResp;
import com.wanshifu.iop.activity.domain.api.response.merchant.MatchUserOrderListTagResp;
import com.wanshifu.iop.activity.domain.api.response.merchant.MerchantPublishOrderTagResp;
import com.wanshifu.iop.activity.domain.bo.ActivityTaskBeanBo;
import com.wanshifu.iop.activity.domain.bo.ChooseRewardInfoBo;
import com.wanshifu.iop.activity.domain.bo.MasterOrderInfoBo;
import com.wanshifu.iop.activity.domain.bo.RewardGiveResp;
import com.wanshifu.iop.activity.domain.enums.*;
import com.wanshifu.iop.activity.domain.po.ActivityBase;
import com.wanshifu.iop.activity.domain.po.CombineRewardConfig;
import com.wanshifu.iop.activity.domain.po.UserActivityTask;
import com.wanshifu.iop.ad.domain.bo.AdMaterialExtBo;
import com.wanshifu.iop.ad.domain.bo.FunctionRelationInfoBo;
import com.wanshifu.iop.ad.domain.enums.AdAttributeTypeEnum;
import com.wanshifu.iop.ad.domain.enums.ListAdSymbolEnum;
import com.wanshifu.iop.ad.domain.enums.PopStyleEnum;
import com.wanshifu.iop.ad.domain.enums.UserClassEnum;
import com.wanshifu.iop.ad.domain.req.activity.ActivityLandingclickReq;
import com.wanshifu.iop.ad.domain.req.activity.TaskReceiveToAdHelpReq;
import com.wanshifu.iop.ad.domain.req.common.AddAdStateInfoReq;
import com.wanshifu.iop.ad.domain.req.common.SavePullNonPopRecordReq;
import com.wanshifu.iop.ad.domain.req.common.SavePullPopRecordReq;
import com.wanshifu.iop.ad.domain.req.pullAd.*;
import com.wanshifu.iop.ad.domain.resp.pullAd.*;
import com.wanshifu.iop.equity.domain.api.request.vipEquity.GetVipEquityRqt;
import com.wanshifu.iop.equity.domain.api.response.GetVipEquityResp;
import com.wanshifu.iop.equity.domain.bo.EquityRewardBo;
import com.wanshifu.iop.equity.domain.enums.EquityRewardExtraTypeEnum;
import com.wanshifu.iop.equity.domain.enums.EquityRewardTypeEnum;
import com.wanshifu.iop.equity.domain.enums.VipTypeEnum;
import com.wanshifu.iop.inner.api.domains.bo.*;
import com.wanshifu.iop.inner.api.domains.bo.mq.user.MerchantVisitDelayBo;
import com.wanshifu.iop.inner.api.domains.enums.*;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.request.MatchUserOrderListTagRqt.MatchUserOrderListTag;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.merchant.*;
import com.wanshifu.iop.inner.api.domains.vo.resp.MasterInfoDtoResp;
import com.wanshifu.iop.inner.api.domains.vo.resp.UserInfoWebDtoResp;
import com.wanshifu.iop.marketing.config.domain.api.request.GetUrlByActivityIdRqt;
import com.wanshifu.iop.marketing.config.domain.api.response.GetUrlByActivityIdResp;
import com.wanshifu.iop.play.method.domain.api.request.DeductInventoryApiRqt;
import com.wanshifu.iop.play.method.domain.api.request.DeductInventoryGetOneRqt;
import com.wanshifu.iop.play.method.domain.api.response.DeductInventoryApiResp;
import com.wanshifu.iop.play.method.domain.api.response.DeductInventoryGetOneResp;
import com.wanshifu.manager.FilterConditionManager;
import com.wanshifu.master.information.api.CommonQueryServiceApi;
import com.wanshifu.master.information.domain.api.request.common.GetMasterInfoRqt;
import com.wanshifu.master.information.domain.api.response.common.GetMasterInfoResp;
import com.wanshifu.order.config.domains.dto.serve.GoodsIdAndServeTypeIdReq;
import com.wanshifu.order.config.domains.dto.serve.ServeBaseInfoResp;
import com.wanshifu.order.config.domains.dto.serve.ServeIdSetReq;
import com.wanshifu.order.offer.api.infoorder.InfoOrderListApi;
import com.wanshifu.order.offer.domains.api.request.infoorder.RecommendInfoOrderListRqt;
import com.wanshifu.order.offer.domains.api.response.infoorder.InfoOrderBaseComposite;
import com.wanshifu.order.offer.domains.po.InfoOrderBase;
import com.wanshifu.order.offer.domains.po.InfoOrderExtraData;
import com.wanshifu.order.offer.domains.po.InfoOrderGoods;
import com.wanshifu.order.offer.domains.po.InfoOrderGoodsAttachment;
import com.wanshifu.order.offer.domains.vo.infoorder.InfoOrderGoodsComposite;
import com.wanshifu.sdk.BonusServiceApi;
import com.wanshifu.service.ActivityService;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import com.wanshifu.toc.user.voucher.domain.po.TocCouponPackComponent;
import com.wanshifu.toc.user.voucher.domain.po.TocCouponSubject;
import com.wanshifu.toc.user.voucher.domain.po.TocCouponSubjectExt;
import com.wanshifu.user.vip.domain.api.request.toc.GetVipAndCouponStatusForIocRequest;
import com.wanshifu.user.vip.domain.api.response.toc.GetVipAndCouponStatusForIocResp;
import com.wanshifu.user.voucher.domain.api.request.BatchQueryTotalAmountByIdReq;
import com.wanshifu.user.voucher.domain.api.request.GetVoucherEventListReqBean;
import com.wanshifu.user.voucher.domain.api.request.GetVoucherPackByIdsReq;
import com.wanshifu.user.voucher.domain.api.request.QueryTotalAmountByIdReq;
import com.wanshifu.user.voucher.domain.api.response.BatchQueryTotalAmountByIdResp;
import com.wanshifu.user.voucher.domain.api.response.QueryTotalAmountByIdResp;
import com.wanshifu.user.voucher.domain.po.VoucherEvent;
import com.wanshifu.user.voucher.domain.po.VoucherPackConfig;
import com.wanshifu.util.BeanCopyUtil;
import com.wanshifu.util.BigDataAliYunUtils;
import com.wanshifu.util.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service("activityService")
@Slf4j
public class ActivityServiceImpl extends AbstractService implements ActivityService {

    //是否允许app访问
    @Value("${wanshifu.app.enable}")
    private Boolean appEnable;

    //拉取广告缓存时间：单位秒
    @Value("${wanshifu.pullAdPosition.redisCacheTime:10}")
    private Integer redisCacheTime;

    //优惠券/券包缓存时间
    @Value("${wanshifu.voucherReward.redisCacheTime:10}")
    private Integer voucherCacheTime;

    /**
     * 落地页地址
     */
    @Value("${wanshifu.marketing.landingPage.url}")
    public String landingPageUrl;
    /**
     * 尾货广告-最大拉取订单数量
     */
    @Value("${wanshifu.weiHuoAd.orderMaxPullSize:5}")
    private Integer orderMaxPullSize;
    /**
     * 尾货订单-师傅详情页地址
     */
    @Value("${wanshifu.weiHuoOrder.masterOrderDetailUrl}")
    private String masterOrderDetailUrl;
    @Resource
    private BigDataAliYunUtils bigDataAliYunUtils;

    /**
     * 师傅红包服务
     */
    @Resource
    private BonusServiceApi bonusServiceApi;
    @Resource
    private FilterConditionManager filterConditionManager;

//    @Resource
//    private UserOrderResourceControllerApi userOrderResourceControllerApi;

    @Resource
    private HttpServletRequest httpServletRequest;
    @Resource
    private InfoOrderListApi infoOrderListApi;
    @Resource
    private CommonQueryServiceApi commonQueryServiceApi;
    @Resource
    private GdGeoCodeApi gdGeoCodeApi;
    @Resource
    private AddressApi addressApi;

    /**
     * 获取活动落地页详情
     */
    @Override
    public GetActivityDetailApiResp getActivityDetail(GetActivityDetailApiRqt getActivityDetailApiRqt) {

        String tokenApp = getActivityDetailApiRqt.getTokenApp();

        //是否是app
        boolean isApp = !StringUtils.isEmpty(tokenApp);
        if (!appEnable && isApp) {
            return null;
        }

        //是否登陆
        boolean isLogin = Objects.nonNull(getActivityDetailApiRqt.getUserId());
        //实例化活动详情
        GetActivityDetailApiResp getActivityDetailApiResp = new GetActivityDetailApiResp();
        GetActivityDetailResp activityDetail;

        if (isLogin) {
            //判断用户登陆状态、

            GetActivityDetailRqt getActivityDetailRqt = new GetActivityDetailRqt();
            getActivityDetailRqt.setUserId(getActivityDetailApiRqt.getUserId());
            getActivityDetailRqt.setActivityId(getActivityDetailApiRqt.getActivityId());

            try {
                activityDetail = activityBusinessServiceApi.getActivityDetail(getActivityDetailRqt);
            } catch (ApiAccessException e) {

                throw new BusException("get_activity_detail_fail", super.getErrorMsg(e.getMessage()));
            }

        } else {
            GetActivityDetailNoLoginRqt getActivityDetailNoLoginRqt = new GetActivityDetailNoLoginRqt();
            getActivityDetailNoLoginRqt.setActivityId(getActivityDetailApiRqt.getActivityId());
            try {
                activityDetail = activityBusinessServiceApi.getActivityDetailNoLogin(getActivityDetailNoLoginRqt);
            } catch (ApiAccessException e) {
                String[] split = e.getMessage().split("【业务异常】");
                String[] split1 = e.getMessage().split("message: ");
                if (split.length > 1) {
                    throw new BusException("get_activity_detail_fail", split[1].replace("]", ""));
                }
                if (split1.length > 1) {
                    throw new BusException("get_activity_detail_fail", split1[1].replace("]", ""));
                } else {
                    throw new BusException("get_activity_detail_fail", e.getMessage());
                }
            }
        }

        //如果终止或者失效
        if ("canceled".equals(activityDetail.getActivityBaseInfo().getActivityState())
                || "expiry".equals(activityDetail.getActivityBaseInfo().getActivityState())) {
            throw new BusException("get_activity_detail_fail", "活动已取消");
        }

        try {
            //基础信息
            GetActivityDetailApiResp.ActivityBaseInfo activityBaseInfo = new GetActivityDetailApiResp.ActivityBaseInfo();
            GetActivityDetailResp.ActivityBaseInfo activityBaseInfo1 = activityDetail.getActivityBaseInfo();
            BeanUtils.copyProperties(activityBaseInfo1, activityBaseInfo);

            String activityUrl = "";
            if (!StringUtils.isEmpty(activityBaseInfo1.getActivityUrl())) {
                activityUrl = activityBaseInfo1.getActivityUrl() + "&t=" + getActivityDetailApiRqt.getIsWeb();
            }

            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");
            //待领奖数，如果自动领奖，待领奖数是0
            if (OperateMethodEnum.AUTO.code.equals(activityDetail.getActivityBaseInfo().getRewardGiveMethod())) {
                getActivityDetailApiResp.setAwarding(0);
            } else {
                getActivityDetailApiResp.setAwarding(activityDetail.getAwarding());
            }

            getActivityDetailApiResp.setHasAwarding(activityDetail.getHasAwarding());

            //判断活动是否开始
            activityBaseInfo.setIsActivityStart(checkActivityStart(activityBaseInfo));

            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo);


            //落地页配置信息
            GetActivityDetailApiResp.LandingPageInfo landingPageInfo = new GetActivityDetailApiResp.LandingPageInfo();
            GetActivityDetailResp.LandingPageInfo landingPageInfo1 = activityDetail.getLandingPageInfo();
            BeanUtils.copyProperties(landingPageInfo1, landingPageInfo);

            //批量图片aid换url
            //rewardImageAid，顺便过滤掉null
            List<Long> imageAidList = activityDetail.getActivityTaskLists().stream().map(GetActivityDetailResp.ActivityTaskBo::getRewardImageAid).filter(Objects::nonNull).collect(Collectors.toList());

            if (!StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    imageAidList.add(Long.valueOf(aid));
                }
            }

            Collections.addAll(imageAidList,
                    landingPageInfo1.getPreviewImageAid(),
                    landingPageInfo1.getBackgroundImageAid(),
                    landingPageInfo1.getTopImageAid(),
                    landingPageInfo1.getAppTopImageAid(),
                    landingPageInfo1.getRewardImageAid()
            );

            //过滤0，null，使用stream过滤 TODO
            List<String> imageAidListNotNull = new ArrayList<>();
            for (Long aLong : imageAidList) {
                if (aLong != null && aLong != 0) {
                    imageAidListNotNull.add(aLong.toString());
                }
            }

            //服务id
            //服务map<服务id,服务名>
//            Map<Long, String> serveNameMap = getServeNameMap(activityDetail.getActivityTaskLists());
            //服务id
            //服务map<服务id,服务名>
            List<ActivityTaskBo> activityTaskLists1 = activityDetail.getActivityTaskLists();
            //获取服务
            Map<Long, String> serveNameMap = this.getServeMap(activityTaskLists1, getActivityDetailApiRqt.getActivityId());


            Map<Long, String> imageResultMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(imageAidListNotNull)) {
                imageResultMap = httpImageUtils.sendPostRequest(imageAidListNotNull);
            }

            //落地页图片aid换成url
            landingPageInfo.setPreviewImageUrl(imageResultMap.get(landingPageInfo1.getPreviewImageAid()));
            landingPageInfo.setBackgroundImageUrl(imageResultMap.get(landingPageInfo1.getBackgroundImageAid()));
            if (getActivityDetailApiRqt.getUserClass().equals(UserTypeEnum.MASTER.type)) {
                landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));
            } else {
                if (isApp || (getActivityDetailApiRqt.getIsH5() != null && getActivityDetailApiRqt.getIsH5() == 1)) {
                    landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getAppTopImageAid()));
                } else {
                    landingPageInfo.setTopImageUrl(imageResultMap.get(landingPageInfo1.getTopImageAid()));
                }
            }


            landingPageInfo.setRewardTimageUrl(imageResultMap.get(landingPageInfo1.getRewardImageAid()));
            landingPageInfo.setIsShowQuestionBottom(landingPageInfo1.getIsShowQuestionBottom());
            landingPageInfo.setActivityModelTitle(landingPageInfo1.getActivityModelTitle());

            List<String> activityDetailList = new ArrayList<>();

            if (!StringUtils.isEmpty(landingPageInfo1.getActivityModelDetail())) {

                //解析图片
                List<String> detailList = JSONArray.parseArray(landingPageInfo1.getActivityModelDetail(), String.class);

                for (String aid : detailList) {
                    activityDetailList.add(imageResultMap.getOrDefault(Long.valueOf(aid), ""));
                }
            }

            landingPageInfo.setActivityModelDetail(activityDetailList);

            getActivityDetailApiResp.setLandingPageInfo(landingPageInfo);

            //实例化任务列表
            List<GetActivityDetailApiResp.ActivityTaskList> activityTaskLists = new ArrayList<>();
            //提前批量查询现金券及卷包
            Map<Long, VoucherRewardBo> voucherRewardBoMap = this.voucherBatchGetRewardValue(activityDetail.getActivityTaskLists(), getActivityDetailApiRqt.getUserClass());

            //任务列表复制
            for (GetActivityDetailResp.ActivityTaskBo activityTaskList : activityDetail.getActivityTaskLists()) {
                GetActivityDetailApiResp.ActivityTaskList activityTaskListTmp = new GetActivityDetailApiResp.ActivityTaskList();
                if (activityTaskList.getTaskOrderLimit() != null) {
                    GetActivityDetailApiResp.ActivityTaskList.TaskOrderLimit taskOrderLimit = new GetActivityDetailApiResp.ActivityTaskList.TaskOrderLimit();

                    if (activityTaskList.getTaskOrderLimit().getServeIds() != null) {
                        List<String> serveList = new ArrayList<>();

                        if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(activityTaskList.getTaskOrderLimit().getServeIds())) {
                            JSONArray serveObj = JSONObject.parseArray(activityTaskList.getTaskOrderLimit().getServeIds());
                            for (Object object : serveObj) {
                                if (!StringUtils.isEmpty(serveNameMap.get(Long.parseLong(object.toString())))) {
                                    serveList.add(serveNameMap.get(Long.parseLong(object.toString())));
                                }
                            }
                            taskOrderLimit.setServes(String.join(",", serveList));
                        }
                    }
                    BeanUtils.copyProperties(activityTaskList.getTaskOrderLimit(), taskOrderLimit);

                    //订单类型user  转中文 user:用户订单,enterprise:总包订单,NULL:不限
                    if (StringUtils.isEmpty(taskOrderLimit.getSpType())) {
                        taskOrderLimit.setFromAccountTypeName("不限");
                    } else {
                        taskOrderLimit.setFromAccountTypeName(taskOrderLimit.getSpType().replace("master", "师傅订单").replace("enterprise", "总包订单"));
                    }

                    //来源(客户端)[site:网站,enterprise_system:总包外部订单,weixin:微信,ikea:宜家,thirdpart:第三方平台,applet:小程序,NULL:不限（多选）],
                    if (StringUtils.isEmpty(taskOrderLimit.getOrderFrom())) {
                        taskOrderLimit.setOrderFromName("不限");
                    } else {
                        taskOrderLimit.setOrderFromName(taskOrderLimit.getOrderFrom().replace("site", "商家web后台").replace("ios,android", "APP").replace("thirdpart", "第三方"));

                    }
                    //appointMethodName 下单模式[normal:直接指派,open:公开抛单,definite_price:一口价,advance_payment:预付款,NULL:不限（多选）]
                    if (StringUtils.isEmpty(taskOrderLimit.getAppointMethod())) {
                        taskOrderLimit.setAppointMethodName("不限");
                    } else {

                        taskOrderLimit.setAppointMethodName(taskOrderLimit.getAppointMethod().replace("open", "报价招标").replace("normal", "直接指派").replace("definite_price", "一口价"));
                    }
                    //商品数量
                    if (taskOrderLimit.getGoodsNumber() == 0) {
                        taskOrderLimit.setGoodsNumberName("不限");
                    } else {
                        if (taskOrderLimit.getGoodsNumber() == 1) {
                            taskOrderLimit.setGoodsNumberName("单个商品");
                        } else {
                            taskOrderLimit.setGoodsNumberName("套装商品（商品数量大于等于2）");
                        }
                    }
                    if (ObjectUtils.isEmpty(taskOrderLimit.getServes())) {
                        taskOrderLimit.setServes("不限");
                    }

                    activityTaskListTmp.setTaskOrderLimit(taskOrderLimit);
                }

                if (activityTaskList.getRewardImageAid() != null) {
                    activityTaskListTmp.setRewardImageUrl(imageResultMap.get(activityTaskList.getRewardImageAid()));
                }
                BeanUtils.copyProperties(activityTaskList, activityTaskListTmp);


                if (isApp || (getActivityDetailApiRqt.getIsH5() != null && getActivityDetailApiRqt.getIsH5() == 1)) {
                    activityTaskListTmp.setButtonSkipUrl(activityTaskList.getButtonSkipUrlMap().get("app"));
                } else {
                    activityTaskListTmp.setButtonSkipUrl(activityTaskList.getButtonSkipUrlMap().get("web"));
                }
                if (getActivityDetailApiRqt.getUserClass().equals(UserTypeEnum.MERCHANT.type)) {
                    //奖励价值赋值，优惠券类型
                    if (voucherRewardBoMap != null && activityTaskList.getRewardExtraId() != null && activityTaskList.getRewardExtraId() > 0) {
                        VoucherRewardBo voucherVo = voucherRewardBoMap.get(activityTaskList.getRewardExtraId());
                        if (voucherVo != null) {
                            activityTaskListTmp.setVoucherType(voucherVo.getVoucherType());
                            activityTaskListTmp.setRewardValue(voucherVo.getRewardValue());
                        }
                    } else {
                        //非优惠券奖励类型
                        activityTaskListTmp.setRewardValue(activityTaskList.getRewardGiveValue());
                    }
//                    activityTaskListTmp.setRewardValue(
//                            this.getRewardValue(
//                                    activityTaskList.getActivityTaskInfo().getRewardExtraId(),
//                                    activityTaskList.getRewardGiveValue()
//                                    , activityTaskList.getRewardSymbol(), activityTaskListTmp));
                }
                if (activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER.type)
                        || activityTaskList.getRewardSymbol().equals(RewardSymbol.VOUCHER_PACK.type)) {
                    activityTaskListTmp.setRewardGiveValue(activityTaskListTmp.getRewardValue());
                }

                //付款/验收如果是返点需要计算具体值
                if (activityTaskListTmp.getTaskSymbol().equals(TaskTypeEnum.ORDER_PAY.symbol)
                        || activityTaskListTmp.getTaskSymbol().equals(TaskTypeEnum.ORDER_CHECK.symbol)) {
                    if (activityTaskListTmp.getRewardGiveType().equals(RewardGiveTypeEnum.RETURN_POINT.type)) {
                        //并且是返点类型
                        BigDecimal divide = activityTaskListTmp.getRewardValue().multiply(activityTaskListTmp.getTargetAmountValue()).divide(new BigDecimal(100));
                        activityTaskListTmp.setRewardGiveValue(divide);
                    }
                }

                BigDecimal rewardGiveValue = activityTaskListTmp.getRewardGiveValue();

                if (rewardGiveValue.compareTo(new BigDecimal("100")) > 0) {
                    activityTaskListTmp.setRewardGiveValue(rewardGiveValue.setScale(0, RoundingMode.HALF_DOWN));
                } else {
                    activityTaskListTmp.setRewardGiveValue(rewardGiveValue.setScale(2, RoundingMode.HALF_DOWN));
                }


                activityTaskListTmp.setRewardType(activityTaskList.getRewardConfigId());

                if (activityTaskList.getUserActivityTask() != null) {
                    activityTaskListTmp.setUserActivityTaskId(activityTaskList.getUserActivityTask().getUserActivityTaskId());
                }

                //设置任务文案 xx内完成1单
                if (isLogin) {
                    activityTaskListTmp.setTitle(this.getTaskTitle(activityTaskList.getTaskSymbol(), activityTaskList.getUserActivityTask()));
                } else {
                    activityTaskListTmp.setTitle(this.getTaskTitleNoLogin(activityTaskList.getTaskSymbol()));
                }

                //设置任务描述 最多可领取3次
                activityTaskListTmp.setTitleDesc(this.getTaskDesc(activityTaskList.getActivityTaskInfo()));

                if (activityBaseInfo.getIsActivityStart() == 1) {
                    if (activityTaskList.getButtonStatus() == 3 || activityTaskList.getButtonStatus() == 0) {
                        activityTaskListTmp.setSecondsRemain(0L);
                    } else {
                        activityTaskListTmp.setSecondsRemain(this.secondsRemain(getActivityDetailApiResp.getActivityBaseInfo(), activityTaskList.getTimeLimit().longValue(), activityTaskList.getApplyTime()));
                    }
                } else {
                    activityTaskListTmp.setSecondsRemain(0L);
                }

                //没报名倒计时返回0
                if (activityBaseInfo.getIsApply() == 0) {
                    activityTaskListTmp.setSecondsRemain(0L);
                }
//                if (ActivityConstant.ActivitymbolEnumList.contains(TaskSymbolEnum.valueOf(activityTaskListTmp.getTaskSymbol()))) {
                if ("create_order".equals(activityTaskListTmp.getTaskSymbol())
                        || "appoint_master".equals(activityTaskListTmp.getTaskSymbol())
                        || "order_pay".equals(activityTaskListTmp.getTaskSymbol())
                        || "order_check".equals(activityTaskListTmp.getTaskSymbol())
                        || "manual_rate".equals(activityTaskListTmp.getTaskSymbol())
                ) {
                    if (activityTaskListTmp.getTaskOrderLimit().getGoodsNumber() != 0
                            || activityTaskListTmp.getTaskOrderLimit().getAppointMethod() != null
                            || activityTaskListTmp.getTaskOrderLimit().getFromAccountType() != null
                            || activityTaskListTmp.getTaskOrderLimit().getOrderFrom() != null
                            || activityTaskListTmp.getTaskOrderLimit().getServes() != null
                    ) {
                        activityTaskListTmp.setIsShowOrderLimit(1);
                    }
                }
                if (!StringUtils.isEmpty(activityTaskListTmp.getTargetAmountValueRange())) {
                    activityTaskListTmp.setTargetAmountValueRange(activityTaskListTmp.getTargetAmountValueRange().replace("[", "").replace("]", ""));
                }

                activityTaskListTmp.setActivityUrl(activityUrl);
                activityTaskLists.add(activityTaskListTmp);
            }

            //任务列表
            getActivityDetailApiResp.setActivityTaskLists(activityTaskLists);

            List<Integer> collect = activityTaskLists.stream().map(GetActivityDetailApiResp.ActivityTaskList::getButtonStatus).collect(Collectors.toList());

            GetActivityDetailApiResp.ActivityBaseInfo activityBaseInfo2 = getActivityDetailApiResp.getActivityBaseInfo();


            if (RewardGiveStrategyEnum.MULTIPLE.code.equals(activityBaseInfo2.getRewardGiveStrategy())) {
                if ((!collect.contains(2)) && (!collect.contains(3)) && (!collect.contains(4)) && activityTaskLists.size() > 0) {
                    activityBaseInfo2.setHasReceivedReward(1);
                } else {
                    activityBaseInfo2.setHasReceivedReward(0);
                }
            }

            getActivityDetailApiResp.setActivityBaseInfo(activityBaseInfo2);
            getActivityDetailApiResp.setServerTimeStamp(System.currentTimeMillis() / 1000L);

            //判断奖励是否都领完
            List<GetActivityDetailApiResp.ActivityTaskList> rewardCompleted = activityTaskLists.stream().filter(f -> f.getButtonStatus().equals(ActivityButtonStateEnum.Collected.code)
                    || f.getButtonStatus().equals(ActivityButtonStateEnum.CollectedComplete.code)
                    || f.getButtonStatus().equals(ActivityButtonStateEnum.CollectedCompleteALl.code)
            ).collect(Collectors.toList());

            if (rewardCompleted.size() == activityTaskLists.size()) {
                getActivityDetailApiResp.setRewardCompleted(1);
            }


            return getActivityDetailApiResp;
        } catch (ApiAccessException e) {
            throw new BusException("get_activity_detail_fail", e.getMessage());
        }
    }

    /**
     * 获取服务信息
     */
    public Map<Long, String> getServeMap(List<ActivityTaskBo> activityTaskLists1, Long activityId) {

        Map<Long, String> serveNameMap = new HashMap<>();

        //键
        String cashKey = String.format(CacheKeyConstant.ACTIVITY_PAGE_SERVE, activityId);

        String serveString = redisHelper.get(cashKey);
        if (!StringUtils.isEmpty(serveString)) {
            Map map = JSON.parseObject(serveString, Map.class);
            map.forEach((k, v) -> {
                serveNameMap.put(Long.valueOf(k.toString()), v.toString());
            });
            return serveNameMap;
        }


        List<Long> serveIdList = new ArrayList<>();
        for (GetActivityDetailResp.ActivityTaskBo activityTaskList : activityTaskLists1) {
            //StringUtil.isEmpty
            if (Objects.nonNull(activityTaskList.getTaskOrderLimit()) && Objects.nonNull(activityTaskList.getTaskOrderLimit().getServeIds())) {
                if (!StringUtils.isEmpty(activityTaskList.getTaskOrderLimit().getServeIds())) {
                    JSONArray objects = JSONObject.parseArray(activityTaskList.getTaskOrderLimit().getServeIds());
                    for (Object object : objects) {
                        serveIdList.add((Long.parseLong(object.toString())));
                    }
                }
            }
        }

        Set<Long> serveIdSet = new HashSet<>();
        serveIdSet.addAll(serveIdList);


        List<ServeBaseInfoResp> serves = new ArrayList<>();
        if (serveIdSet.size() > 0) {
            ServeIdSetReq serveIdSetReq = new ServeIdSetReq();
            serveIdSetReq.setServeIdSet(serveIdSet);
            serves = serveServiceApi.getServeBaseInfo(serveIdSetReq);
        }

        for (ServeBaseInfoResp serve : serves) {
            if (serve.getLevel() == 2) {
                serveNameMap.put(serve.getServeId(), serve.getName());
            }
        }
        if (serveNameMap.size() > 0) {
            redisHelper.set(cashKey, JSON.toJSONString(serveNameMap), CacheKeyConstant.ACTIVITY_PAGE_SERVE_TIME);
        }

        return serveNameMap;
    }

    /**
     * 调用活动落地页详情浏览记录接口
     */
    public void actiLandIngClick(Long userId, Long activityId, String userClass) {
        if (userId != 0L) {
            ActivityLandingclickReq activityLandingclickReq = new ActivityLandingclickReq();
            activityLandingclickReq.setActivityId(activityId);
            activityLandingclickReq.setUserClass(userClass);
            activityLandingclickReq.setLookTime(new Date());
            activityLandingclickReq.setUserId(userId);
            adPullApi.actiLandingclickCollect(activityLandingclickReq);
        }

    }

    /***
     * @Description：获取
     * @param: activityTaskLists
     * @return：
     */
    private Map<Long, String> getServeNameMap(List<ActivityTaskBo> activityTaskLists) {
        Map<Long, String> serveNameMap = new HashMap<>();
        Set<Long> longSet = Sets.newHashSet();
        activityTaskLists.forEach(
                activityTaskList -> {
                    if (activityTaskList.getTaskOrderLimit() != null && !StringUtils.isEmpty(activityTaskList.getTaskOrderLimit().getServeIds())) {
                        List<Long> list = JSONObject.parseArray(activityTaskList.getTaskOrderLimit().getServeIds(), Long.class);
                        longSet.addAll(list);
                    }
                });
        ServeIdSetReq serveIdSetReq = new ServeIdSetReq();
        serveIdSetReq.setServeIdSet(longSet);
        List<ServeBaseInfoResp> serves = serveServiceApi.getServeBaseInfo(serveIdSetReq);

        for (ServeBaseInfoResp serve : serves) {
            if (serve.getLevel() == 2) {
                serveNameMap.put(serve.getServeId(), serve.getName());
            }
        }
        return serveNameMap;
    }

    /**
     * 活动是否开始，before TODO
     */
    public Integer checkActivityStart(GetActivityDetailApiResp.ActivityBaseInfo activityBaseInfo) {

        long activityStartStamp = activityBaseInfo.getActivityStartTime().getTime() / 1000;

        long activityEndStamp = activityBaseInfo.getActivityEndTime().getTime() / 1000;

        long nowStamp = System.currentTimeMillis() / 1000;

        if (nowStamp > activityStartStamp && nowStamp < activityEndStamp) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 返回时间剩余秒数
     */
    public Long secondsRemain(GetActivityDetailApiResp.ActivityBaseInfo activityBaseInfo,
                              Long timeLimits, Date applyTime) {

        //活动结束时间
        long activityEndTime = activityBaseInfo.getActivityEndTime().getTime() / 1000;

        //任务限时
        long timeLimit = timeLimits;

        long applyTimeStamp = applyTime.getTime() / 1000;

        //当前时间戳
        long nowTime = (new Date()).getTime() / 1000;

        if (timeLimit > 0 && activityEndTime - nowTime > timeLimit) {
            //如果有限时，并且当前时间-活动结束时间差大于限时时间就返回限时时间
            return timeLimit + applyTimeStamp - nowTime;
        } else {
            if (activityEndTime - nowTime > 0) {
                return activityEndTime - nowTime;
            } else {
                return 0L;
            }
        }
    }

    //设置任务文案，xxx内完成1单 登陆状态
    public String getTaskTitle(String taskSymbol, UserActivityTask userActivityTask) {
        if (userActivityTask == null) {
            return "";
        }
        switch (taskSymbol) {
            case "visit":
                return "完成" + userActivityTask.getTargetQuantityValue() + "次登录";
            case "create_order":
                if (userActivityTask.getTimeLimit() > 0) {
                    return "内完成" + userActivityTask.getTargetQuantityValue() + "次下单";
                } else {
                    return "完成" + userActivityTask.getTargetQuantityValue() + "次下单";
                }
            case "appoint_master":
                if (userActivityTask.getTimeLimit() > 0) {
                    return "内完成" + userActivityTask.getTargetQuantityValue() + "次指派";
                } else {
                    return "完成" + userActivityTask.getTargetQuantityValue() + "次指派";
                }
            case "order_pay":
                if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() > 0) {
                    return "内完成" + userActivityTask.getTargetQuantityValue() + "次费用托管，且金额满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() == 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() > 0) {
                    return "内累计托管费用满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() == 0 && userActivityTask.getTimeLimit() > 0) {
                    return "内完成" + userActivityTask.getTargetQuantityValue() + "次费用托管";
                } else if (userActivityTask.getTargetQuantityValue() == 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() == 0) {
                    return "累计托管费用满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() == 0) {
                    return "完成" + userActivityTask.getTargetQuantityValue() + "次费用托管，且金额满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() == 0 && userActivityTask.getTimeLimit() == 0) {
                    return "完成" + userActivityTask.getTargetQuantityValue() + "次费用托管";
                }
            case "order_check":
                if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() > 0) {
                    return "内完成" + userActivityTask.getTargetQuantityValue() + "次订单验收，且金额满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() == 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() > 0) {
                    return "内累计验收订单金额满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() == 0 && userActivityTask.getTimeLimit() > 0) {
                    return "内完成" + userActivityTask.getTargetQuantityValue() + "次验收";
                } else if (userActivityTask.getTargetQuantityValue() == 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() == 0) {
                    return "累计验收订单金额满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() > 0 && userActivityTask.getTimeLimit() == 0) {
                    return "完成" + userActivityTask.getTargetQuantityValue() + "次验收，且金额满" + userActivityTask.getTargetAmountValue() + "元";
                } else if (userActivityTask.getTargetQuantityValue() > 1 && userActivityTask.getTargetAmountValue().intValue() == 0 && userActivityTask.getTimeLimit() == 0) {
                    return "完成" + userActivityTask.getTargetQuantityValue() + "次订单验收";
                }
            case "manual_rate":
                if (userActivityTask.getTimeLimit() > 0) {
                    return "内完成" + userActivityTask.getTargetQuantityValue() + "次评价";
                } else {
                    return "完成" + userActivityTask.getTargetQuantityValue() + "次评价";
                }
            default:
                return null;

        }
    }

    //设置任务文案，xxx内完成1单 登陆状态
    public String getTaskTitleNoLogin(String taskSymbol) {
        switch (taskSymbol) {
            case "visit":
                return "完成指定次数登录";
            case "create_order":
                return "完成指定次数下单";
            case "appoint_master":
                return "完成指定次数指派";
            case "order_pay":
                return "完成指定托管费用";
            case "order_check":
                return "完成指定次数/金额订单验收";
            case "manual_rate":
                return "完成指定次数评价";
        }

        return null;
    }


    //设置任务描述，最多领取三次
    public String getTaskDesc(ActivityTaskBeanBo activityTask) {

        try {
            SimpleDateFormat formatter = new SimpleDateFormat("HH:mm");

            //任务执行类型[single:单次,cycle:持续循环,day:每天,week:每周,month:每月]
            switch (activityTask.getExecuteType()) {
                case "single":
                    return "活动期间最多可领取1次";
                case "cycle":
                    return "活动期间最多可领取" + activityTask.getRewardUserUpperLimit() + "次";
                case "day":
                    return "每天" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                            + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                            + "可领取" + activityTask.getRewardUserDayUpperLimit() + "次";
                case "week":
                    String executeRange = activityTask.getExecuteRange();
                    List<Integer> weeks = JSONObject.parseArray(executeRange, Integer.class);

                    Map<Integer, String> weekMap = new HashMap<>();
                    weekMap.put(1, "周一");
                    weekMap.put(2, "周二");
                    weekMap.put(3, "周三");
                    weekMap.put(4, "周四");
                    weekMap.put(5, "周五");
                    weekMap.put(6, "周六");
                    weekMap.put(7, "周日");

                    List<String> weekstr = new ArrayList<>();
                    for (Integer week : weeks) {
                        weekstr.add(weekMap.get(week));
                    }
                    String join = String.join("、", weekstr);
                    if (activityTask.getRewardUserDayUpperLimit() != 0) {
                        return "每" + join + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每天封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
                    } else {
                        return "每" + join + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每周封顶" + activityTask.getRewardUserWeekUpperLimit() + "次";
                    }
                case "month":
                    List<Integer> months = JSONObject.parseArray(activityTask.getExecuteRange(), Integer.class);
                    String monthStr = "";

                    for (Integer object : months) {
                        if (object != null) {
                            monthStr += object + "、";
                        }
                    }
                    monthStr = StringUtils.trimTrailingCharacter(monthStr, '、');

                    if (activityTask.getRewardUserDayUpperLimit() != 0) {
                        return "每月" + monthStr + "号" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每日封顶" + activityTask.getRewardUserDayUpperLimit() + "次";
                    } else {
                        return "每月" + monthStr + "号" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeStart()))
                                + "到" + formatter.format(new SimpleDateFormat("HH:mm").parse(activityTask.getExecuteTimeRangeEnd()))
                                + "完成任务可领，每月封顶" + activityTask.getRewardUserMonthUpperLimit() + "次";
                    }
                default:
                    return null;
            }

        } catch (Exception e) {
            throw new BusException("data_parse_data_error", e.getMessage());
        }
    }

    /**
     * 优惠券批量查询券值
     * 卷包暂时无批量查询，过滤相同券包，减少查询次数
     */
    public Map<Long, VoucherRewardBo> voucherBatchGetRewardValue(List<GetActivityDetailResp.ActivityTaskBo> activityTaskList, String userClass) {

        Map<Long, VoucherRewardBo> voucherRewardBoMap = new HashMap<>();
        if (!UserTypeEnum.MERCHANT.type.equals(userClass) || CollectionUtils.isEmpty(activityTaskList)) {
            return null;
        }
        //复制方面后面删除操作
        List<GetActivityDetailResp.ActivityTaskBo> activityTaskListNew = new ArrayList<>();
        for (GetActivityDetailResp.ActivityTaskBo vo : activityTaskList) {
            GetActivityDetailResp.ActivityTaskBo bo = new GetActivityDetailResp.ActivityTaskBo();
            BeanUtils.copyProperties(vo, bo);
            activityTaskListNew.add(bo);
        }
        List<Integer> extraIdList = activityTaskListNew.stream().
                filter(f -> (RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) || RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()))
                        && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(extraIdList)) {
            return null;
        }
        /**
         * 先查缓存，缓存没有再往后查
         */
        List<String> cacheReqList = extraIdList.stream().map(m -> String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, m)).collect(Collectors.toList());
        List<String> voucherRewardCacheList = httpImageUtils.getBatchRedisKeyValue(cacheReqList);
        if (CollectionUtils.isNotEmpty(voucherRewardCacheList)) {
            for (String str : voucherRewardCacheList) {
                VoucherRewardBo bo = JSON.parseObject(str, VoucherRewardBo.class);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
            }
            Iterator<GetActivityDetailResp.ActivityTaskBo> iter = activityTaskListNew.iterator();
            while (iter.hasNext()) {
                GetActivityDetailResp.ActivityTaskBo bo = iter.next();
                if (voucherRewardBoMap.get(bo.getRewardExtraId()) != null) {
                    iter.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(activityTaskListNew)) {
            return voucherRewardBoMap;
        }
        /**
         * 优惠券
         */
        List<Integer> rewardExtraIdList = activityTaskListNew.stream().
                filter(f -> RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol()) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        List<VoucherEvent> voucherEventList = null;
        if (CollectionUtils.isNotEmpty(rewardExtraIdList)) {

            GetVoucherEventListReqBean getVoucherEventListReqBean = new GetVoucherEventListReqBean();
            getVoucherEventListReqBean.setEventIdList(rewardExtraIdList);
            voucherEventList = voucherEventApi.getVoucherEventList(getVoucherEventListReqBean);

        }
        if (!CollectionUtils.isEmpty(voucherEventList)) {
            voucherEventList.stream().forEach(voucherEvent -> {
                VoucherRewardBo bo = new VoucherRewardBo();
                //优惠券类型 （money_off:满减-默认 、discount: 折扣）
                bo.setVoucherType(voucherEvent.getMold());
                BigDecimal resultAmount = BigDecimal.ZERO;
                if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherEvent.getMold())) {
                    resultAmount = voucherEvent.getDiscountRatio() == null ? new BigDecimal("0") : voucherEvent.getDiscountRatio();
                } else {
                    String amountWeight = voucherEvent.getAmountWeight();
                    if (!StringUtils.isEmpty(amountWeight)) {
                        if (amountWeight.contains(":")) {
                            resultAmount = new BigDecimal(amountWeight.split(":")[0]);
                        } else {
                            resultAmount = new BigDecimal(amountWeight);
                        }
                    }
                }
                bo.setRewardExtraId(voucherEvent.getEventId().longValue());
                bo.setRewardValue(resultAmount);
                voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                redisHelper.set(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, bo.getRewardExtraId()), JSON.toJSONString(bo), voucherCacheTime);
            });
        }

        /**
         * 券包：满减券面额之和+折扣券最高抵扣金额之和
         */
        List<Integer> packExtraIdList = activityTaskListNew.stream().
                filter(f -> RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol()) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).
                map(m -> m.getRewardExtraId().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packExtraIdList)) {

            BatchQueryTotalAmountByIdReq queryTotalAmountByIdReq1 = new BatchQueryTotalAmountByIdReq();

            queryTotalAmountByIdReq1.setVoucherPackIdList(packExtraIdList);

            BatchQueryTotalAmountByIdResp queryTotalAmountByIdResp1 = voucherEventApi.batchQueryTotalAmountById(queryTotalAmountByIdReq1);

            if (queryTotalAmountByIdResp1 != null && queryTotalAmountByIdResp1.getPackIdForTotalAmount() != null) {

                Map<Integer, BigDecimal> resultMap = queryTotalAmountByIdResp1.getPackIdForTotalAmount();

                packExtraIdList.stream().forEach(packExtraId -> {
                    if (!ObjectUtils.isEmpty(resultMap.get(packExtraId))) {
                        VoucherRewardBo bo = new VoucherRewardBo();
                        bo.setRewardExtraId(packExtraId.longValue());
                        bo.setRewardValue(resultMap.get(packExtraId));
                        voucherRewardBoMap.put(bo.getRewardExtraId(), bo);
                        redisHelper.set(String.format(CacheKeyConstant.VOUCHER_REWARD_KEY, bo.getRewardExtraId()), JSON.toJSONString(bo), voucherCacheTime);
                    }
                });

            }

        }
        activityTaskListNew = null;
        return voucherRewardBoMap;
    }

    //获取任务奖励价值，VoucherEventApi.queryTotalAmountById
    public BigDecimal getRewardValue(Long rewardExtraId, BigDecimal rewardGiveValue, String rewardSymbol, GetActivityDetailApiResp.ActivityTaskList activityTaskListTmp) {

        RewardSymbol rewardSymbol1 = RewardSymbol.fromType(rewardSymbol);

        switch (rewardSymbol1) {
            case VOUCHER:
                /**
                 * 现金券
                 * 如果是满减券，展示面额；举例：50元
                 * 如果是折扣券：展示折扣；举例：9.5折
                 */

                VoucherEvent voucherEvent = voucherEventApi.getEventDetailById(rewardExtraId.intValue());

                BigDecimal resultAmount = new BigDecimal("0");
                if (ObjectUtils.isEmpty(voucherEvent)) {
                    return resultAmount;
                }

                if (Objects.nonNull(activityTaskListTmp)) {
                    //优惠券类型 （money_off:满减-默认 、discount: 折扣）
                    activityTaskListTmp.setVoucherType(voucherEvent.getMold());
                }

                if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherEvent.getMold())) {
                    resultAmount = voucherEvent.getDiscountRatio() == null ? new BigDecimal("0") : voucherEvent.getDiscountRatio();
                } else {
                    String amountWeight = voucherEvent.getAmountWeight();
                    if (!StringUtils.isEmpty(amountWeight)) {
                        if (amountWeight.contains(":")) {
                            resultAmount = new BigDecimal(amountWeight.split(":")[0]);
                        } else {
                            resultAmount = new BigDecimal(amountWeight);
                        }
                    }
                }
                return resultAmount;
            case VOUCHER_PACK:
                /**
                 * 券包：满减券面额之和+折扣券最高抵扣金额之和
                 */
                QueryTotalAmountByIdReq queryTotalAmountByIdReq1 = new QueryTotalAmountByIdReq();
                queryTotalAmountByIdReq1.setQueryId(rewardExtraId.intValue());
                queryTotalAmountByIdReq1.setType(1);

                QueryTotalAmountByIdResp queryTotalAmountByIdResp1 = voucherEventApi.queryTotalAmountById(queryTotalAmountByIdReq1);

                BigDecimal resultNum = new BigDecimal("0");
                if (!ObjectUtils.isEmpty(queryTotalAmountByIdResp1)) {
                    resultNum = queryTotalAmountByIdResp1.getTotalAmount();
                }
//                VoucherPackDetailReq req = new VoucherPackDetailReq();
//                req.setPackId(rewardExtraId.intValue());
//                log.info("卷包请求参数：{}", req);
//                VoucherPackConfig configVo = voucherPackApi.detail(req);
//                log.info("券包返回数据：{}", configVo);
//                BigDecimal resultNum = new BigDecimal("0");
//                if (ObjectUtils.isEmpty(configVo) || StringUtils.isEmpty(configVo.getVoucher())) {
//                    return resultNum;
//                }
//                //[{"eventId":19,"voucherNum":1},{"eventId":21,"voucherNum":2}]
//                List<VoucherPackBo> voucherList = JSON.parseArray(configVo.getVoucher(), VoucherPackBo.class);
//                if (CollectionUtils.isEmpty(voucherList)) {
//                    return resultNum;
//                }
//                List<Integer> eventIdList = voucherList.stream().map(VoucherPackBo::getEventId).collect(Collectors.toList());
//                GetVoucherEventListReqBean reqBean = new GetVoucherEventListReqBean();
//                reqBean.setEventIdList(eventIdList);
//                List<VoucherEvent> voucherEventList = voucherEventApi.getVoucherEventList(reqBean);
//                if (CollectionUtils.isEmpty(voucherEventList)) {
//                    return resultNum;
//                }
//                for (VoucherEvent vo : voucherEventList) {
//                    List<VoucherPackBo> packBoList = voucherList.stream().filter(f -> f.getEventId().equals(vo.getEventId())).collect(Collectors.toList());
//                    if (CollectionUtils.isEmpty(packBoList)) {
//                        continue;
//                    }
//                    String amountWeight = vo.getAmountWeight();
//                    BigDecimal amountWeightNum = new BigDecimal(0);
//                    if (!StringUtils.isEmpty(amountWeight)) {
//                        if (amountWeight.contains(":")) {
//                            amountWeightNum = new BigDecimal(amountWeight.split(":")[0]);
//                        } else {
//                            amountWeightNum = new BigDecimal(amountWeight);
//                        }
//                    }
//                    resultNum = resultNum.add(amountWeightNum.multiply(new BigDecimal(packBoList.get(0).getVoucherNum())));
//                }
                return resultNum;
            default:
                return rewardGiveValue;
        }

    }

    @Override
    @UserLoginInfo
    public List<GetActivityListApiResp> getList(GetActivityListApiRqt getActivityListApiRqt) {


        DiscountActivityListReq discountActivityListReq = new DiscountActivityListReq();
        discountActivityListReq.setUserId(getActivityListApiRqt.getUserId().toString());
        BeanUtils.copyProperties(getActivityListApiRqt, discountActivityListReq);

        List<DiscountActivityListResp> DiscountActivityListRespList = null;
        try {
            DiscountActivityListRespList = activityBusinessServiceApi.getList(discountActivityListReq);
        } catch (ApiAccessException e) {
            throw new BusException("getlist_fail", "获取活动列表失败");
        }

        //预览图,背景图，头图aidlist
        List<String> imageAidList = new ArrayList<>();

        imageAidList.addAll(DiscountActivityListRespList.stream().filter(f -> f.getLandingPageInfo().getPreviewImageAid() != null)
                .map(m -> m.getLandingPageInfo().getPreviewImageAid())
                .map(Objects::toString).collect(Collectors.toList()));

        imageAidList.addAll(DiscountActivityListRespList.stream().filter(f -> f.getLandingPageInfo().getBackgroundImageAid() != null)
                .map(m -> m.getLandingPageInfo().getBackgroundImageAid())
                .map(Objects::toString).collect(Collectors.toList()));

        imageAidList.addAll(DiscountActivityListRespList.stream().filter(f -> f.getLandingPageInfo().getTopImageAid() != null)
                .map(m -> m.getLandingPageInfo().getTopImageAid())
                .map(Objects::toString).collect(Collectors.toList()));

        //返回值
        List<GetActivityListApiResp> getActivityListApiRespList = new ArrayList<>();

        Map<Long, String> imageResults = null;
        //批量获取图片返回map<aid,url>
        if (imageAidList.size() > 0) {
            imageResults = httpImageUtils.sendPostRequest(imageAidList);
        }


        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        for (DiscountActivityListResp discountActivityListResp : DiscountActivityListRespList) {

            GetActivityListApiResp getActivityListApiResp = new GetActivityListApiResp();

            GetActivityListApiResp.ActivityBaseInfo activityBaseInfo = new GetActivityListApiResp.ActivityBaseInfo();
            BeanUtils.copyProperties(discountActivityListResp.getActivityBaseInfo(), activityBaseInfo);
            activityBaseInfo.setActivityName("");
            activityBaseInfo.setActivityDescription("");
            String activityUrl = discountActivityListResp.getActivityBaseInfo().getActivityUrl();
            if (!StringUtils.isEmpty(activityUrl)) {
                activityBaseInfo.setActivityUrl(activityUrl + "&t=" + getActivityListApiRqt.getIsWeb());
            }


            getActivityListApiResp.setActivityBaseInfo(activityBaseInfo);

            GetActivityListApiResp.LandingPageInfo landingPageInfo = new GetActivityListApiResp.LandingPageInfo();

            if (discountActivityListResp.getLandingPageInfo().getBackgroundImageAid() != null && imageResults != null) {
                landingPageInfo.setBackgroundImageUrl(imageResults.getOrDefault(discountActivityListResp.getLandingPageInfo().getBackgroundImageAid(), ""));
            }

            if (discountActivityListResp.getLandingPageInfo().getPreviewImageAid() != null && imageResults != null) {
                landingPageInfo.setPreviewImageUrl(imageResults.getOrDefault(discountActivityListResp.getLandingPageInfo().getPreviewImageAid(), ""));
            }

            if (discountActivityListResp.getLandingPageInfo().getTopImageAid() != null && imageResults != null) {
                landingPageInfo.setTopImageUrl(imageResults.getOrDefault(discountActivityListResp.getLandingPageInfo().getTopImageAid(), ""));
            }

            BeanUtils.copyProperties(discountActivityListResp.getLandingPageInfo(), landingPageInfo);

            //设置时间范围
            landingPageInfo.setActivityDayRange(formatter.format(activityBaseInfo.getActivityStartTime()) + " 至 " + formatter.format(activityBaseInfo.getActivityEndTime()));

            getActivityListApiResp.setLandingPageInfo(landingPageInfo);

            getActivityListApiRespList.add(getActivityListApiResp);
        }
        return getActivityListApiRespList;
    }

    /**
     * 广告结果回传
     */
    @Override
    public Integer backDialogAd(BackDialogAdRqt backDialogAdRqt) {


        //cache 里面的userId
        Long cacheUserId = backDialogAdRqt.getUserId();


        FallbackResultSaveReq fallbackResultSaveReq = new FallbackResultSaveReq();
        fallbackResultSaveReq.setAdId(backDialogAdRqt.getAdId());
        fallbackResultSaveReq.setUserId(cacheUserId);
        fallbackResultSaveReq.setCallbackTime(new Date());
        fallbackResultSaveReq.setIsUnLoginPull(backDialogAdRqt.getIsUnLoginPull());
        try {

            adPullApi.fallbackResultSave(fallbackResultSaveReq);
            if (StringUtils.isEmpty(backDialogAdRqt.getAdPositionSymbol())) {
                return 1;
            }
            //非弹窗广告位，回调成功后需删除缓存
            String userFormatKey = String.format(CacheKeyConstant.USER_PULL_AD_POSITION_KEY, backDialogAdRqt.getUserClass(), backDialogAdRqt.getLaunchPort(), backDialogAdRqt.getAdPositionSymbol(), cacheUserId);
            if (redisHelper.exists(userFormatKey)) {
                redisHelper.del(userFormatKey);
            }
            return 1;
        } catch (ApiAccessException e) {
            throw new BusException("back_fail", "广告回传失败");
        }


    }

    /**
     * 用户活动报名
     */
    @Override
    public Integer apply(ApplyActivityApiRqt applyActivityApiRqt) {

        ActivityApplyRqt activityApplyRqt = new ActivityApplyRqt();
        activityApplyRqt.setApplyTime(new Date());
        activityApplyRqt.setUserId(applyActivityApiRqt.getUserId());
        activityApplyRqt.setActivityId(applyActivityApiRqt.getActivityId());
        activityApplyRqt.setUserClass(applyActivityApiRqt.getUserClass());
        activityApplyRqt.setApplyConditionType(applyActivityApiRqt.getApplyConditionType());
        activityApplyRqt.setApplyConditionValue(applyActivityApiRqt.getApplyConditionValue());

        try {

            int apply = activityBusinessServiceApi.apply(activityApplyRqt);
            if (apply == 1) {
                if (applyActivityApiRqt.getUserClass().equals(UserTypeEnum.MERCHANT.type)) {
                    ActivityTaskRunRqt activityTaskRunRqt = ActivityTaskRunRqt.builder().userClass(applyActivityApiRqt.getUserClass())
                            .taskSymbol(TaskSymbolEnum.VISIT.taskSymbol)
                            .launchPorts(applyActivityApiRqt.getIsWeb() == 1 ? LaunchPortEnum.MERCHANT_WEB.type : LaunchPortEnum.MERCHANT_APP.type)
                            .userId(applyActivityApiRqt.getUserId())
                            .executeTime(new Date())
                            .extraType(TaskSymbolEnum.VISIT.taskSymbol).extraNo(String.valueOf(System.currentTimeMillis())).build();

                    activityBusinessServiceApi.taskRun(activityTaskRunRqt);
                }

                //异步线程
                CompletableFuture<Void> f = CompletableFuture.supplyAsync(() -> {
                    ActivityDetailByIdRqt activityDetailRqt = new ActivityDetailByIdRqt();
                    activityDetailRqt.setActivityId(applyActivityApiRqt.getActivityId());
                    ActivityBase activityBase = activityBusinessServiceApi.getActivityDetailById(activityDetailRqt);
                    if (ObjectUtils.isEmpty(activityBase)) {
                        throw new BusException("查询活动信息异常");
                    }
                    TaskReceiveToAdHelpReq taskReciveToAdHelpReq = new TaskReceiveToAdHelpReq();
                    taskReciveToAdHelpReq.setActivityId(applyActivityApiRqt.getActivityId());
                    taskReciveToAdHelpReq.setUserClass(applyActivityApiRqt.getUserClass());
                    taskReciveToAdHelpReq.setUserId(applyActivityApiRqt.getUserId());
                    taskReciveToAdHelpReq.setReceiveTime(new Date());
                    taskReciveToAdHelpReq.setTriggerTime(new Date());
                    taskReciveToAdHelpReq.setActivityEndTime(activityBase.getActivityEndTime());
                    taskReciveToAdHelpReq.setActivityStartTime(activityBase.getActivityStartTime());
                    adPullApi.taskReciveToAdHelp(taskReciveToAdHelpReq);
                    return null;
                });
            }
            return apply;
        } catch (ApiAccessException e) {
            throw new BusException("sign_fail", super.getErrorMsg(e.getMessage()));
        }
    }


    /**
     * 返回标签信息
     */
    @Override
    public List<Long> getUserGroupIds(String userId, String personaId) {

        String redisKey = String.format(CacheKeyConstant.USER_GROUP_KEY_V2, userId);

        //如果存在,get方法无需提前判断exist
        String cacheValue = redisHelper.get(redisKey);
        if (!StringUtils.isEmpty(cacheValue)) {
            List<Long> cacheList = JSONObject.parseArray(cacheValue, Long.class);
            if (!CollectionUtils.isEmpty(cacheList)) {
                return cacheList;
            }
        }

        List<Long> labelIdList = new ArrayList<>();

        TargetGroupIdRespBo targetGroupId = super.getTargetGroupId(userId, personaId);

        Calendar cal = Calendar.getInstance();
        int hourOfDay = cal.get(Calendar.HOUR_OF_DAY);

        if (targetGroupId != null) {
            String groupids = targetGroupId.getGroupIds();
            if (!StringUtils.isEmpty(groupids)) {
                labelIdList = Arrays.stream(groupids.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                if (bigdataGroupCacheTime > 0) {
                    redisHelper.set(redisKey, JSONObject.toJSONString(labelIdList), super.bigdataGroupCacheTime);
                }
            }
        }
        return labelIdList;
    }


    /**
     * 用户活动下发
     */
    @Override
    public Integer issue(IssueApiRqt issueApiRqt) {

        ActivityIssueRqt activityIssueRqt = new ActivityIssueRqt();
        activityIssueRqt.setUserClass(issueApiRqt.getUserClass());

        String launchPort = null;

        if (!StringUtils.isEmpty(issueApiRqt.getLaunchPort())) {
            launchPort = issueApiRqt.getLaunchPort();
        } else {
            launchPort = issueApiRqt.getLaunchPorts();
        }
        //处理传递到平台层launchPorts为空的问题
        activityIssueRqt.setLaunchPorts(launchPort);
        activityIssueRqt.setUserId(issueApiRqt.getUserId());

        List<Long> labelIds = new ArrayList<>();

        //根据用户id调用大数据接口 返回群体id列表
        String personaId;
        switch (issueApiRqt.getUserClass()) {
            case "merchant":
                personaId = "1";
                break;
            case "master":
                if (LaunchPortEnum.TOC_MASTER_APP.type.equals(launchPort)) {
                    //c端师傅
                    personaId = "4";
                } else {
                    //b端师傅
                    personaId = "2";
                }
                break;
            case "customer":
                personaId = "3";
                break;
            default:
                personaId = null;
                break;
        }

        //大数据人群标签，可使用fatureTask同步请求
        labelIds.addAll(super.getUserGroupIds(issueApiRqt.getUserId().toString(), personaId));

        try {
            //根据师傅id调用大数据接口 返回群体id列表
            ActivityIssueRqt activityIssueRqt1 = ActivityIssueRqt.builder()
                    .userClass(issueApiRqt.getUserClass())
                    .launchPorts(launchPort)
                    .userId(issueApiRqt.getUserId())
                    .userLabelIds(labelIds)
                    .build();
            int issue = 0;
            try {
                issue = activityBusinessServiceApi.issue(activityIssueRqt1);
            } catch (Exception e) {
                log.info("请求超时");
            }


            if (issueApiRqt.getUserClass().equals(UserTypeEnum.MERCHANT.type) || issueApiRqt.getUserClass().equals(UserTypeEnum.CUSTOMER.type)) {
                //只有商户/家庭的才有访问类型的任务
                //活动下发同时异步触发“访问”活动任务
                //延时消息消息体
                String taskSymbol = issueApiRqt.getUserClass().equals(UserTypeEnum.MERCHANT.type) ? TaskSymbolEnum.VISIT.taskSymbol : TaskSymbolEnum.CUSTOMER_VISIT.taskSymbol;

                MerchantVisitDelayBo merchantVisitDelayBo = MerchantVisitDelayBo.builder().userClass(activityIssueRqt.getUserClass())
                        .taskSymbol(taskSymbol)
                        .launchPorts(activityIssueRqt.getLaunchPorts())
                        .userId(activityIssueRqt.getUserId())
                        .executeTime(new Date())
                        .extraType(taskSymbol).extraNo(String.valueOf(System.currentTimeMillis())).build();

                merchantDelayVisitProducer.sendMerchantVisitDelay(merchantVisitDelayBo);

            }

            return issue;
        } catch (ApiAccessException e) {
            throw new BusException("issue_fail", super.getErrorMsg(e.getMessage()));
        }
    }

    /**
     * 用户浏览落地页详情记录
     */
    @Override
    public Integer actiLandingclickCollect(EnterActivityDetailRqt enterActivityDetailRqt) {

        //查询活动信息
        GetUserHasSignUpRqt activityDetailSignRqt = new GetUserHasSignUpRqt();
        activityDetailSignRqt.setActivityId(enterActivityDetailRqt.getActivityId());
        activityDetailSignRqt.setUserId(enterActivityDetailRqt.getUserId());
        ActivityApplyUpResp activityApplyUpResp = activityBusinessServiceApi.getUserHasApplyUp(activityDetailSignRqt);
        if (ObjectUtils.isEmpty(activityApplyUpResp)) {
            throw new BusException("查询活动信息异常");
        }
        //没有报名就不调用落地页记录保存接口
        if (activityApplyUpResp.getIsApply() == null || activityApplyUpResp.getIsApply() != 1 || !ActivityStateEnum.LAUNCHING.code.equals(activityApplyUpResp.getActivityState())) {
            return 2;
        }
        ActivityLandingclickReq activityLandingclickReq = new ActivityLandingclickReq();

        activityLandingclickReq.setUserId(enterActivityDetailRqt.getUserId());
        activityLandingclickReq.setActivityId(enterActivityDetailRqt.getActivityId());
        activityLandingclickReq.setUserClass(enterActivityDetailRqt.getUserClass());
        activityLandingclickReq.setPort(enterActivityDetailRqt.getLaunchPort());
        activityLandingclickReq.setLookTime(new Date());
        activityLandingclickReq.setActivityEndTime(activityApplyUpResp.getActivityEndTime());
        activityLandingclickReq.setActivityStartTime(activityApplyUpResp.getActivityStartTime());
        activityLandingclickReq.setAdId(enterActivityDetailRqt.getAdId());
        activityLandingclickReq.setTriggerTime(new Date());
        try {
            return adPullApi.actiLandingclickCollect(activityLandingclickReq);
        } catch (ApiAccessException e) {
            throw new BusException("landing_fail", "用户浏览落地页详情记录失败");
        }

    }


    /**
     * 获取弹窗广告
     */
    @Override
    @UserLoginInfo
    @ExceptionHandle(note = "弹窗广告获取异常")
    public GetUserAdResp getDialogAd(GetUserPopAdRqt getUserPopAdRqt) {
        GetUserAdResp getUserAdResp = new GetUserAdResp();
        Long userId = getUserPopAdRqt.getUserId();
        List<String> tagList = new ArrayList<>();
        try {
            tagList = this.getTagListFromBigData(getUserPopAdRqt.getUserClass(), userId, getUserPopAdRqt.getLaunchPort());
        } catch (Exception e) {
            log.error("getDialogAd调用大数据接口异常:" + e.getMessage());
        }
        PopAdBo popAdBo = PopAdBo.builder().port(getUserPopAdRqt.getLaunchPort())
                .adPositionSymbol(getUserPopAdRqt.getAdPositionSymbol())
                .userClass(getUserPopAdRqt.getUserClass())
                .userId(userId)
                .taskSymbol(getUserPopAdRqt.getTaskSymbol())
                .tagList(tagList)
                .build();

        PullPopAdReq pullPopAdReq = new PullPopAdReq();
        BeanUtils.copyProperties(popAdBo, pullPopAdReq);

        //调用接口返回
        List<PullAdPopupResp> pullAdPopupRespList = new ArrayList<>();

        PullAdPopupResp pullAdPopupResp = null;
        try {
            pullAdPopupRespList = adPullApi.pullPopup(pullPopAdReq);
            if (CollectionUtils.isEmpty(pullAdPopupRespList)) {
                return null;
            }
        } catch (ApiAccessException e) {
            throw new BusException("get_pop_fail", "拉取弹窗广告失败");
        }

        //批量判断是否下发,以及是否达上限
        List<Long> activityIdList = pullAdPopupRespList.stream().filter(f -> f.getAdBaseInfo() != null && f.getAdBaseInfo().getActivityId() != 0).map(m -> m.getAdBaseInfo().getActivityId()).collect(Collectors.toList());
        List<ActivityBaseResp> activityBaseList = this.signUpAndNotUpperLimitActivityDetail(activityIdList, userId, getUserPopAdRqt.getUserClass(), "", true,
                null);
        List<Long> filterActivityIdList = null;
        if (CollectionUtils.isNotEmpty(activityBaseList)) {
            filterActivityIdList = activityBaseList.stream().map(ActivityBaseResp::getActivityId).collect(Collectors.toList());
        }

        for (PullAdPopupResp adPopupResp : pullAdPopupRespList) {
            //判断活动是否下发给用户，如果没有下发就返回null
            if (adPopupResp.getAdBaseInfo() == null) {
                continue;
            }

            if (adPopupResp.getAdBaseInfo().getActivityId() != 0) {
                if (CollectionUtils.isNotEmpty(filterActivityIdList) && filterActivityIdList.contains(adPopupResp.getAdBaseInfo().getActivityId())) {
                    pullAdPopupResp = adPopupResp;
                    break;
                }
            } else {
                //如果都不是活动类型的广告
                pullAdPopupResp = adPopupResp;
                break;
            }
        }

        if (ObjectUtils.isEmpty(pullAdPopupResp)) {
            //如果对象是空，说明没有满足条件的弹窗广告
            return null;
        }
        if (pullAdPopupResp.getMeterialList() == null && pullAdPopupResp.getAdBaseInfo() == null) {
            return null;
        }
        //回调符合条件的弹窗广告aid
        this.backPopAid(pullAdPopupResp.getAdBaseInfo().getAdId(), userId, pullAdPopupResp.getAdBaseInfo().getPopEffectiveTimeId());
        ActivityBaseResp activityBaseResp = null;
        Long activityId = pullAdPopupResp.getAdBaseInfo().getActivityId();
        if (CollectionUtils.isNotEmpty(activityBaseList) && activityId != 0) {
            Optional<ActivityBaseResp> activityBaseRespOptional = activityBaseList.stream().filter(f -> f.getActivityId().equals(activityId)).findFirst();
            activityBaseResp = activityBaseRespOptional.isPresent() ? activityBaseRespOptional.get() : null;
        }
        //组装弹窗广告出参信息：广告信息+活动信息+图片信息
        List<GetUserAdResp.AdInfo> getUserAdRespList = this.packageDialogAdOutput(pullAdPopupResp, getUserPopAdRqt, activityBaseResp);
        getUserAdResp.setAdInfo(getUserAdRespList);

        return getUserAdResp;
    }


    /**
     * 获取用户广告位广告（非弹窗）
     */
    @Override
    @ExceptionHandle(note = "广告位广告获取异常")
    public GetUserAdResp getAdPosition(GetUserAdRqt getUserAdRqt, String token, String tokenApp, String signature) {
        //判断是否是app访问
        boolean isApp = !StringUtils.isEmpty(tokenApp);

        //appEnable是否允许app访问，true是，false否
        if (!appEnable && isApp) {
            return null;
        }
        String isWeb = "app";
        if (!StringUtils.isEmpty(token)) {
            isWeb = "web";
        }

        //返回结果对象
        GetUserAdResp getUserAdResp = new GetUserAdResp();

        //签名统一赋值
        String autograph;
        if (getUserAdRqt.getUserClass().equals(UserTypeEnum.MASTER.type)) {
            autograph = signature;
        } else {
            autograph = isApp ? tokenApp : token;
        }

        //获取用户信息
        Long userId = redisExUtil.checkLoginStatus(autograph, getUserAdRqt.getUserClass());
        if (userId == null || userId == 0L) {
            //非登陆情况下的广告，登陆页，开屏广告(login_background)和app开屏广告(open_screen)
            return this.getUnLoginAdPosition(getUserAdRqt, getUserAdResp);
        }
        log.info("getAdPosition入参：userId={}", userId);
        /**
         *  查询是否已有拉取广告缓存，有则直接使用缓存数据，无则查询
         */
        String userFormatKey = String.format(CacheKeyConstant.USER_PULL_AD_POSITION_KEY, getUserAdRqt.getUserClass(), getUserAdRqt.getLaunchPort(), getUserAdRqt.getAdPositionSymbol(), userId);
        String cacheValueStr = redisHelper.get(userFormatKey);
        if (!StringUtils.isEmpty(cacheValueStr)) {
            String decodeCacheValue = MathUtils.decompressFromString(cacheValueStr);
            getUserAdResp = JSON.parseObject(decodeCacheValue, GetUserAdResp.class);
            log.info("userId:{},通过拉取广告缓存获取广告", userId);
            return getUserAdResp;
        }
        try {
            //组装拉取广告入参
            PullNonPopAdReq pullNonPopAdReq = new PullNonPopAdReq();
            List<String> tagList = new ArrayList<>();
            try {
                tagList = this.getTagListFromBigData(getUserAdRqt.getUserClass(), userId, getUserAdRqt.getLaunchPort());
            } catch (Exception e) {
                log.error("getAdPosition调用大数据接口异常:" + e.getMessage());
            }
            AdBo build = AdBo.builder().port(getUserAdRqt.getLaunchPort())
                    .adPositionSymbol(getUserAdRqt.getAdPositionSymbol())
                    .userClass(getUserAdRqt.getUserClass())
                    .userId(userId)
                    .tagList(tagList).build();
            BeanUtils.copyProperties(build, pullNonPopAdReq);

            //调用接口返回list
            PullNonPopupResp pullNonPopupResp = adPullApi.pullNonPopupV2(pullNonPopAdReq);
            getUserAdResp.setAdStatus(pullNonPopupResp.getAdStatus());
            getUserAdResp.setAdSourceType(pullNonPopupResp.getAdSourceType());
            getUserAdResp.setPersonalizationAdMaxPullTime(pullNonPopupResp.getPersonalizationAdMaxPullTime());
            List<PullAdNonPopupResp> pullAdNonPopupRespList = pullNonPopupResp.getPullAdNonPopupRespList();
            //2025年6月4日14:24:18 【师傅】开屏广告热启动 添加出参
            if (AdConstant.AD_HOT_START_PARAM_AD_SYMBOL_LIST.contains(getUserAdRqt.getAdPositionSymbol())) {
                //列表广告排序
                getUserAdResp.setOtherParam(this.getOtherParam(getUserAdRqt.getAdPositionSymbol()));
            }
            if (CollectionUtils.isEmpty(pullAdNonPopupRespList)) {
//                //增加缓存
//                if (redisCacheTime > 0) {
//                    String encodeCache = MathUtils.compressToString(JSON.toJSONString(getUserAdResp));
//                    redisHelper.set(userFormatKey, encodeCache, redisCacheTime);
//                }
                getUserAdResp.setAdInfo(new ArrayList<>());
                return getUserAdResp;
            }

            List<PullAdNonPopupResp> pullAdNonPopupResps = new ArrayList<>();
            //选中的aid list
            List<Long> selectedAidList = new ArrayList<>();
            //选中的aid list
            List<ActivityBaseResp> activityBaseList = new ArrayList<>();
            //过滤广告
            this.getAdPositionFilterAdId(pullAdNonPopupRespList, selectedAidList, pullAdNonPopupResps, userId, getUserAdRqt.getUserClass(), activityBaseList, getUserAdRqt.getAdPositionSymbol());
            //截取最大展示个数
            if (!CollectionUtils.isEmpty(pullAdNonPopupResps)) {
                int maxShowNum = pullAdNonPopupRespList.get(0).getMaxShowNum();
                if (pullAdNonPopupResps.size() >= maxShowNum) {
                    pullAdNonPopupResps = pullAdNonPopupResps.subList(0, maxShowNum);
                    selectedAidList = selectedAidList.subList(0, maxShowNum);
                }
            }

            /**
             * 组装出参信息:广告信息+活动信息+图片信息
             */
            List<GetUserAdResp.AdInfo> getUserAdRespList = this.packageAdPositionOutput(pullAdNonPopupResps, userId, activityBaseList);
            if (getUserAdRqt.getAdPositionSymbol().equals("activity_center_ad_list")) {
                //列表广告排序
                getUserAdRespList = this.listAdSort(getUserAdRespList);
            }

            getUserAdResp.setAdInfo(getUserAdRespList);
            //增加缓存
            if (CollectionUtils.isNotEmpty(getUserAdRespList) && redisCacheTime > 0) {
                String encodeCache = MathUtils.compressToString(JSON.toJSONString(getUserAdResp));
                redisHelper.set(userFormatKey, encodeCache, redisCacheTime);
            }
            //选中了哪几个需要回调给平台
            if (selectedAidList.size() > 0) {
                this.backAidList(selectedAidList, userId);
            }
            log.info("userId:{},通过调用平台层获取广告", userId);
            return getUserAdResp;
        } catch (ApiAccessException e) {
            log.warn("拉取广告失败,请求参数={},错误信息={}", JSON.toJSONString(getUserAdRqt), e.getMessage());
            return null;
        }
    }

    private String getOtherParam(String adPositionSymbol) {
        if (AdConstant.AD_HOT_START_PARAM_AD_SYMBOL_LIST.contains(adPositionSymbol)) {
            if ("master_app_open_screen".equals(adPositionSymbol)) {
                return adHotStartParam;
            }
        }
        return null;
    }

    /**
     * 如果是列表广告增加排序，时间大的排前面
     */
    private List<GetUserAdResp.AdInfo> listAdSort(List<GetUserAdResp.AdInfo> rqt) {

        rqt.sort(new Comparator<GetUserAdResp.AdInfo>() {
            @Override
            public int compare(GetUserAdResp.AdInfo u1, GetUserAdResp.AdInfo u2) {

                if (u1.getAdStartTime().after(u2.getAdStartTime())) {
                    //return -1:即为正序排序
                    return -1;
                } else if (u1.getAdStartTime().equals(u2.getAdStartTime())) {
                    return 0;
                } else {
                    //return 1: 即为倒序排序
                    return 1;
                }
            }
        });
        return rqt;

    }


    /**
     * 过滤广告：活动广告要求：已下发且为到达上限
     * 选取的广告数不能超过最大值
     *
     * @param pullAdNonPopupRespList
     * @param selectedAidList
     * @param pullAdNonPopupResps
     * @param userId
     * @param userClass
     */
    public void getAdPositionFilterAdId(List<PullAdNonPopupResp> pullAdNonPopupRespList, List<Long> selectedAidList, List<PullAdNonPopupResp> pullAdNonPopupResps, Long userId, String userClass, List<ActivityBaseResp> selectedActivityBaseList, String adPositionSymbol) {
        List<Long> checkedActivityList = null;
        //增加排序
        if (CollectionUtils.isNotEmpty(pullAdNonPopupRespList)) {
            pullAdNonPopupRespList = pullAdNonPopupRespList.stream().sorted(Comparator.comparing(PullAdNonPopupResp::getSortNumber)).collect(Collectors.toList());
            //批量查询是否已下发且未到达上限
            List<PullAdNonPopupBo> pullAdNonPopupBos = pullAdNonPopupRespList.stream().map(m -> PullAdNonPopupBo.builder()
                    .adId(m.getAdBaseInfo().getAdId())
                    .activityId(m.getAdBaseInfo().getActivityId())
                    .adPositionSymbol(m.getAdBaseInfo().getAdPositionSymbol())
                    .isHelpTime(m.getAdBaseInfo().getIsHelpTime())
                    .isTodayPull(m.getAdBaseInfo().getIsTodayPull())
                    .build()).collect(Collectors.toList());
            List<Long> activityIdList = pullAdNonPopupRespList.stream().filter(f -> f.getAdBaseInfo() != null && f.getAdBaseInfo().getActivityId() != 0).map(m -> m.getAdBaseInfo().getActivityId()).collect(Collectors.toList());
//            checkedActivityList = this.hasSignUpAndNotUpperLimit(activityIdList, userId, userClass);
            List<ActivityBaseResp> activityBaseList = this.signUpAndNotUpperLimitActivityDetail(activityIdList, userId, userClass, adPositionSymbol, false, pullAdNonPopupBos);
            if (CollectionUtils.isNotEmpty(activityBaseList)) {
                checkedActivityList = activityBaseList.stream().map(ActivityBaseResp::getActivityId).collect(Collectors.toList());
                selectedActivityBaseList.addAll(activityBaseList);
            }
        }
        //检验广告是否已经下发给用户
        for (PullAdNonPopupResp pullAdNonPopupResp : pullAdNonPopupRespList) {
            if (pullAdNonPopupResp.getAdBaseInfo() != null) {

                if (pullAdNonPopupResp.getAdBaseInfo().getActivityId() != 0) {
                    //如果这个活动没下发或者达到领奖上限就跳过循环
                    if (CollectionUtils.isNotEmpty(checkedActivityList) && checkedActivityList.contains(pullAdNonPopupResp.getAdBaseInfo().getActivityId())) {
                        pullAdNonPopupResps.add(pullAdNonPopupResp);
                        selectedAidList.add(pullAdNonPopupResp.getAdBaseInfo().getAdId());
                    }
                } else {
                    pullAdNonPopupResps.add(pullAdNonPopupResp);
                    selectedAidList.add(pullAdNonPopupResp.getAdBaseInfo().getAdId());
                }
            }
        }
    }

    /**
     * 过滤家庭广告：活动广告要求：已下发且未到达上限;活动广告对应活动未过报名结束时间
     * 选取的广告数不能超过最大值
     *
     * @param pullAdNonPopupRespList
     * @param selectedAidList
     * @param pullAdNonPopupResps
     * @param userId
     * @param userClass
     */
    public void filterCustomerAd(List<PullAdNonPopupResp> pullAdNonPopupRespList, List<Long> selectedAidList, List<PullAdNonPopupResp> pullAdNonPopupResps, Long userId, String userClass, List<ActivityBaseResp> selectedActivityBaseList) {
        List<Long> checkedActivityList = null;
        if (CollectionUtils.isNotEmpty(pullAdNonPopupRespList)) {
            pullAdNonPopupRespList = pullAdNonPopupRespList.stream().sorted(Comparator.comparing(PullAdNonPopupResp::getSortNumber)).collect(Collectors.toList());
            //批量查询是否已下发且未到达上限
            List<Long> activityIdList = pullAdNonPopupRespList.stream().filter(f -> f.getAdBaseInfo() != null && f.getAdBaseInfo().getActivityId() != 0).map(m -> m.getAdBaseInfo().getActivityId()).collect(Collectors.toList());
//            checkedActivityList = this.hasSignUpAndNotUpperLimit(activityIdList, userId, userClass);
            if (CollectionUtils.isNotEmpty(activityIdList)) {
                List<ActivityBaseResp> activityBaseList = this.signUpAndNotUpperLimitActivityDetail(activityIdList, userId, userClass, "", false,
                        null);
                if (CollectionUtils.isNotEmpty(activityBaseList)) {
                    //过滤已过报名结束时间的活动
                    List<ActivityBaseResp> activityList = activityBaseList.stream().filter(f -> {
                        return Objects.nonNull(f.getApplyEndTime()) ? f.getApplyEndTime().after(new Date()) : f.getLaunchEndTime().after(new Date());
                    }).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(activityList)) {
                        checkedActivityList = activityList.stream().map(ActivityBaseResp::getActivityId).collect(Collectors.toList());
                        selectedActivityBaseList.addAll(activityList);
                    }
                }
            }

        }
        //检验广告是否已经下发给用户
        for (PullAdNonPopupResp pullAdNonPopupResp : pullAdNonPopupRespList) {
            if (pullAdNonPopupResp.getAdBaseInfo() != null) {

                if (pullAdNonPopupResp.getAdBaseInfo().getActivityId() != 0) {
                    //如果这个活动没下发或者达到领奖上限就跳过循环
                    if (CollectionUtils.isNotEmpty(checkedActivityList) && checkedActivityList.contains(pullAdNonPopupResp.getAdBaseInfo().getActivityId())) {
                        pullAdNonPopupResps.add(pullAdNonPopupResp);
                        selectedAidList.add(pullAdNonPopupResp.getAdBaseInfo().getAdId());
                    }
                } else {
                    pullAdNonPopupResps.add(pullAdNonPopupResp);
                    selectedAidList.add(pullAdNonPopupResp.getAdBaseInfo().getAdId());
                }
            }
        }
    }

    /**
     * 获取会员权益信息
     *
     * @param userId
     */
    public GetVipEquityInfoBo getVipEquityInfo(Long userId) {
//        if(CollectionUtils.isNotEmpty(vipAdList)){
//            return;
//        }
        GetVipEquityInfoBo resp = new GetVipEquityInfoBo();
        //获取用户会员数据
        GetVipAndCouponStatusForIocRequest request = new GetVipAndCouponStatusForIocRequest();
        request.setUserId(userId);
        GetVipAndCouponStatusForIocResp couponStatusResp = tocVipCouponApi.getUserVipAndCouponStatusForIoc(request);
        Boolean isVip = couponStatusResp.getIsVip();
        //是会员
        if (isVip) {

            Integer comboType = couponStatusResp.getComboType();
            //首月
            if (couponStatusResp.getIsFirstMonth()) {
                //已领
                if (couponStatusResp.getFirstMonthCouponReceived()) {
                    Integer firstMonthPackId = couponStatusResp.getFirstMonthPackId();
                    resp.setExtraType(EquityRewardExtraTypeEnum.CUSTOMER_VOUCHER_PACK.type);
                    resp.setExtraId(String.valueOf(firstMonthPackId));
                    resp.setVipQualificationState(1);
                    resp.setCouponReceivedState(1);
                    resp.setReceiveTime(couponStatusResp.getReceiveTime());
                    return resp;
                } else {
                    //未领
                    return this.getVipEquityInfoBo(userId, UserClassEnum.CUSTOMER.type, this.convertVipType(comboType, userId), EquityRewardTypeEnum.FIRST_MONTH.type, false, isVip);
                }
            }
            //次月
            //已领
            if (couponStatusResp.getSecondMonthCouponReceived()) {
                Integer secondMonthPackId = couponStatusResp.getSecondMonthPackId();
                resp.setExtraType(EquityRewardExtraTypeEnum.CUSTOMER_VOUCHER_PACK.type);
                resp.setExtraId(String.valueOf(secondMonthPackId));
                resp.setVipQualificationState(1);
                resp.setCouponReceivedState(1);
                resp.setReceiveTime(couponStatusResp.getReceiveTime());
                return resp;
            } else {
                //未领
                return this.getVipEquityInfoBo(userId, UserClassEnum.CUSTOMER.type, this.convertVipType(comboType, userId), EquityRewardTypeEnum.NEXT_MONTH.type, false, isVip);
            }
        } else {
            //非会员
            return this.getVipEquityInfoBo(userId, UserClassEnum.CUSTOMER.type, VipTypeEnum.COMMON.type, EquityRewardTypeEnum.FIRST_MONTH.type, false, isVip);
        }
    }

    /**
     * 过滤家庭广告：活动广告要求：已下发且未到达上限;活动广告对应活动未过报名结束时间
     * 选取的广告数不能超过最大值
     *
     * @param pullAdNonPopupRespList
     * @param userId
     */
    public void filterNewCustomerAd(List<NewAdPositionAdResp> pullAdNonPopupRespList, List<NewAdPositionAdResp> suitAdNonPopupRespList, Long userId, List<CustomerActivityBaseBo> selectedActivityBaseList, List<String> supportTypeList, VipEquityInfoBo vipEquityInfoBo) {
        pullAdNonPopupRespList = pullAdNonPopupRespList.stream().filter(f -> Objects.nonNull(f.getAdBaseInfo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pullAdNonPopupRespList)) {
            return;
        }
        //是否支持会员广告
        boolean isSupportVipAd = Objects.nonNull(userId) && CollectionUtils.isNotEmpty(supportTypeList) && supportTypeList.contains(SupportTypeEnum.VIP_EQUITY.type);

        pullAdNonPopupRespList = pullAdNonPopupRespList.stream().sorted(Comparator.comparing(NewAdPositionAdResp::getSortNumber)).collect(Collectors.toList());
        List<NewAdPositionAdResp> vipAdList = null;
        if (!isSupportVipAd) {
            //未登录或不支持会员广告的版本去掉所有会员广告
            pullAdNonPopupRespList = pullAdNonPopupRespList.stream().filter(r -> CollectionUtils.isEmpty(r.getAdBaseInfo().getAdAttributeTypeList()) || !r.getAdBaseInfo().getAdAttributeTypeList().contains(AdAttributeTypeEnum.VIP_EQUITY_TYPE.type)).collect(Collectors.toList());
        } else {
            vipAdList = pullAdNonPopupRespList.stream().filter(f -> this.isVipAd(f)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(pullAdNonPopupRespList)) {
            return;
        }

        //批量查询是否已下发且未到达上限
        List<NewAdPositionAdResp> activityNonPopupRespList = pullAdNonPopupRespList.stream().filter(f -> f.getAdBaseInfo().getActivityId() != 0).collect(Collectors.toList());
        //没有活动广告和会员广告，不走后续流程
        if (CollectionUtils.isEmpty(activityNonPopupRespList) && CollectionUtils.isEmpty(vipAdList)) {
            suitAdNonPopupRespList.addAll(pullAdNonPopupRespList);
            return;
        }
        //通过过滤的活动
        List<CustomerActivityBaseResp> activityBaseList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityNonPopupRespList)) {
            //调用活动接口过滤不符合的活动广告信息（达到上限，或者登录态下不在投放人群中或者没下发）
            List<GetCustomerSuitActivityDetailRqt.ActivityLimitInfo> activityLimitInfoList = new ArrayList<>();
            activityNonPopupRespList.forEach(activityNonPopupResp -> {
                GetCustomerSuitActivityDetailRqt.ActivityLimitInfo activityLimitInfo = new GetCustomerSuitActivityDetailRqt.ActivityLimitInfo();
                activityLimitInfo.setIsFilterRewardMaxUpperLimit(1);
                activityLimitInfo.setIsFilterBudgetUpperLimit(1);
                activityLimitInfo.setActivityId(activityNonPopupResp.getAdBaseInfo().getActivityId());
                activityLimitInfoList.add(activityLimitInfo);
            });
            activityBaseList = this.customerSignUpAndNotUpperLimitActivityDetail(activityLimitInfoList, userId);
            //没有通过的活动和会员广告直接返回普通广告
            if (CollectionUtils.isEmpty(activityBaseList) && CollectionUtils.isEmpty(vipAdList)) {
                suitAdNonPopupRespList.addAll(pullAdNonPopupRespList.stream().filter(f -> f.getAdBaseInfo().getActivityId() == 0).collect(Collectors.toList()));
                return;
            }
            if (CollectionUtils.isEmpty(activityBaseList)) {
                activityBaseList = new ArrayList<>();
            }
        }


        //家庭首页领券广告位需查询优惠券信息
        List<Long> queryVoucherActivityIdList = pullAdNonPopupRespList.stream().filter(f -> f.getAdBaseInfo().getActivityId() > 0 &&
                        AdConstant.CUSTOMER_RECEIVE_VOUCHER_AD_SYMBOL_LIST.contains(f.getAdPositionSymbol()))
                .map(f -> f.getAdBaseInfo().getActivityId()).distinct().collect(Collectors.toList());
        List<Long> customerVoucherIdList = new ArrayList<>();
        List<Long> customerVoucherPackIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityBaseList)) {
            customerVoucherIdList = activityBaseList.stream().filter(f -> queryVoucherActivityIdList.contains(f.getActivityId()) && RewardSymbol.CUSTOMER_VOUCHER.type.equals(f.getRewardType()) && f.getRewardExtraId() > 0).map(m -> m.getRewardExtraId()).distinct().collect(Collectors.toList());
            customerVoucherPackIdList = activityBaseList.stream().filter(f -> queryVoucherActivityIdList.contains(f.getActivityId()) && RewardSymbol.CUSTOMER_VOUCHER_PACK.type.equals(f.getRewardType()) && f.getRewardExtraId() > 0).map(m -> m.getRewardExtraId()).distinct().collect(Collectors.toList());
        }
        GetVipEquityInfoBo vipEquityInfo = null;
        if (CollectionUtils.isNotEmpty(vipAdList)) {
            vipEquityInfo = this.getVipEquityInfo(userId);
            if (Objects.nonNull(vipEquityInfo)) {
                if (EquityRewardExtraTypeEnum.CUSTOMER_VOUCHER_PACK.type.equals(vipEquityInfo.getExtraType())) {
                    customerVoucherPackIdList.add(Long.valueOf(vipEquityInfo.getExtraId()));
                } else if (EquityRewardExtraTypeEnum.CUSTOMER_VOUCHER.type.equals(vipEquityInfo.getExtraType())) {
                    customerVoucherIdList.add(Long.valueOf(vipEquityInfo.getExtraId()));
                }
            }
        }
        CustomerVoucherOrPackRespBo customerVoucherOrPackRespBo = this.queryVoucherAndPackInfo(customerVoucherIdList, customerVoucherPackIdList);
        boolean hasVipAd = false;
        for (NewAdPositionAdResp respVo : pullAdNonPopupRespList) {
            //普通广告
            if (respVo.getAdBaseInfo().getActivityId() < 1) {
                if (AdConstant.CUSTOMER_RECEIVE_VOUCHER_AD_SYMBOL_LIST.contains(respVo.getAdPositionSymbol())) {
//                    log.error("首页领券广告活动id为空，请联系管理员处理，adId:{}", respVo.getAdBaseInfo().getAdId());
                    //是否是会员广告
                    if (!this.isVipAd(respVo)) {
                        continue;
                    }
                    if (Objects.isNull(vipEquityInfo)) {
                        continue;
                    }
                    if (hasVipAd) {
                        continue;
                    }
                    BeanCopyUtil.copyProperties(vipEquityInfo, vipEquityInfoBo);
                    List<RewardDetailInfoBo> rewardInfoList = this.buildRewardInfoList(customerVoucherOrPackRespBo, vipEquityInfo.getExtraType(), Long.valueOf(vipEquityInfo.getExtraId()), vipEquityInfo.getCouponReceivedState(), vipEquityInfo.getReceiveTime());
                    if (CollectionUtils.isEmpty(rewardInfoList)) {
                        //一个可用券都没有,不展示
                        continue;
                    }
                    //优惠券排序
                    vipEquityInfoBo.setRewardInfo(this.sortRewardInfoList(rewardInfoList));
//                    suitAdNonPopupRespList.add(respVo);
                    //会员广告目前只展示一个
                    hasVipAd = true;
//                    selectedActivityBaseList.add(customerActivityBaseBo);
                }
                suitAdNonPopupRespList.add(respVo);
                continue;
            }
            //活动广告处理
            Optional<CustomerActivityBaseResp> activityBaseRespOptional = activityBaseList.stream().filter(f -> f.getActivityId().equals(respVo.getAdBaseInfo().getActivityId())).findFirst();
            if (!activityBaseRespOptional.isPresent()) {
                continue;
            }
            CustomerActivityBaseResp activityBaseResp = activityBaseRespOptional.get();
            CustomerActivityBaseBo customerActivityBaseBo = new CustomerActivityBaseBo();
            if (!AdConstant.CUSTOMER_RECEIVE_VOUCHER_AD_SYMBOL_LIST.contains(respVo.getAdPositionSymbol())) {
                BeanUtils.copyProperties(activityBaseResp, customerActivityBaseBo);
                suitAdNonPopupRespList.add(respVo);
                selectedActivityBaseList.add(customerActivityBaseBo);
                continue;
            }
            /**
             * 首页领券广告位特殊处理：
             * 已领取的优惠券模板，需按照模板配置的使用时间比对领奖时间判断是否已过期；
             * 如优惠券配置指定时间使用，则当前时间必须在指定时间时，才返回该优惠券模板ID；
             * 如优惠券配置派发后xx天使用，则（当前时间-活动奖励领取时间）>=优惠券设置的派发后使用时间时，才返回该优惠券模板ID；
             */
            if (Objects.isNull(activityBaseResp.getRewardExtraId()) || activityBaseResp.getRewardExtraId() < 1) {
                log.error("首页领券广告优惠券信息为空，请联系管理员处理，adId:{},activityId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId());
                continue;
            }
            if (Objects.isNull(customerVoucherOrPackRespBo)) {
                log.error("查询优惠券信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
                continue;
            }
            Map<Long, CustomerVoucherOrPackRespBo.VoucherInfoBo> voucherInfoMap = customerVoucherOrPackRespBo.getVoucherInfoMap();
            Map<Long, List<CustomerVoucherOrPackRespBo.PackComponentBo>> packInfoMap = customerVoucherOrPackRespBo.getPackInfoMap();
            List<RewardDetailInfoBo> rewardInfoList = new ArrayList<>();
            //优惠券
            if (RewardSymbol.CUSTOMER_VOUCHER.type.equals(activityBaseResp.getRewardType())) {
                CustomerVoucherOrPackRespBo.VoucherInfoBo voucherInfoBo = voucherInfoMap.get(activityBaseResp.getRewardExtraId());
                if (Objects.isNull(voucherInfoBo)) {
                    log.error("查询优惠券信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
                    continue;
                }
                if (activityBaseResp.getRewardGiveState() == 1) {
                    Date userEndTime = voucherInfoBo.getTermStatus() == 1 ? voucherInfoBo.getUseEndTime() : DateUtils.addDays(activityBaseResp.getRewardGiveTime(), voucherInfoBo.getExpireTimeNum());
                    if (userEndTime.compareTo(new Date()) <= 0) {
                        continue;
                    }
                }
                rewardInfoList.addAll(this.buildRewardInfoBo(voucherInfoBo, 1));
            }
            //券包
            if (RewardSymbol.CUSTOMER_VOUCHER_PACK.type.equals(activityBaseResp.getRewardType())) {
                List<CustomerVoucherOrPackRespBo.PackComponentBo> componentBoList = packInfoMap.get(activityBaseResp.getRewardExtraId());
                if (CollectionUtils.isEmpty(componentBoList)) {
                    log.error("查询券包信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
                    continue;
                }
                componentBoList.forEach(componentBo -> {
                    CustomerVoucherOrPackRespBo.VoucherInfoBo voucherInfoBo = voucherInfoMap.get(componentBo.getSubjectId());
                    if (Objects.isNull(voucherInfoBo)) {
                        log.error("查询优惠券信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
                        return;
                    }
                    if (Objects.nonNull(activityBaseResp.getRewardGiveState()) && activityBaseResp.getRewardGiveState() == 1) {
                        Date userEndTime = voucherInfoBo.getTermStatus() == 1 ? voucherInfoBo.getUseEndTime() : DateUtils.addDays(activityBaseResp.getRewardGiveTime(), voucherInfoBo.getExpireTimeNum());
                        if (userEndTime.compareTo(new Date()) <= 0) {
                            return;
                        }
                    }
                    rewardInfoList.addAll(this.buildRewardInfoBo(voucherInfoBo, componentBo.getQuantity()));
                });
            }
            //没有优惠券奖励信息，这过滤掉该广告
            if (CollectionUtils.isEmpty(rewardInfoList)) {
                continue;
            }
            //设置优惠券信息
            BeanUtils.copyProperties(activityBaseResp, customerActivityBaseBo);
            //优惠券排序
            customerActivityBaseBo.setRewardInfo(this.sortRewardInfoList(rewardInfoList));
            suitAdNonPopupRespList.add(respVo);
            selectedActivityBaseList.add(customerActivityBaseBo);
        }

    }

    private List<RewardDetailInfoBo> buildRewardInfoList(CustomerVoucherOrPackRespBo customerVoucherOrPackRespBo, String extraType, Long extraId, Integer rewardGiveState, Date receiveTime) {
        List<RewardDetailInfoBo> rewardInfoList = new ArrayList<>();
        /**
         * 首页领券广告位特殊处理：
         * 已领取的优惠券模板，需按照模板配置的使用时间比对领奖时间判断是否已过期；
         * 如优惠券配置指定时间使用，则当前时间必须在指定时间时，才返回该优惠券模板ID；
         * 如优惠券配置派发后xx天使用，则（当前时间-活动奖励领取时间）>=优惠券设置的派发后使用时间时，才返回该优惠券模板ID；
         */
        if (Objects.isNull(extraId) || extraId < 1) {
//            log.error("首页领券广告优惠券信息为空，请联系管理员处理，adId:{},activityId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId());
            return rewardInfoList;
        }
        if (Objects.isNull(customerVoucherOrPackRespBo)) {
//            log.error("查询优惠券信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
            return rewardInfoList;
        }
        Map<Long, CustomerVoucherOrPackRespBo.VoucherInfoBo> voucherInfoMap = customerVoucherOrPackRespBo.getVoucherInfoMap();
        Map<Long, List<CustomerVoucherOrPackRespBo.PackComponentBo>> packInfoMap = customerVoucherOrPackRespBo.getPackInfoMap();
        //优惠券
        if (RewardSymbol.CUSTOMER_VOUCHER.type.equals(extraType)) {
            CustomerVoucherOrPackRespBo.VoucherInfoBo voucherInfoBo = voucherInfoMap.get(extraId);
            if (Objects.isNull(voucherInfoBo)) {
//                log.error("查询优惠券信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
                return rewardInfoList;
            }
            if (rewardGiveState == 1) {
                Date userEndTime = voucherInfoBo.getTermStatus() == 1 ? voucherInfoBo.getUseEndTime() : DateUtils.addDays(receiveTime, voucherInfoBo.getExpireTimeNum());
                if (userEndTime.compareTo(new Date()) <= 0) {
                    return rewardInfoList;
                }
            }
            rewardInfoList.addAll(this.buildRewardInfoBo(voucherInfoBo, 1));
        }
        //券包
        if (RewardSymbol.CUSTOMER_VOUCHER_PACK.type.equals(extraType)) {
            List<CustomerVoucherOrPackRespBo.PackComponentBo> componentBoList = packInfoMap.get(extraId);
            if (CollectionUtils.isEmpty(componentBoList)) {
//                log.error("查询券包信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
                return rewardInfoList;
            }
            for (CustomerVoucherOrPackRespBo.PackComponentBo componentBo : componentBoList) {
                CustomerVoucherOrPackRespBo.VoucherInfoBo voucherInfoBo = voucherInfoMap.get(componentBo.getSubjectId());
                if (Objects.isNull(voucherInfoBo)) {
//                    log.error("查询优惠券信息为空，请联系管理员处理，adId:{},activityId:{}，rewardExtraId:{}", respVo.getAdBaseInfo().getAdId(), activityBaseResp.getActivityId(), activityBaseResp.getRewardExtraId());
                    continue;
                }
                if (Objects.nonNull(rewardGiveState) && rewardGiveState == 1) {
                    Date userEndTime = voucherInfoBo.getTermStatus() == 1 ? voucherInfoBo.getUseEndTime() : DateUtils.addDays(receiveTime, voucherInfoBo.getExpireTimeNum());
                    if (userEndTime.compareTo(new Date()) <= 0) {
                        continue;
                    }
                }
                rewardInfoList.addAll(this.buildRewardInfoBo(voucherInfoBo, componentBo.getQuantity()));
            }
        }
        return rewardInfoList;
    }

    /**
     * 优惠券展示排序：
     * a. 第1优先级：无使用门槛>有使用门槛；
     * 无使用门槛定义：如（优惠券门槛-优惠券面额）<1元；
     * 有使用门槛定义：如（优惠券门槛-优惠券面额）>=1元；
     * b. 第2优先级：按照面额从大到小的顺序，在页面从左至右进行展示；
     *
     * @param rewardInfoList
     * @return
     */
    private List<RewardDetailInfoBo> sortRewardInfoList(List<RewardDetailInfoBo> rewardInfoList) {
        List<RewardDetailInfoBo> sortedRewardInfoList = new ArrayList<>();
        //无门槛优惠券
        List<RewardDetailInfoBo> noThresholdRewardInfoList = rewardInfoList.stream().filter(f -> f.getMinTradeAmount().subtract(f.getRewardMaxValue()).compareTo(BigDecimal.ONE) < 0)
                .sorted(Comparator.comparing(RewardDetailInfoBo::getRewardMaxValue).reversed()).collect(Collectors.toList());
        //有门槛优惠券
        List<RewardDetailInfoBo> haveThresholdRewardInfoList = rewardInfoList.stream().filter(f -> f.getMinTradeAmount().subtract(f.getRewardMaxValue()).compareTo(BigDecimal.ONE) >= 0)
                .sorted(Comparator.comparing(RewardDetailInfoBo::getRewardMaxValue).reversed()).collect(Collectors.toList());
        sortedRewardInfoList.addAll(noThresholdRewardInfoList);
        sortedRewardInfoList.addAll(haveThresholdRewardInfoList);
        return sortedRewardInfoList;
    }

    /**
     * 相同券有多张-全部平铺开
     *
     * @param voucherInfoBo
     * @param rewardNum
     * @return
     */
    private List<RewardDetailInfoBo> buildRewardInfoBo(CustomerVoucherOrPackRespBo.VoucherInfoBo voucherInfoBo, Integer rewardNum) {
        List<RewardDetailInfoBo> rewardInfoBoList = new ArrayList<>();
        for (int i = 0; i < rewardNum; i++) {
            RewardDetailInfoBo rewardInfoBo = new RewardDetailInfoBo();
            rewardInfoBo.setRewardExtraId(voucherInfoBo.getSubjectId());
            rewardInfoBo.setRewardExtraName(voucherInfoBo.getTitle());
            rewardInfoBo.setRewardNum(1);
            rewardInfoBo.setRewardType(RewardSymbol.CUSTOMER_VOUCHER.type);
            rewardInfoBo.setRewardExtraType(voucherInfoBo.getVoucherType());
            rewardInfoBo.setMinTradeAmount(voucherInfoBo.getMinTradeAmount());
            rewardInfoBo.setDiscount(voucherInfoBo.getDiscountRatio());
            rewardInfoBo.setRewardMaxValue(voucherInfoBo.getVoucherMaxValue());
            rewardInfoBoList.add(rewardInfoBo);
        }
        return rewardInfoBoList;
    }

    /**
     * 批量查询家庭 优惠券以及券包信息
     *
     * @param customerVoucherIdList
     * @param customerVoucherPackIdList
     * @return
     */
    private CustomerVoucherOrPackRespBo queryVoucherAndPackInfo(List<Long> customerVoucherIdList, List<Long> customerVoucherPackIdList) {
        if (CollectionUtils.isEmpty(customerVoucherPackIdList) && CollectionUtils.isEmpty(customerVoucherIdList)) {
            return null;
        }
        CustomerVoucherOrPackRespBo respBo = new CustomerVoucherOrPackRespBo();
        Map<Long, CustomerVoucherOrPackRespBo.VoucherInfoBo> voucherInfoMap = new HashMap<>();
        Map<Long, List<CustomerVoucherOrPackRespBo.PackComponentBo>> packInfoMap = new HashMap<>();
        respBo.setPackInfoMap(packInfoMap);
        respBo.setVoucherInfoMap(voucherInfoMap);
        Set<Integer> queryVoucherIdSet = new HashSet<>();
        queryVoucherIdSet.addAll(customerVoucherIdList.stream().map(m -> m.intValue()).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(customerVoucherPackIdList)) {
            List<TocCouponPackComponent> tocCouponPackComponentList = tocCouponPackApi.batchGetPackComponentList(customerVoucherPackIdList.stream().map(m -> m.intValue()).collect(Collectors.toSet()));
            if (CollectionUtils.isNotEmpty(tocCouponPackComponentList)) {
                Map<Integer, List<TocCouponPackComponent>> packComponentMap = tocCouponPackComponentList.stream().collect(Collectors.groupingBy(t -> t.getPackId()));
                packComponentMap.forEach((voucherPackId, componentList) -> {
                    List<CustomerVoucherOrPackRespBo.PackComponentBo> componentBoList = new ArrayList<>();
                    for (TocCouponPackComponent component : componentList) {
                        CustomerVoucherOrPackRespBo.PackComponentBo componentBo = new CustomerVoucherOrPackRespBo.PackComponentBo();
                        componentBo.setPackId(voucherPackId.longValue());
                        componentBo.setQuantity(component.getQuantity());
                        componentBo.setSubjectId(component.getSubjectId().longValue());
                        queryVoucherIdSet.add(component.getSubjectId());
                        componentBoList.add(componentBo);
                    }
                    packInfoMap.put(voucherPackId.longValue(), componentBoList);
                });
            }
        }
        if (CollectionUtils.isEmpty(queryVoucherIdSet)) {
            return respBo;
        }
        //批量查询优惠券信息
        List<TocCouponSubject> couponSubjectList = tocCouponApi.selectCouponSubjectByIds(queryVoucherIdSet);
        Set<Integer> rangeSubjectIdSet = couponSubjectList.stream().filter(f -> f.getTermStatus() == 1).map(m -> m.getId()).collect(Collectors.toSet());
        //去扩展表查询优惠券使用时间范围
        List<TocCouponSubjectExt> subjectExtList = CollectionUtils.isNotEmpty(rangeSubjectIdSet) ? tocCouponApi.selectCouponSubjectExtBySubjects(rangeSubjectIdSet) : new ArrayList<>();
        couponSubjectList.forEach(subject -> {
            CustomerVoucherOrPackRespBo.VoucherInfoBo voucherInfoBo = new CustomerVoucherOrPackRespBo.VoucherInfoBo();
            voucherInfoBo.setSubjectId(subject.getId().longValue());
            voucherInfoBo.setTitle(subject.getTitle());
            voucherInfoBo.setVoucherType(FamilyVoucherTypeEnum.getTypeByCode(subject.getType()));
            voucherInfoBo.setVoucherMaxValue(FamilyVoucherTypeEnum.MONEY_OFF.code.equals(subject.getType()) ? subject.getDiscount() : subject.getMaxReductionAmount());
            //门槛可能为空，赋默认值0
            voucherInfoBo.setMinTradeAmount(Objects.nonNull(subject.getLimitation()) ? subject.getLimitation() : BigDecimal.ZERO);
            voucherInfoBo.setDiscountRatio(subject.getDiscountRatio());
            voucherInfoBo.setExpireTimeNum(subject.getExpires());
            //有效期为时间范围
            if (subject.getTermStatus() == 1) {
                Optional<TocCouponSubjectExt> tocCouponSubjectExtOptional = subjectExtList.stream().filter(f -> f.getSubject().equals(subject.getId())).findFirst();
                voucherInfoBo.setUseStartTime(tocCouponSubjectExtOptional.isPresent() ? tocCouponSubjectExtOptional.get().getUseStartTime() : null);
                voucherInfoBo.setUseEndTime(tocCouponSubjectExtOptional.isPresent() ? tocCouponSubjectExtOptional.get().getUseEndTime() : null);
            }
            voucherInfoBo.setTermStatus(subject.getTermStatus());
            voucherInfoMap.put(subject.getId().longValue(), voucherInfoBo);
        });
        return respBo;
    }

    /**
     * 批量获取用户符合规则的活动详情(已下发+未达到领奖上限)
     *
     * @param activityIdList
     * @param userId
     * @param userClass
     * @param pullAdNonPopupBos
     * @return
     */
    private List<ActivityBaseResp> signUpAndNotUpperLimitActivityDetail(List<Long> activityIdList, Long userId,
                                                                        String userClass, String adPositionSymbol, boolean isPopupAd,
                                                                        List<PullAdNonPopupBo> pullAdNonPopupBos) {
        if (CollectionUtils.isEmpty(activityIdList)) {
            return null;
        }
        GetSuitActivityDetailRqt getSuitActivityDetailRqt = new GetSuitActivityDetailRqt();
        getSuitActivityDetailRqt.setActivityIdList(activityIdList);
        getSuitActivityDetailRqt.setUserId(userId);
        //现只针对师傅端做达到上限处理，新增加商家充值活动也需要判断总上限
        getSuitActivityDetailRqt.setIsFilterUpperLimit(UserTypeEnum.MASTER.type.equals(userClass) ? 1 : 0);
        if (UserTypeEnum.MERCHANT.type.equals(userClass) && !StringUtils.isEmpty(adPositionSymbol)) {
            if (AdConstant.MERCHANT_CHECK_UPPER_LIMIT_POSITION_SYMBOLS.contains(adPositionSymbol)) {
                getSuitActivityDetailRqt.setIsFilterUpperLimit(1);
                getSuitActivityDetailRqt.setCheckTaskUpperLimit(1);
                getSuitActivityDetailRqt.setCheckSecurityFund(AdConstant.MERCHANT_CHECK_SECURITY_FUND_POSITION_SYMBOLS.contains(adPositionSymbol) ? 1 : 0);
            }
        }
        //活动弹窗广告达到上限不展示
        if (isPopupAd) {
            getSuitActivityDetailRqt.setIsFilterUpperLimit(1);
        }
        //师傅多状态特殊处理
        if (AdConstant.QUOTED_TOP_BANNER.equals(adPositionSymbol)) {
            if (CollectionUtils.isNotEmpty(pullAdNonPopupBos)) {
                //当天已拉取
                PullAdNonPopupBo pullAdNonPopupBo = pullAdNonPopupBos.get(0);
                if (1 == pullAdNonPopupBo.getIsTodayPull()) {
                    getSuitActivityDetailRqt.setActivityIdList(Arrays.asList(pullAdNonPopupBo.getActivityId()));
                    getSuitActivityDetailRqt.setIsFilterUpperLimit(0);
                    getSuitActivityDetailRqt.setCheckTaskUpperLimit(0);
                    getSuitActivityDetailRqt.setCheckActivityFinishTime(0);
                } else {
                    getSuitActivityDetailRqt.setCheckTaskUpperLimit(0);
                    getSuitActivityDetailRqt.setCheckActivityFinishTime(1);
                }
            }


        }
        return activityBusinessServiceApi.getUserSuitActivityDetailBatch(getSuitActivityDetailRqt);
    }

    /**
     * 批量获取用户符合规则的活动详情(已下发+未达到领奖上限)----批量拉取广告位广告接口专用
     *
     * @param pullAdNonPopupResps
     * @param userId
     * @param userClass
     * @return
     */
    private List<ActivityBaseResp> batchSignUpAndNotUpperLimitActivityDetail(List<PullAdNonPopupResp> pullAdNonPopupResps, Long userId, String userClass) {
        List<GetSuitActivityDetailForEachRqt.ActivityQueryConfigVo> activityQueryConfigVoList = new ArrayList<>();
        //同一个活动，假如一个广告需要处理，一个不需要处理，都以已需要处理为准
        Map<Long, List<PullAdNonPopupResp>> activityIdMap = pullAdNonPopupResps.stream().filter(f -> f.getAdBaseInfo().getActivityId() > 0).collect(Collectors.groupingBy(f -> f.getAdBaseInfo().getActivityId()));
        activityIdMap.forEach((activityId, pullAdNonPopupRespList) -> {
            if (CollectionUtils.isEmpty(pullAdNonPopupRespList)) {
                return;
            }
            GetSuitActivityDetailForEachRqt.ActivityQueryConfigVo reqVo = new GetSuitActivityDetailForEachRqt.ActivityQueryConfigVo();
            reqVo.setActivityId(activityId);
            Optional<PullAdNonPopupResp> finishTimeOptional = pullAdNonPopupRespList.stream().filter(f -> AdConstant.QUOTED_TOP_BANNER.equals(f.getAdBaseInfo().getAdPositionSymbol())).findFirst();
            reqVo.setCheckActivityFinishTime(finishTimeOptional.isPresent() ? 1 : 0);
            Optional<PullAdNonPopupResp> filterUpperLimitOptional = pullAdNonPopupRespList.stream().filter(f -> UserTypeEnum.MASTER.type.equals(userClass) && !AdConstant.QUOTED_TOP_BANNER.equals(f.getAdBaseInfo().getAdPositionSymbol())).findFirst();
            reqVo.setIsFilterUpperLimit(filterUpperLimitOptional.isPresent() ? 1 : 0);
            activityQueryConfigVoList.add(reqVo);
        });
        if (CollectionUtils.isEmpty(activityQueryConfigVoList)) {
            return new ArrayList<>();
        }
        GetSuitActivityDetailForEachRqt getSuitActivityDetailForEachRqt = new GetSuitActivityDetailForEachRqt();
        getSuitActivityDetailForEachRqt.setActivityQueryConfigVoList(activityQueryConfigVoList);
        getSuitActivityDetailForEachRqt.setUserId(userId);
        return activityBusinessServiceApi.getUserSuitActivityDetailForEach(getSuitActivityDetailForEachRqt);
    }

    /**
     * 批量获取家庭活动广告符合规则的活动详情(已下发+未达到领奖上限)
     *
     * @return
     */
    private List<CustomerActivityBaseResp> customerSignUpAndNotUpperLimitActivityDetail(List<GetCustomerSuitActivityDetailRqt.ActivityLimitInfo> activityLimitInfoList, Long userId) {
        if (CollectionUtils.isEmpty(activityLimitInfoList)) {
            return new ArrayList<>();
        }
        GetCustomerSuitActivityDetailRqt getSuitActivityDetailRqt = new GetCustomerSuitActivityDetailRqt();
        getSuitActivityDetailRqt.setActivityLimitInfoList(activityLimitInfoList);
        getSuitActivityDetailRqt.setUserId(userId);
        log.info("customerSignUpAndNotUpperLimitActivityDetail req:{}", JSON.toJSONString(getSuitActivityDetailRqt));
        List<CustomerActivityBaseResp> respList = activityBusinessServiceApi.batchGetCustomerSuitActivityDetail(getSuitActivityDetailRqt);
        if (CollectionUtils.isEmpty(respList)) {
            return new ArrayList<>();
        }
        return respList;
    }

    /**
     * 非登陆广告获取图片aid
     */
    public Map<Long, String> getNotLoginAdImgInfo(UnLoginPullAdResp unLoginPullAdResp) {
        //图片id 批量请求基础平台获取url
        List<String> aidList = new ArrayList<>();
        if (unLoginPullAdResp.getMeterialList() != null) {
            List<CommonMeterialResp> meterialList = unLoginPullAdResp.getMeterialList();
            if (CollectionUtils.isNotEmpty(meterialList)) {
                for (CommonMeterialResp commonMeterialResp : meterialList) {
                    if (!StringUtils.isEmpty(commonMeterialResp.getPictureId())) {
                        aidList.add(commonMeterialResp.getPictureId().toString());
                    }
                    if (!StringUtils.isEmpty(commonMeterialResp.getClickedPictureId())) {
                        aidList.add(commonMeterialResp.getClickedPictureId().toString());
                    }
                }
            }
        }


        Map<Long, String> imageResultMap = new HashMap<>();
        if (aidList.size() > 0) {
            imageResultMap = httpImageUtils.sendPostRequest(aidList);
        }
        return imageResultMap;
    }

    /**
     * 非登陆家庭广告获取图片aid
     */
    public Map<Long, String> getNotLoginCustomerAdImgInfo(List<PullAdNonPopupResp> pullAdNonPopupRespList) {
        //图片id 批量请求基础平台获取url
        List<String> aidList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pullAdNonPopupRespList)) {
            for (PullAdNonPopupResp pullAdNonPopupResp : pullAdNonPopupRespList) {
                this.buildAidList(pullAdNonPopupResp, aidList);
            }
        }

        Map<Long, String> imageResultMap = new HashMap<>();
        if (aidList.size() > 0) {
            imageResultMap = httpImageUtils.sendPostRequest(aidList);
        }
        return imageResultMap;
    }

    /**
     * 组装aidList
     *
     * @param pullAdNonPopupResp
     * @param aidList
     */
    public void buildAidList(PullAdNonPopupResp pullAdNonPopupResp, List<String> aidList) {
        List<CommonMeterialResp> meterialList = pullAdNonPopupResp.getMeterialList();
        if (CollectionUtils.isNotEmpty(meterialList)) {
            for (CommonMeterialResp commonMeterialResp : meterialList) {
                if (!StringUtils.isEmpty(commonMeterialResp.getPictureId())) {
                    aidList.add(commonMeterialResp.getPictureId().toString());
                }
                if (!StringUtils.isEmpty(commonMeterialResp.getClickedPictureId())) {
                    aidList.add(commonMeterialResp.getClickedPictureId().toString());
                }
            }
        }
    }

    /**
     * 非登录状态拉取广告
     *
     * @param getUserAdRqt
     * @param getUserAdResp
     * @return
     */
    public GetUserAdResp getUnLoginAdPosition(GetUserAdRqt getUserAdRqt, GetUserAdResp getUserAdResp) {
        List<GetUserAdResp.AdInfo> getUserAdRespList = new ArrayList<>();
        UnLoginPullAdReq unLoginPullAdReq = new UnLoginPullAdReq();
        unLoginPullAdReq.setAdPositionSymbol(getUserAdRqt.getAdPositionSymbol());
        unLoginPullAdReq.setPort(getUserAdRqt.getLaunchPort());
        unLoginPullAdReq.setUserClass(getUserAdRqt.getUserClass());

        try {

            UnLoginPullAdResp unLoginPullAdResp = adPullApi.unLoginpullAd(unLoginPullAdReq);

//            if (unLoginPullAdResp.getMeterialList() == null) {
//                return null;
//            }
            getUserAdResp.setAdSourceType(unLoginPullAdResp.getAdSourceType());
            getUserAdResp.setAdStatus(unLoginPullAdResp.getAdStatus());
            getUserAdResp.setPersonalizationAdMaxPullTime(unLoginPullAdResp.getPersonalizationAdMaxPullTime());
            //2025年6月4日14:24:18 【师傅】开屏广告热启动 添加出参
            if (AdConstant.AD_HOT_START_PARAM_AD_SYMBOL_LIST.contains(getUserAdRqt.getAdPositionSymbol())) {
                //列表广告排序
                getUserAdResp.setOtherParam(this.getOtherParam(getUserAdRqt.getAdPositionSymbol()));
            }
            if (unLoginPullAdResp.getMeterialList() == null) {
                getUserAdResp.setAdInfo(getUserAdRespList);
                return getUserAdResp;
            }

            AdBaseInfoResp adBaseInfo = unLoginPullAdResp.getAdBaseInfo();
            if (adBaseInfo == null) {
                getUserAdResp.setAdInfo(getUserAdRespList);
                return getUserAdResp;
            }

            Map<Long, String> imageResultMap = this.getNotLoginAdImgInfo(unLoginPullAdResp);

            GetUserAdResp.AdInfo adInfo = new GetUserAdResp.AdInfo();

            BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
            adInfo.setMaterialClass("picture");


            //定义list，返回给前端使用
            List<GetUserAdResp.Meterial> meterialList2 = new ArrayList<>();

            //接口返回list
            List<CommonMeterialResp> meterialList1 = unLoginPullAdResp.getMeterialList();

            for (CommonMeterialResp commonMeterialResp : meterialList1) {
                GetUserAdResp.Meterial meterial = new GetUserAdResp.Meterial();
                BeanUtils.copyProperties(commonMeterialResp, meterial);
                meterial.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
                meterial.setClickedPictureUrl(imageResultMap.get(commonMeterialResp.getClickedPictureId()));
                meterialList2.add(meterial);
                adInfo.setMaterialClass(commonMeterialResp.getMaterialClass());
            }
            adInfo.setMeterialList(meterialList2);

            GetUserAdResp.ActivityInfo activityInfo = new GetUserAdResp.ActivityInfo();

            //如果有活动信息
            if (adBaseInfo.getActivityId() != 0L) {
                ActivityDetailByIdRqt activityDetailByIdRqt = new ActivityDetailByIdRqt();
                activityDetailByIdRqt.setActivityId(adBaseInfo.getActivityId());
                ActivityBase activityDetailById = activityBusinessServiceApi.getActivityDetailById(activityDetailByIdRqt);
                BeanUtils.copyProperties(activityDetailById, activityInfo, "activityName", "activityDescription");
            }

            adInfo.setActivityInfo(activityInfo);
            getUserAdRespList.add(adInfo);
            getUserAdResp.setAdInfo(getUserAdRespList);
            return getUserAdResp;

        } catch (ApiAccessException e) {
            throw new BusException("get_not_login_ad_fail", "拉取广告失败");
        }
    }

    /**
     * 非登录状态拉取家庭广告位广告
     *
     * @param customerAdRqt
     * @param customerAdResp
     * @return
     */
    public CustomerAdResp listUnLoginCustomerAdPositionAd(CustomerAdRqt customerAdRqt, CustomerAdResp customerAdResp) {
        List<CustomerAdResp.AdInfo> getUserAdRespList = new ArrayList<>();
        UnLoginPullAdReq unLoginPullAdReq = new UnLoginPullAdReq();
        BeanUtils.copyProperties(customerAdRqt, unLoginPullAdReq);
        unLoginPullAdReq.setPort(customerAdRqt.getLaunchPort());

        try {

            List<PullAdNonPopupResp> pullAdNonPopupRespList = adPullApi.unLoginpullCustomerNonPopupAd(unLoginPullAdReq);

            if (CollectionUtils.isEmpty(pullAdNonPopupRespList)) {
                return customerAdResp;
            }
            //批量获取活动信息
//            List<Long> activityIdList = pullAdNonPopupRespList.stream().map(pullAdNonPopupResp -> {
//                return pullAdNonPopupResp.getAdBaseInfo().getActivityId();
//            }).collect(Collectors.toList());
//            ListActivityDetailByIdsRqt listActivityDetailByIdsRqt = new ListActivityDetailByIdsRqt();
//            listActivityDetailByIdsRqt.setActivityIdList(activityIdList);
//            List<ActivityBase> activityBaseList = activityBusinessServiceApi.listActivityDetailByIds(listActivityDetailByIdsRqt);
//            Map<Long, ActivityBase> activityBaseMap = activityBaseList.stream().collect(Collectors.toMap(ActivityBase::getActivityId, activityBase -> activityBase, (value1, value2) -> value1));
            //非登陆家庭广告获取图片aid
            Map<Long, String> imageResultMap = this.getNotLoginCustomerAdImgInfo(pullAdNonPopupRespList);

            for (PullAdNonPopupResp pullAdNonPopupResp : pullAdNonPopupRespList) {
                AdBaseInfoResp adBaseInfo = pullAdNonPopupResp.getAdBaseInfo();
                CustomerAdResp.AdInfo adInfo = new CustomerAdResp.AdInfo();

                BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
                adInfo.setMaterialClass("picture");

                //定义list，返回给前端使用
                List<CustomerAdResp.Meterial> meterialList2 = new ArrayList<>();

                //接口返回list
                List<CommonMeterialResp> meterialList1 = pullAdNonPopupResp.getMeterialList();

                for (CommonMeterialResp commonMeterialResp : meterialList1) {
                    CustomerAdResp.Meterial meterial = new CustomerAdResp.Meterial();
                    BeanUtils.copyProperties(commonMeterialResp, meterial);
                    meterial.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
                    meterial.setClickedPictureUrl(imageResultMap.get(commonMeterialResp.getClickedPictureId()));
                    meterial.setAdForwardUrlInfo(this.getAdForwardUrlInfo(commonMeterialResp.getAdForwardUrlBo()));
                    meterialList2.add(meterial);
                    adInfo.setMaterialClass(commonMeterialResp.getMaterialClass());
                }
                adInfo.setMeterialList(meterialList2);
//                CustomerAdResp.ActivityInfo activityInfo = new CustomerAdResp.ActivityInfo();
                //如果有活动信息
//                if (adBaseInfo.getActivityId() != 0L) {
////                    ActivityDetailByIdRqt activityDetailByIdRqt = new ActivityDetailByIdRqt();
////                    activityDetailByIdRqt.setActivityId(adBaseInfo.getActivityId());
////                    ActivityBase activityDetailById = activityBusinessServiceApi.getActivityDetailById(activityDetailByIdRqt);
//                    ActivityBase activityBase = activityBaseMap.get(adBaseInfo.getActivityId());
//                    if(Objects.nonNull(activityBase)){
//                        //过滤已过报名结束时间的广告
//                        if(activityBase.getApplyEndTime().before(new Date())){
//                            continue;
//                        }
//                        BeanUtils.copyProperties(activityBase, activityInfo, "activityName", "activityDescription");
//                    }
//                }
//                adInfo.setActivityInfo(activityInfo);
                getUserAdRespList.add(adInfo);
            }
            customerAdResp.setAdInfo(getUserAdRespList);

            return customerAdResp;
        } catch (ApiAccessException e) {
            log.error("get_not_login_customer_ad_positionad_fail", e);
            throw new BusException("get_not_login_customer_ad_positionad_ad_fail", "拉取广告失败");
        }
    }

    /**
     * 广告位广告用户获取特殊处理
     *
     * @param signature
     * @param userClass
     * @param token
     * @param tokenApp
     * @return
     */
    public Long getAdPositionUserId(String signature, String userClass, String token, String tokenApp) {
        Long userId;
        if (userClass.equals(UserTypeEnum.MERCHANT.type)) {
            String valueBykey = redisExUtil.getValueBykey(StringUtils.isEmpty(token) ? tokenApp : token, userClass);
            JSONObject userInfo = JSONObject.parseObject(valueBykey);
            UserInfoWebDtoResp userInfoWebDtoResp = JSONObject.toJavaObject(userInfo, UserInfoWebDtoResp.class);

            userId = userInfoWebDtoResp.getUserId();
        } else {
            //TODO 解析师傅登录信息；
            String valueBykey = redisExUtil.getValueBykey(signature, userClass);
            JSONObject userInfo = JSONObject.parseObject(valueBykey);
            MasterInfoDtoResp userInfoWebDtoResp = JSONObject.toJavaObject(userInfo, MasterInfoDtoResp.class);
            userId = userInfoWebDtoResp.getMasterId();
        }
        return userId;
    }

    /**
     * 调用大数据接口获取标签
     *
     * @param userClass
     * @param userId
     * @return
     */
    public List<String> getTagListFromBigData(String userClass, Long userId, String launchPort) {
        String personaId;
        switch (userClass) {
            case "merchant":
                personaId = "1";
                break;
            case "master":
                if (LaunchPortEnum.TOC_MASTER_APP.type.equals(launchPort)) {
                    //c端师傅
                    personaId = "4";
                } else {
                    //b端师傅
                    personaId = "2";
                }
                break;
            case "customer":
                personaId = "3";
                break;
            default:
                personaId = null;
                break;
        }

        List<Long> userGroupIds = super.getUserGroupIds(userId.toString(), personaId);

        return userGroupIds.stream().map(Object::toString).collect(Collectors.toList());
    }

    /**
     * 获取图片aid，活动id
     */
    public AdIdActivityIdRespBo getAdIdActivityIdList(List<PullAdNonPopupResp> pullAdNonPopupResps) {

        Map<Long, String> result = new HashMap<>();

        List<String> aidList = new ArrayList<>();

        List<Long> activityIdList = pullAdNonPopupResps.stream().filter(f -> f.getAdBaseInfo() != null && f.getAdBaseInfo().getActivityId() > 0).map(PullAdNonPopupResp::getAdBaseInfo).map(AdBaseInfoResp::getActivityId).collect(Collectors.toList());

        for (PullAdNonPopupResp pullAdNonPopupResp1 : pullAdNonPopupResps) {

            aidList.addAll(pullAdNonPopupResp1.getMeterialList().stream().filter(f -> f.getPictureId() != null).map(CommonMeterialResp::getPictureId).map(Object::toString).collect(Collectors.toList()));

            List<String> clickedAidList = pullAdNonPopupResp1.getMeterialList().stream().filter(f -> f.getClickedPictureId() != null).map(CommonMeterialResp::getClickedPictureId).map(Object::toString).collect(Collectors.toList());
            aidList.addAll(clickedAidList);
            //背景图片查询
            List<String> backendAidList = pullAdNonPopupResp1.getMeterialList().stream().filter(f -> f.getBackendPictureId() != null).map(CommonMeterialResp::getBackendPictureId).map(Object::toString).collect(Collectors.toList());
            aidList.addAll(backendAidList);
        }
        if (CollectionUtils.isNotEmpty(aidList)) {
            result = httpImageUtils.sendPostRequest(aidList);
        }
        return AdIdActivityIdRespBo.builder().activityIdList(activityIdList).aidList(aidList).maps(result).build();
    }

    /**
     * 获取图片aid，活动id
     */
    public Map<Long, String> getNewAdAidList(List<NewAdPositionAdResp> pullAdNonPopupResps) {
        Map<Long, String> result = new HashMap<>();
        List<String> aidList = new ArrayList<>();
        for (NewAdPositionAdResp pullAdNonPopupResp1 : pullAdNonPopupResps) {
            aidList.addAll(pullAdNonPopupResp1.getMeterialList().stream().filter(f -> f.getPictureId() != null).map(CommonMeterialResp::getPictureId).map(Object::toString).collect(Collectors.toList()));
            List<String> clickedAidList = pullAdNonPopupResp1.getMeterialList().stream().filter(f -> f.getClickedPictureId() != null).map(CommonMeterialResp::getClickedPictureId).map(Object::toString).collect(Collectors.toList());
            aidList.addAll(clickedAidList);
            //背景图片查询
            List<String> backendAidList = pullAdNonPopupResp1.getMeterialList().stream().filter(f -> f.getBackendPictureId() != null).map(CommonMeterialResp::getBackendPictureId).map(Object::toString).collect(Collectors.toList());
            aidList.addAll(backendAidList);
        }
        if (CollectionUtils.isNotEmpty(aidList)) {
            result = httpImageUtils.sendPostRequest(aidList);
        }
        return result;
    }

    /**
     * 获取活动信息列表
     */
    public List<ActivityBaseResp> getActivityBaseList(List<Long> activityIdList, Long userId) {
        List<ActivityBaseResp> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(activityIdList)) {
            return result;
        }
        try {
            GetActivityBaseListRqt getActivityBaseListRqt = new GetActivityBaseListRqt();
            getActivityBaseListRqt.setActivityIdList(activityIdList);
            getActivityBaseListRqt.setUserId(userId);
            return activityBusinessServiceApi.getActivityBaseList(getActivityBaseListRqt);
        } catch (Exception e) {
            throw new BusException("获取活动信息列表异常", e.getMessage());
        }
    }

    /**
     * icon广告处理
     */
    public List<GetUserAdResp.Meterial> getIconAd(List<CommonMeterialResp> meterialList, Map<Long, String> imageResultMap, GetUserAdResp.AdInfo adInfo) {

        List<GetUserAdResp.Meterial> meterial = new ArrayList<>();

        //icon廣告
        //先把icon第三个图标抽出来
        List<CommonMeterialResp> meterialRespLists = new ArrayList<>();
        CommonMeterialResp commonMeterialOrderResp = new CommonMeterialResp();
        for (CommonMeterialResp commonMeterialResp : meterialList) {
            adInfo.setMaterialClass(commonMeterialResp.getMaterialClass());
            if ("puborder_icon".equals(commonMeterialResp.getMaterialType())) {
                commonMeterialOrderResp = commonMeterialResp;
            } else {
                meterialRespLists.add(commonMeterialResp);
            }
        }

        for (int i = 0; i < meterialRespLists.size() / 2; i++) {
            GetUserAdResp.Meterial meterial1 = new GetUserAdResp.Meterial();
            CommonMeterialResp commonMeterialResp = meterialRespLists.get(i);
            meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));

            meterial1.setClickedPictureUrl(imageResultMap.get(meterialRespLists.get(i + meterialRespLists.size() / 2).getClickedPictureId()));
            meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
//            if (activityDetail != null && !StringUtils.isEmpty(activityDetail.getActivityUrl())) {
//                meterial1.setForwardUrl(activityDetail.getActivityUrl() + "&t=" + ("app".equals(isWeb) ? 1 : 2));
//            }
            //统一从广告服务获取地址
            meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());
            meterial1.setMaterialType(commonMeterialResp.getMaterialType());
            meterial1.setShowTime(commonMeterialResp.getShowTime());
            meterial.add(meterial1);
        }

        //发布订单icon（第三个icon）
        GetUserAdResp.Meterial meterialOrder = new GetUserAdResp.Meterial();
        BeanUtils.copyProperties(commonMeterialOrderResp, meterialOrder);
        meterialOrder.setPictureUrl(imageResultMap.get(commonMeterialOrderResp.getPictureId()));
        meterialOrder.setClickedPictureUrl(imageResultMap.get(commonMeterialOrderResp.getPictureId()));

        //此处2表示第三个位置保持不变，不能改
        meterial.add(2, meterialOrder);

        return meterial;
    }

    /**
     * 非弹窗广告组装
     */
    public List<GetUserAdResp.Meterial> getNoPopAd(List<CommonMeterialResp> meterialList, Map<Long, String> imageResultMap, GetUserAdResp.AdInfo adInfo) {

        List<GetUserAdResp.Meterial> meterial = new ArrayList<>();
        //其他廣告
        for (CommonMeterialResp commonMeterialResp : meterialList) {
            adInfo.setMaterialClass(commonMeterialResp.getMaterialClass());
            GetUserAdResp.Meterial meterial1 = new GetUserAdResp.Meterial();

            meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
            if (commonMeterialResp.getClickedPictureId() != null) {
                meterial1.setClickedPictureUrl(imageResultMap.get(commonMeterialResp.getClickedPictureId()));
            }
            if (commonMeterialResp.getBackendPictureId() != null) {
                meterial1.setBackendPictureUrl(imageResultMap.get(commonMeterialResp.getBackendPictureId()));
            }
            meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
            //forwardUrlType为3则表明是选择的是活动落地页，使用广告平台返回的地址
//            if (adBaseInfo.getActivityId() != null && adBaseInfo.getActivityId() != 0 && commonMeterialResp.getForwardUrlType() != 3) {
//                if (activityDetail != null && !StringUtils.isEmpty(activityDetail.getActivityUrl())) {
//                    meterial1.setForwardUrl(activityDetail.getActivityUrl() + "&t=" + ("app".equals(isWeb) ? 1 : 2));
//                }
//            } else {
//                meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());
//            }
            //统一从广告服务获取跳转地址
//            meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());
            //拼接广告id
            if (commonMeterialResp.getForwardUrl() != null) {
                if (commonMeterialResp.getForwardUrl().contains("http")) {
                    String url = commonMeterialResp.getForwardUrl().contains("?") ?
                            commonMeterialResp.getForwardUrl() + "&adId=" + adInfo.getAdId() :
                            commonMeterialResp.getForwardUrl() + "?adId=" + adInfo.getAdId();
                    meterial1.setForwardUrl(url);
                } else {
                    meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());
                }
            }

            meterial1.setMaterialType(commonMeterialResp.getMaterialType());
            meterial1.setShowTime(commonMeterialResp.getShowTime());
            meterial1.setMaterialClass(commonMeterialResp.getMaterialClass());
//            meterial1.setForwardUrlType(StringUtils.isEmpty(commonMeterialResp.getForwardPageCode()) ? 1 : 2);
            meterial1.setForwardUrlType(commonMeterialResp.getForwardUrlType());

            if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(commonMeterialResp.getMaterialDesc())) {
                meterial1.setMaterialDesc(commonMeterialResp.getMaterialDesc());
            }

            meterial.add(meterial1);
        }
        return meterial;
    }

    /**
     * 家庭非弹窗广告组装
     */
    public List<CustomerAdResp.Meterial> getCustomerNoPopAd(List<CommonMeterialResp> meterialList, Map<Long, String> imageResultMap, CustomerAdResp.AdInfo adInfo) {

        List<CustomerAdResp.Meterial> meterial = new ArrayList<>();
        //其他廣告
        for (CommonMeterialResp commonMeterialResp : meterialList) {
            adInfo.setMaterialClass(commonMeterialResp.getMaterialClass());
            CustomerAdResp.Meterial meterial1 = new CustomerAdResp.Meterial();

            meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
            if (commonMeterialResp.getClickedPictureId() != null) {
                meterial1.setClickedPictureUrl(imageResultMap.get(commonMeterialResp.getClickedPictureId()));
            }
            meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
            //forwardUrlType为3则表明是选择的是活动落地页，使用广告平台返回的地址
//            if (adBaseInfo.getActivityId() != null && adBaseInfo.getActivityId() != 0 && commonMeterialResp.getForwardUrlType() != 3) {
//                if (activityDetail != null && !StringUtils.isEmpty(activityDetail.getActivityUrl())) {
//                    meterial1.setForwardUrl(activityDetail.getActivityUrl() + "&t=" + ("app".equals(isWeb) ? 1 : 2));
//                }
//            } else {
//                meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());
//            }
            //统一从广告服务获取跳转地址
//            meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());
            //拼接广告id
            if (commonMeterialResp.getForwardUrl() != null) {
                if (commonMeterialResp.getForwardUrl().contains("http")) {
                    String url = commonMeterialResp.getForwardUrl().contains("?") ?
                            commonMeterialResp.getForwardUrl() + "&adId=" + adInfo.getAdId() :
                            commonMeterialResp.getForwardUrl() + "?adId=" + adInfo.getAdId();
                    meterial1.setForwardUrl(url);
                } else {
                    meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());
                }
            }

            meterial1.setMaterialType(commonMeterialResp.getMaterialType());
            meterial1.setIsLimitTime(commonMeterialResp.getIsLimitTime());
            meterial1.setShowTime(commonMeterialResp.getShowTime());
            meterial1.setMaterialClass(commonMeterialResp.getMaterialClass());
            meterial1.setForwardUrlType(commonMeterialResp.getForwardUrlType());

            if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(commonMeterialResp.getMaterialDesc())) {
                meterial1.setMaterialDesc(commonMeterialResp.getMaterialDesc());
            }

            meterial1.setAdForwardUrlInfo(this.getAdForwardUrlInfo(commonMeterialResp.getAdForwardUrlBo()));
            //赋值素材扩展信息
            if (Objects.nonNull(commonMeterialResp.getAdMaterialExtInfo())) {
                AdMaterialExtBo adMaterialExtInfo = commonMeterialResp.getAdMaterialExtInfo();
                if (Objects.nonNull(adMaterialExtInfo.getGoodsServiceInfo())) {
                    CustomerAdResp.GoodsServiceInfo goodsServiceInfo = new CustomerAdResp.GoodsServiceInfo();
                    List<CustomerAdResp.GoodsServicePicVo> goodsServicePicList = new ArrayList<>();
                    goodsServiceInfo.setShowType(adMaterialExtInfo.getGoodsServiceInfo().getShowType());
                    if (CollectionUtils.isNotEmpty(adMaterialExtInfo.getGoodsServiceInfo().getGoodsServicePicList())) {
                        adMaterialExtInfo.getGoodsServiceInfo().getGoodsServicePicList().forEach(picVo -> {
                            CustomerAdResp.GoodsServicePicVo servicePicVo = new CustomerAdResp.GoodsServicePicVo();
                            BeanUtils.copyProperties(picVo, servicePicVo);
                            goodsServicePicList.add(servicePicVo);
                        });
                    }
                    goodsServiceInfo.setGoodsServicePicList(goodsServicePicList);
                    meterial1.setGoodsServiceInfo(goodsServiceInfo);
                }
                meterial1.setPositionRegionHEX(adMaterialExtInfo.getPositionRegionHEX());
                meterial1.setSearchButtonHEX(adMaterialExtInfo.getSearchButtonHEX());
                if (Objects.nonNull(adMaterialExtInfo.getMaterialPictureUrlInfo())) {
                    CustomerAdResp.MaterialPictureUrlInfo materialPictureUrlInfo = new CustomerAdResp.MaterialPictureUrlInfo();
                    BeanUtils.copyProperties(adMaterialExtInfo.getMaterialPictureUrlInfo(), materialPictureUrlInfo);
                    meterial1.setMaterialPictureUrlInfo(materialPictureUrlInfo);
                }
            }
            meterial.add(meterial1);
        }
        return meterial;
    }


    /**
     * 组装广告位广告出参信息
     *
     * @param pullAdNonPopupResps
     * @param userId
     * @return
     */
    public List<GetUserAdResp.AdInfo> packageAdPositionOutput(List<PullAdNonPopupResp> pullAdNonPopupResps, Long userId, List<ActivityBaseResp> activityBaseList) {
        List<GetUserAdResp.AdInfo> getUserAdRespList = new ArrayList<>();

        //图片id 批量请求基础平台获取url
        AdIdActivityIdRespBo adIdActivityIdList = this.getAdIdActivityIdList(pullAdNonPopupResps);

//        List<Long> activityIdList = adIdActivityIdList.getActivityIdList();

        //过滤出图片aid,活动id
        Map<Long, String> imageResultMap = adIdActivityIdList.getMaps();

        //批量获取活动基本信息
//        List<ActivityBaseResp> activityBaseList = this.getActivityBaseList(activityIdList, userId);

        for (PullAdNonPopupResp pullAdNonPopupResp : pullAdNonPopupResps) {
            //返回的实体类中的子类
            GetUserAdResp.AdInfo adInfo = new GetUserAdResp.AdInfo();
            GetUserAdResp.ActivityInfo activityInfo = new GetUserAdResp.ActivityInfo();

            //广告基本信息
            AdBaseInfoResp adBaseInfo = pullAdNonPopupResp.getAdBaseInfo();

            ActivityBaseResp activityDetail;
            if (adBaseInfo.getActivityId() != 0) {
                Optional<ActivityBaseResp> baseResp = activityBaseList.stream().filter(base -> base.getActivityId().equals(adBaseInfo.getActivityId())).findFirst();
                if (!baseResp.isPresent()) {
                    continue;
                }
                activityDetail = baseResp.get();

                //活动基本信息
                if (activityDetail != null) {
                    BeanUtils.copyProperties(activityDetail, activityInfo, "activityName", "activityDescription");

                    adInfo.setIsHelpTime(adBaseInfo.getIsHelpTime());
                    //判断是否真的在保护期,商家多状态banner不取消保护期
                    if (adBaseInfo.getIsHelpTime() == 1) {
                        Integer isProtected = this.isProtected(activityDetail.getActivityEndTime(), adBaseInfo.getActivityId(), userId, adBaseInfo.getIsHelpTime(), adBaseInfo.getAdId(), adBaseInfo.getAdPositionSymbol());
                        adInfo.setIsHelpTime(isProtected);
                    }
                }
            }
            activityInfo.setActivityTitle(adBaseInfo.getActivityTitle());
            adInfo.setActivityInfo(activityInfo);

            //广告基本信息
            BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
//                adInfo.setIsHelpTime(adBaseInfo.getIsHelpTime());

            //广告素材信息
            List<CommonMeterialResp> meterialList = pullAdNonPopupResp.getMeterialList();

            List<GetUserAdResp.Meterial> meterial = new ArrayList<>();

            if ("icon".equals(adBaseInfo.getAdPositionSymbol())) {
                meterial = this.getIconAd(meterialList, imageResultMap, adInfo);
            } else {
                meterial = this.getNoPopAd(meterialList, imageResultMap, adInfo);
            }

            adInfo.setMeterialList(meterial);

            getUserAdRespList.add(adInfo);
        }
        //gc
        activityBaseList = null;
        return getUserAdRespList;
    }

    /**
     * 组装家庭广告位广告出参信息
     *
     * @param pullAdNonPopupResps
     * @param userId
     * @return
     */
    public List<CustomerAdResp.AdInfo> packageCustomerAdPositionOutput(List<PullAdNonPopupResp> pullAdNonPopupResps, Long userId, List<ActivityBaseResp> activityBaseList) {
        List<CustomerAdResp.AdInfo> getUserAdRespList = new ArrayList<>();

        //图片id 批量请求基础平台获取url
        AdIdActivityIdRespBo adIdActivityIdList = this.getAdIdActivityIdList(pullAdNonPopupResps);

//        List<Long> activityIdList = adIdActivityIdList.getActivityIdList();

        //过滤出图片aid,活动id
        Map<Long, String> imageResultMap = adIdActivityIdList.getMaps();

        //批量获取活动基本信息
//        List<ActivityBaseResp> activityBaseList = this.getActivityBaseList(activityIdList, userId);

        for (PullAdNonPopupResp pullAdNonPopupResp : pullAdNonPopupResps) {
            //返回的实体类中的子类
            CustomerAdResp.AdInfo adInfo = new CustomerAdResp.AdInfo();
            CustomerAdResp.ActivityInfo activityInfo = new CustomerAdResp.ActivityInfo();

            //广告基本信息
            AdBaseInfoResp adBaseInfo = pullAdNonPopupResp.getAdBaseInfo();

            ActivityBaseResp activityDetail;
            if (adBaseInfo.getActivityId() != 0) {
                Optional<ActivityBaseResp> baseResp = activityBaseList.stream().filter(base -> base.getActivityId().equals(adBaseInfo.getActivityId())).findFirst();
                if (!baseResp.isPresent()) {
                    continue;
                }
                activityDetail = baseResp.get();

                //活动基本信息
                if (activityDetail != null) {
                    BeanUtils.copyProperties(activityDetail, activityInfo, "activityName", "activityDescription");

                    adInfo.setIsHelpTime(adBaseInfo.getIsHelpTime());
                    //判断是否真的在保护期,商家多状态banner不取消保护期
                    if (adBaseInfo.getIsHelpTime() == 1) {
                        Integer isProtected = this.isProtected(activityDetail.getActivityEndTime(), adBaseInfo.getActivityId(), userId, adBaseInfo.getIsHelpTime(), adBaseInfo.getAdId(), adBaseInfo.getAdPositionSymbol());
                        adInfo.setIsHelpTime(isProtected);
                    }
                }
            }
            activityInfo.setActivityTitle(adBaseInfo.getActivityTitle());
            adInfo.setActivityInfo(activityInfo);

            //广告基本信息
            BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
//                adInfo.setIsHelpTime(adBaseInfo.getIsHelpTime());

            //广告素材信息
            List<CommonMeterialResp> meterialList = pullAdNonPopupResp.getMeterialList();

            List<CustomerAdResp.Meterial> meterial = new ArrayList<>();
            meterial = this.getCustomerNoPopAd(meterialList, imageResultMap, adInfo);

            adInfo.setMeterialList(meterial);

            getUserAdRespList.add(adInfo);
        }
        //gc
        activityBaseList = null;
        return getUserAdRespList;
    }

    /**
     * 组装弹窗广告信息
     */
    public List<GetUserAdResp.AdInfo> packageDialogAdOutput(PullAdPopupResp pullAdPopupResp, GetUserPopAdRqt getUserPopAdRqt, ActivityBaseResp activityBaseResp) {
        String userClass = getUserPopAdRqt.getUserClass();
        Long userId = getUserPopAdRqt.getUserId();
        Integer isWeb = getUserPopAdRqt.getIsWeb();
        //返回结果list
        List<GetUserAdResp.AdInfo> getUserAdRespList = new ArrayList<>();

        //获取图片urlissue
        List<String> aidList = pullAdPopupResp.getMeterialList().stream().filter(f -> f.getPictureId() != null).map(PopMeterialResp::getPictureId).map(Objects::toString).collect(Collectors.toList());

        Map<Long, String> imageResultMap = new HashMap<>();
        if (aidList.size() > 0) {
            imageResultMap = httpImageUtils.sendPostRequest(aidList);
        }
        //返回的实体类中的子类
        GetUserAdResp.AdInfo adInfo = new GetUserAdResp.AdInfo();
        GetUserAdResp.ActivityInfo activityInfo = new GetUserAdResp.ActivityInfo();
        //广告基本信息
        AdBaseInfoResp adBaseInfo = pullAdPopupResp.getAdBaseInfo();

//        GetActivityDetailSignResp activityDetailSignResp = null;
        if (adBaseInfo.getActivityId() != 0) {
            GetActivityDetailSignRqt getActivityDetailSignRqt = new GetActivityDetailSignRqt();
            getActivityDetailSignRqt.setActivityId(adBaseInfo.getActivityId());
            getActivityDetailSignRqt.setUserId(userId);
//            activityDetailSignResp = activityBusinessServiceApi.getActivityDetailSignResp(getActivityDetailSignRqt);

            //活动基本信息
            if (activityBaseResp != null) {
//                if (activityBaseResp.getApplyState() != 0) {
                BeanUtils.copyProperties(
                        activityBaseResp, activityInfo,
                        "activityName", "activityDescription");
//                }
                //1已下发已报名，0未下发,2:已经下发 未报名
                adInfo.setHasSignUp(activityBaseResp.getApplyState() == 0 ? 2 : activityBaseResp.getApplyState());
            }
        } else {
            adInfo.setHasSignUp(0);
        }
        adInfo.setActivityInfo(activityInfo);
        //广告基本信息
        BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
        adInfo.setMaterialClass("picture");
        //功能性弹窗关联信息设置
        if (PopStyleEnum.isFunctionPop(adBaseInfo.getPopStyleSymbol())) {
            if (StringUtils.isEmpty(adBaseInfo.getFunctionRelationJson())) {
                throw new BusException("功能弹窗关联信息不能为空,adId=" + adBaseInfo.getAdId());
            }
            FunctionRelationInfoBo functionRelationInfoBo = JSONObject.parseObject(adBaseInfo.getFunctionRelationJson(), FunctionRelationInfoBo.class);
            GetUserAdResp.FunctionRelationInfoVo functionRelationInfo = new GetUserAdResp.FunctionRelationInfoVo();
            BeanUtils.copyProperties(functionRelationInfoBo, functionRelationInfo);
            adInfo.setFunctionRelationInfo(functionRelationInfo);
        }
        //判断活动是否是开始前还是开始后
        Date currentTime = new Date();

        //广告素材信息
        List<GetUserAdResp.Meterial> meterial = new ArrayList<>();
        if (pullAdPopupResp.getMeterialList() != null) {
            List<PopMeterialResp> meterialList = pullAdPopupResp.getMeterialList();

            PopMeterialResp commonMeterialResp = null;
            //领奖弹窗特殊处理，返回领奖前以及领奖后的素材
            if (PopStyleEnum.ACTIVITY_RECEIVE_REWARD_POP.type.equals(adBaseInfo.getPopStyleSymbol())) {
                for (PopMeterialResp materialInfo : meterialList) {
                    GetUserAdResp.Meterial materialVo = new GetUserAdResp.Meterial();
                    BeanUtils.copyProperties(materialInfo, materialVo);
                    materialVo.setMaterialClass("picture");
                    materialVo.setPictureUrl(imageResultMap.get(materialInfo.getPictureId()));
                    meterial.add(materialVo);
                }
                adInfo.setMeterialList(meterial);
                getUserAdRespList.add(adInfo);
                return getUserAdRespList;
            }
//            ActivityBase activityDetailById = null;
            if (pullAdPopupResp.getAdBaseInfo().getActivityId() == 0 || !PopStyleEnum.isActivityPop(pullAdPopupResp.getAdBaseInfo().getPopStyleSymbol())) {
                commonMeterialResp = meterialList.get(0);
            } else {
                //如果活动id=0,表示是普通弹窗广告，或者是活动广告但是普通素材，素材表中只有一个
                ActivityDetailByIdRqt getActivityDetailRqt1 = new ActivityDetailByIdRqt();
                getActivityDetailRqt1.setActivityId(adBaseInfo.getActivityId());
//                activityDetailById = activityBusinessServiceApi.getActivityDetailById(getActivityDetailRqt1);
                if (!ObjectUtils.isEmpty(activityBaseResp)) {
                    commonMeterialResp = currentTime.before(activityBaseResp.getActivityStartTime()) ? meterialList.get(0) : meterialList.get(1);
                }
            }

            GetUserAdResp.Meterial meterial1 = new GetUserAdResp.Meterial();
            meterial1.setForwardUrlType(commonMeterialResp.getForwardUrlType());
            meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
            meterial1.setMaterialType(commonMeterialResp.getMaterialType());
            meterial1.setMaterialClass("picture");

            String activityLandingUrl;
            //3是广告菜单里创建的活动落地页，不走常规活动落地页赋值
//            if (activityDetailById != null && commonMeterialResp.getForwardUrlType() != 3) {
//                if (activityDetailSignResp != null && !StringUtils.isEmpty(activityDetailSignResp.getActivityUrl())) {
//                    activityLandingUrl = activityDetailSignResp.getActivityUrl() + "&t=" + isWeb;
//                }
//            } else {
//                activityLandingUrl = commonMeterialResp.getForwardUrl();
//            }
            //统一改为从广告服务获取地址
            activityLandingUrl = commonMeterialResp.getForwardUrl();
            meterial1.setForwardUrl(activityLandingUrl);

            GetUserAdResp.Meterial popMaterialList = this.getPopMaterialList(
                    PopAdMaterialBo.builder().activityLandingUrl(activityLandingUrl)
                            .pullAdPopupResp(pullAdPopupResp)
                            .adBaseInfo(adBaseInfo)
                            .adInfo(adInfo)
                            .commonMeterialResp(commonMeterialResp)
                            .userClass(userClass).build()
            );
            meterial1.setPopIconList(popMaterialList.getPopIconList());
            meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
            meterial.add(meterial1);
        }
        adInfo.setMeterialList(meterial);
        getUserAdRespList.add(adInfo);
        return getUserAdRespList;
    }

    /**
     * 组装家庭弹窗广告信息
     */
    public List<CustomerAdResp.AdInfo> packageCustomerPopAdOutput(PullAdPopupResp pullAdPopupResp, CustomerAdRqt customerAdRqt, CustomerActivityBaseResp activityBaseResp) {
//        String userClass = customerAdRqt.getUserClass();
        Long userId = customerAdRqt.getUserId();
        //返回结果list
        List<CustomerAdResp.AdInfo> customerAdRespList = new ArrayList<>();

        //获取图片urlissue
        List<String> aidList = pullAdPopupResp.getMeterialList().stream().filter(f -> f.getPictureId() != null).map(PopMeterialResp::getPictureId).map(Objects::toString).collect(Collectors.toList());

        Map<Long, String> imageResultMap = new HashMap<>();
        if (aidList.size() > 0) {
            imageResultMap = httpImageUtils.sendPostRequest(aidList);
        }
        //返回的实体类中的子类
        CustomerAdResp.AdInfo adInfo = new CustomerAdResp.AdInfo();
        CustomerAdResp.ActivityInfo activityInfo = new CustomerAdResp.ActivityInfo();
        //广告基本信息
        AdBaseInfoResp adBaseInfo = pullAdPopupResp.getAdBaseInfo();

//        GetActivityDetailSignResp activityDetailSignResp = null;
        if (adBaseInfo.getActivityId() != 0) {
            GetActivityDetailSignRqt getActivityDetailSignRqt = new GetActivityDetailSignRqt();
            getActivityDetailSignRqt.setActivityId(adBaseInfo.getActivityId());
            getActivityDetailSignRqt.setUserId(userId);
//            activityDetailSignResp = activityBusinessServiceApi.getActivityDetailSignResp(getActivityDetailSignRqt);

            //活动基本信息
            if (activityBaseResp != null) {
//                if (activityBaseResp.getApplyState() != 0) {
                BeanUtils.copyProperties(
                        activityBaseResp, activityInfo,
                        "activityName", "activityDescription");
//                }
                //1已下发已报名，0未下发,2:已经下发 未报名
                adInfo.setHasSignUp(activityBaseResp.getApplyState() == 0 ? 2 : activityBaseResp.getApplyState());
                adInfo.setHasReward(Objects.nonNull(activityBaseResp.getRewardGiveState()) ? activityBaseResp.getRewardGiveState() : 0);
            }
        } else {
            adInfo.setHasSignUp(0);
        }
        adInfo.setActivityInfo(activityInfo);
        //广告基本信息
        BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
        adInfo.setMaterialClass("picture");

        //判断活动是否是开始前还是开始后
        Date currentTime = new Date();

        //广告素材信息
        List<CustomerAdResp.Meterial> meterial = new ArrayList<>();
        if (pullAdPopupResp.getMeterialList() != null) {
            List<PopMeterialResp> meterialList = pullAdPopupResp.getMeterialList();
            for (PopMeterialResp popMeterialResp : meterialList) {
                PopMeterialResp commonMeterialResp = null;

                //            ActivityBase activityDetailById = null;
                if (pullAdPopupResp.getAdBaseInfo().getActivityId() == 0 || !PopStyleEnum.isActivityPop(pullAdPopupResp.getAdBaseInfo().getPopStyleSymbol())) {
                    commonMeterialResp = popMeterialResp;
                } else {
                    //如果活动id=0,表示是普通弹窗广告，或者是活动广告但是普通素材，素材表中只有一个
                    ActivityDetailByIdRqt getActivityDetailRqt1 = new ActivityDetailByIdRqt();
                    getActivityDetailRqt1.setActivityId(adBaseInfo.getActivityId());
                    //                activityDetailById = activityBusinessServiceApi.getActivityDetailById(getActivityDetailRqt1);
                    if (!ObjectUtils.isEmpty(activityBaseResp)) {
//                        commonMeterialResp = currentTime.before(activityBaseResp.getActivityStartTime()) ? popMeterialResp : meterialList.get(1);
                        commonMeterialResp = popMeterialResp;
                    }
                }

                CustomerAdResp.Meterial meterial1 = new CustomerAdResp.Meterial();
                meterial1.setForwardUrlType(commonMeterialResp.getForwardUrlType());
                meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
                meterial1.setMaterialType(commonMeterialResp.getMaterialType());
                meterial1.setIsLimitTime(commonMeterialResp.getIsLimitTime());
                meterial1.setShowTime(commonMeterialResp.getShowTime());
                meterial1.setMaterialClass("picture");

                //统一改为从广告服务获取地址
                meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());

                meterial1.setAdForwardUrlInfo(this.getAdForwardUrlInfo(commonMeterialResp.getAdForwardUrlBo()));

                //            CustomerAdResp.Meterial popMaterialList = this.getCustomerPopMaterialList(
                //                    CustomerPopAdMaterialBo.builder().activityLandingUrl(commonMeterialResp.getForwardUrl())
                //                            .pullAdPopupResp(pullAdPopupResp)
                //                            .adBaseInfo(adBaseInfo)
                //                            .adInfo(adInfo)
                //                            .commonMeterialResp(commonMeterialResp)
                //                            .userClass(userClass).build()
                //            );
                //            meterial1.setPopIconList(popMaterialList.getPopIconList());
                meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
                meterial.add(meterial1);
            }

        }
        adInfo.setMeterialList(meterial);
        customerAdRespList.add(adInfo);
        return customerAdRespList;
    }

    /**
     * 组装非登录状态家庭弹窗广告信息
     */
    public List<CustomerAdResp.AdInfo> packageUnLoginCustomerPopAdOutput(PullAdPopupResp pullAdPopupResp, CustomerAdRqt customerAdRqt, ActivityBase activityBase) {
        String userClass = customerAdRqt.getUserClass();
        Long userId = customerAdRqt.getUserId();
        //返回结果list
        List<CustomerAdResp.AdInfo> customerAdRespList = new ArrayList<>();

        //获取图片urlissue
        List<String> aidList = pullAdPopupResp.getMeterialList().stream().filter(f -> f.getPictureId() != null).map(PopMeterialResp::getPictureId).map(Objects::toString).collect(Collectors.toList());

        Map<Long, String> imageResultMap = new HashMap<>();
        if (aidList.size() > 0) {
            imageResultMap = httpImageUtils.sendPostRequest(aidList);
        }
        //返回的实体类中的子类
        CustomerAdResp.AdInfo adInfo = new CustomerAdResp.AdInfo();
        CustomerAdResp.ActivityInfo activityInfo = new CustomerAdResp.ActivityInfo();
        //广告基本信息
        AdBaseInfoResp adBaseInfo = pullAdPopupResp.getAdBaseInfo();

//        GetActivityDetailSignResp activityDetailSignResp = null;
        if (adBaseInfo.getActivityId() != 0) {
//            GetActivityDetailSignRqt getActivityDetailSignRqt = new GetActivityDetailSignRqt();
//            getActivityDetailSignRqt.setActivityId(adBaseInfo.getActivityId());
//            getActivityDetailSignRqt.setUserId(userId);
//            activityDetailSignResp = activityBusinessServiceApi.getActivityDetailSignResp(getActivityDetailSignRqt);

            //活动基本信息
            if (activityBase != null) {
//                if (activityBaseResp.getApplyState() != 0) {
                BeanUtils.copyProperties(
                        activityBase, activityInfo,
                        "activityName", "activityDescription");
//                }
                //1已下发已报名，0未下发,2:已经下发 未报名
//                adInfo.setHasSignUp(activityBaseResp.getApplyState() == 0 ? 2 : activityBaseResp.getApplyState());
            }
        } else {
            adInfo.setHasSignUp(0);
        }
        adInfo.setActivityInfo(activityInfo);
        //广告基本信息
        BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
        adInfo.setMaterialClass("picture");

        //判断活动是否是开始前还是开始后
        Date currentTime = new Date();

        //广告素材信息
        List<CustomerAdResp.Meterial> meterial = new ArrayList<>();
        if (pullAdPopupResp.getMeterialList() != null) {
            List<PopMeterialResp> meterialList = pullAdPopupResp.getMeterialList();

            PopMeterialResp commonMeterialResp = null;

//            ActivityBase activityDetailById = null;
            if (pullAdPopupResp.getAdBaseInfo().getActivityId() == 0 || !PopStyleEnum.isActivityPop(pullAdPopupResp.getAdBaseInfo().getPopStyleSymbol())) {
                commonMeterialResp = meterialList.get(0);
            } else {
                //如果活动id=0,表示是普通弹窗广告，或者是活动广告但是普通素材，素材表中只有一个
                ActivityDetailByIdRqt getActivityDetailRqt1 = new ActivityDetailByIdRqt();
                getActivityDetailRqt1.setActivityId(adBaseInfo.getActivityId());
//                activityDetailById = activityBusinessServiceApi.getActivityDetailById(getActivityDetailRqt1);
                if (!ObjectUtils.isEmpty(activityBase)) {
                    commonMeterialResp = currentTime.before(activityBase.getActivityStartTime()) ? meterialList.get(0) : meterialList.get(1);
                }
            }

            CustomerAdResp.Meterial meterial1 = new CustomerAdResp.Meterial();
            meterial1.setForwardUrlType(commonMeterialResp.getForwardUrlType());
            meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
            meterial1.setMaterialType(commonMeterialResp.getMaterialType());
            meterial1.setMaterialClass("picture");

            String activityLandingUrl;
            //3是广告菜单里创建的活动落地页，不走常规活动落地页赋值
//            if (activityDetailById != null && commonMeterialResp.getForwardUrlType() != 3) {
//                if (activityDetailSignResp != null && !StringUtils.isEmpty(activityDetailSignResp.getActivityUrl())) {
//                    activityLandingUrl = activityDetailSignResp.getActivityUrl() + "&t=" + isWeb;
//                }
//            } else {
//                activityLandingUrl = commonMeterialResp.getForwardUrl();
//            }
            //统一改为从广告服务获取地址
            activityLandingUrl = commonMeterialResp.getForwardUrl();
            meterial1.setForwardUrl(activityLandingUrl);

            CustomerAdResp.Meterial popMaterialList = this.getCustomerPopMaterialList(
                    CustomerPopAdMaterialBo.builder().activityLandingUrl(activityLandingUrl)
                            .pullAdPopupResp(pullAdPopupResp)
                            .adBaseInfo(adBaseInfo)
                            .adInfo(adInfo)
                            .commonMeterialResp(commonMeterialResp)
                            .userClass(userClass).build()
            );
            meterial1.setPopIconList(popMaterialList.getPopIconList());
            meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
            meterial.add(meterial1);
        }
        adInfo.setMeterialList(meterial);
        customerAdRespList.add(adInfo);
        return customerAdRespList;
    }

    /**
     * 获取弹窗素材列表
     */
    public GetUserAdResp.Meterial getPopMaterialList(PopAdMaterialBo popAdMaterialBo) {

        GetUserAdResp.Meterial meterial1 = new GetUserAdResp.Meterial();

        String activityLandingUrl = popAdMaterialBo.getActivityLandingUrl();

        AdBaseInfoResp adBaseInfo = popAdMaterialBo.getAdBaseInfo();

        PopMeterialResp commonMeterialResp = popAdMaterialBo.getCommonMeterialResp();


        if (popAdMaterialBo.getPullAdPopupResp().getAdBaseInfo().getActivityId() != 0 && PopStyleEnum.isActivityPop(popAdMaterialBo.getPullAdPopupResp().getAdBaseInfo().getPopStyleSymbol())) {
            List<GetUserAdResp.PopIcon> popIconList = new ArrayList<>();
            GetUserAdResp.PopIcon popIcon = new GetUserAdResp.PopIcon();
            PopIconInfoResp popIconInfoResp = new PopIconInfoResp();

            if (popAdMaterialBo.getAdInfo().getHasSignUp() == 1) {
                if (CollectionUtils.isNotEmpty(commonMeterialResp.getPopIconList())) {
                    if (commonMeterialResp.getPopIconList().size() > 1) {
                        BeanUtils.copyProperties(commonMeterialResp.getPopIconList().get(1), popIconInfoResp);
                    } else {
                        //防止异常情况，弹窗素材只有一个的情况
                        PopIconInfoResp firstPopIconInfoResp = commonMeterialResp.getPopIconList().get(0);
                        popIconInfoResp = this.popIconInfoRespBean(activityLandingUrl, firstPopIconInfoResp.getButtonStyle(), firstPopIconInfoResp.getButtonPictureId());
                    }
                    BeanUtils.copyProperties(popIconInfoResp, popIcon);
                    if ("landing_page".equals(popIconInfoResp.getForwardCode())) {
                        popIcon.setForwardUrl(activityLandingUrl);
                    }
                }
            } else {
                if (popAdMaterialBo.getUserClass().equals(UserTypeEnum.MERCHANT.type)) {
                    popIconInfoResp = commonMeterialResp.getPopIconList().get(0);
                    BeanUtils.copyProperties(popIconInfoResp, popIcon);
                    if (adBaseInfo.getActivityId() != null && adBaseInfo.getActivityId() != 0) {
                        popIcon.setForwardUrl(activityLandingUrl);
                    }
                } else {
                    popIcon.setForwardUrl((adBaseInfo.getActivityId() != null && adBaseInfo.getActivityId() != 0) ? activityLandingUrl : commonMeterialResp.getForwardUrl());
                }
            }

            if (!StringUtils.isEmpty(popIconInfoResp.getChildIcon())) {
                ChildIconClass childIconClass = JSONObject.toJavaObject(JSONObject.parseObject(popIconInfoResp.getChildIcon()), ChildIconClass.class);
                childIconClass.setForwardUrl(activityLandingUrl);
                popIcon.setChildIcon(childIconClass);
            }

            popIcon.setForwardUrlType("landing_page".equals(popIcon.getForwardCode()) ? 1 : 2);
            popIcon.setIsReceive(popIconInfoResp.getIsReceive() == null ? 0 : 1);
            if (Objects.nonNull(popIcon.getButtonPictureId()) && popIcon.getButtonPictureId() > 0) {
                Map<Long, String> imageResultMap = httpImageUtils.sendPostRequest(Collections.singletonList(popIcon.getButtonPictureId().toString()));
                popIcon.setButtonPictureUrl(Objects.nonNull(imageResultMap.get(popIcon.getButtonPictureId())) ? imageResultMap.get(popIcon.getButtonPictureId()) : "");
            }
            popIconList.add(popIcon);
            meterial1.setPopIconList(popIconList);
        }
        return meterial1;
    }


    /**
     * 获取家庭弹窗素材列表
     */
    public CustomerAdResp.Meterial getCustomerPopMaterialList(CustomerPopAdMaterialBo popAdMaterialBo) {

        CustomerAdResp.Meterial meterial1 = new CustomerAdResp.Meterial();

        String activityLandingUrl = popAdMaterialBo.getActivityLandingUrl();

        AdBaseInfoResp adBaseInfo = popAdMaterialBo.getAdBaseInfo();

        PopMeterialResp commonMeterialResp = popAdMaterialBo.getCommonMeterialResp();


        if (popAdMaterialBo.getPullAdPopupResp().getAdBaseInfo().getActivityId() != 0 && PopStyleEnum.isActivityPop(popAdMaterialBo.getPullAdPopupResp().getAdBaseInfo().getPopStyleSymbol())) {
            List<CustomerAdResp.PopIcon> popIconList = new ArrayList<>();
            CustomerAdResp.PopIcon popIcon = new CustomerAdResp.PopIcon();
            PopIconInfoResp popIconInfoResp = new PopIconInfoResp();

            if (popAdMaterialBo.getAdInfo().getHasSignUp() == 1) {
                if (CollectionUtils.isNotEmpty(commonMeterialResp.getPopIconList())) {
                    if (commonMeterialResp.getPopIconList().size() > 1) {
                        BeanUtils.copyProperties(commonMeterialResp.getPopIconList().get(1), popIconInfoResp);
                    } else {
                        //防止异常情况，弹窗素材只有一个的情况
                        PopIconInfoResp firstPopIconInfoResp = commonMeterialResp.getPopIconList().get(0);
                        popIconInfoResp = this.popIconInfoRespBean(activityLandingUrl, firstPopIconInfoResp.getButtonStyle(), firstPopIconInfoResp.getButtonPictureId());
                    }
                    BeanUtils.copyProperties(popIconInfoResp, popIcon);
                    if ("landing_page".equals(popIconInfoResp.getForwardCode())) {
                        popIcon.setForwardUrl(activityLandingUrl);
                    }
                }
            } else {
                if (popAdMaterialBo.getUserClass().equals(UserTypeEnum.MERCHANT.type)) {
                    popIconInfoResp = commonMeterialResp.getPopIconList().get(0);
                    BeanUtils.copyProperties(popIconInfoResp, popIcon);
                    if (adBaseInfo.getActivityId() != null && adBaseInfo.getActivityId() != 0) {
                        popIcon.setForwardUrl(activityLandingUrl);
                    }
                } else {
                    popIcon.setForwardUrl((adBaseInfo.getActivityId() != null && adBaseInfo.getActivityId() != 0) ? activityLandingUrl : commonMeterialResp.getForwardUrl());
                }
            }

            if (!StringUtils.isEmpty(popIconInfoResp.getChildIcon())) {
                ChildIconClass childIconClass = JSONObject.toJavaObject(JSONObject.parseObject(popIconInfoResp.getChildIcon()), ChildIconClass.class);
                childIconClass.setForwardUrl(activityLandingUrl);
                popIcon.setChildIcon(childIconClass);
            }

            popIcon.setForwardUrlType("landing_page".equals(popIcon.getForwardCode()) ? 1 : 2);
            popIcon.setIsReceive(popIconInfoResp.getIsReceive() == null ? 0 : 1);
            if (Objects.nonNull(popIcon.getButtonPictureId()) && popIcon.getButtonPictureId() > 0) {
                Map<Long, String> imageResultMap = httpImageUtils.sendPostRequest(Collections.singletonList(popIcon.getButtonPictureId().toString()));
                popIcon.setButtonPictureUrl(Objects.nonNull(imageResultMap.get(popIcon.getButtonPictureId())) ? imageResultMap.get(popIcon.getButtonPictureId()) : "");
            }
            popIconList.add(popIcon);
            meterial1.setPopIconList(popIconList);
        }
        return meterial1;
    }

    /**
     * 防止异常情况，弹窗素材只有一个的情况
     */
    public PopIconInfoResp popIconInfoRespBean(String activityLandingUrl, String buttonStyle, Long buttonPictureId) {
        PopIconInfoResp popIconInfoResp = new PopIconInfoResp();
        popIconInfoResp.setForwardCode("landing_page");
        popIconInfoResp.setIconTitle("活动详情");
        popIconInfoResp.setIsChildIcon(0);
        popIconInfoResp.setIconFunction("non");
        popIconInfoResp.setIsReceive(1);
        popIconInfoResp.setChildIcon("");
        popIconInfoResp.setForwardUrl(activityLandingUrl);
        popIconInfoResp.setButtonStyle(buttonStyle);
        popIconInfoResp.setButtonPictureId(buttonPictureId);
        return popIconInfoResp;
    }

    /**
     * 返回已下发活动，未达上限活动id
     */
    public List<Long> hasSignUpAndNotUpperLimit(List<Long> activityIdList, Long userId, String userClass) {
        List<Long> checkedActivityList = new ArrayList<>();
        if (CollectionUtils.isEmpty(activityIdList)) {
            return null;
        }
        GetUserHasSignUpBatchRqt getUserHasSignUpBatchRqt = new GetUserHasSignUpBatchRqt();
        getUserHasSignUpBatchRqt.setUserId(userId);
        getUserHasSignUpBatchRqt.setActivityIdList(activityIdList);

        Map<String, Integer> signUpBatchList = activityBusinessServiceApi.getUserHasSignUpBatch(getUserHasSignUpBatchRqt);

        if (signUpBatchList == null) {
            return null;
        }
        //过滤已下发的活动
        List<Long> hadSignUpList = signUpBatchList.entrySet().stream().filter(f -> f.getValue() == 1).map(m -> Long.parseLong(m.getKey())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hadSignUpList)) {
            return null;
        }
        //现只针对师傅端做达到领奖上限处理
        if (!UserTypeEnum.MASTER.type.equals(userClass)) {
            checkedActivityList.addAll(hadSignUpList);
            return checkedActivityList;
        }
        GetUserHasUpperLimitBatchRqt getUserHasUpperLimitBatchRqt = new GetUserHasUpperLimitBatchRqt();
        getUserHasUpperLimitBatchRqt.setActivityIdList(hadSignUpList);
        getUserHasUpperLimitBatchRqt.setUserId(userId);

        Map<Long, Integer> hasUpperLimitMap = activityBusinessServiceApi.getUserHasUpperLimitBatch(getUserHasUpperLimitBatchRqt);

        //返回空则都没有报名，肯定没有达到上限
        if (hasUpperLimitMap == null) {
            checkedActivityList.addAll(hadSignUpList);
            return checkedActivityList;
        }
        //取未达到上限的活动
        checkedActivityList = hasUpperLimitMap.entrySet().stream().filter(f -> f.getValue() == 0).map(m -> m.getKey()).collect(Collectors.toList());
        return checkedActivityList;
    }


    public List<PullAdNonPopupResp> getBatchAd(GetUserAdBatchRqt getUserAdBatchRqt, List<String> needQueryAdPositionSymbolList, Long userId) {

        try {
            PullMultiNonPopAdReq pullMultiNonPopAdReq = new PullMultiNonPopAdReq();

            pullMultiNonPopAdReq.setAdPositionSymbolList(needQueryAdPositionSymbolList);
            pullMultiNonPopAdReq.setPort(getUserAdBatchRqt.getLaunchPort());
            pullMultiNonPopAdReq.setUserClass(getUserAdBatchRqt.getUserClass());
            pullMultiNonPopAdReq.setUserId(userId);
            //请求大数据接口获取用户人群标签
            pullMultiNonPopAdReq.setTagList(this.getTagListFromBigData(getUserAdBatchRqt.getUserClass(), userId, getUserAdBatchRqt.getLaunchPort()));
            log.info("pullMultiNonPopAdReq入参：{}", JSON.toJSONString(pullMultiNonPopAdReq));
            return adPullApi.pullMultiNonPopup(pullMultiNonPopAdReq);

        } catch (Exception e) {
            throw new BusException("get_batch_ad_fail", e.getMessage());
        }
    }

    /**
     * 过滤出批量广告中的图片
     */
    public Map<Long, String> getBatchAdImg(List<PullAdNonPopupResp> pullAdNonPopupResps) {
        //取出所有的图片id
        Set<Long> aidSet = new HashSet<>();

        for (PullAdNonPopupResp pullAdNonPopupResp : pullAdNonPopupResps) {

            aidSet.addAll(
                    pullAdNonPopupResp.getMeterialList().stream()
                            .map(CommonMeterialResp::getPictureId)
                            .filter(pictureId -> !ObjectUtils.isEmpty(pictureId)).collect(Collectors.toSet())
            );

            aidSet.addAll(
                    pullAdNonPopupResp.getMeterialList().stream()
                            .map(CommonMeterialResp::getClickedPictureId)
                            .filter(clickedPictureId -> !ObjectUtils.isEmpty(clickedPictureId)).collect(Collectors.toSet())
            );
        }

        Map<Long, String> imageResultMap = new HashMap<>();

        List<String> aidList = aidSet.stream().map(e -> e + "").collect(Collectors.toList());
        if (aidList.size() > 0) {
            imageResultMap = httpImageUtils.sendPostRequest(aidList);
        }
        return imageResultMap;
    }

    /**
     * 批量获取活动信息
     */
    public List<ActivityLandPageBatchResp> getBatchActivityInfo(Long userId, List<Long> activityIdList) {
        try {
            List<ActivityLandPageBatchResp> activityDetailBatch = new ArrayList<>();
            GetActivityDetailBatchRqt getActivityDetailBatchRqt = new GetActivityDetailBatchRqt();
            getActivityDetailBatchRqt.setUserId(userId);
            getActivityDetailBatchRqt.setActivityIdList(activityIdList);
            //获取活动信息
            activityDetailBatch = activityBusinessServiceApi.getActivityDetailBatch(getActivityDetailBatchRqt);
            if (CollectionUtils.isEmpty(activityDetailBatch)) {
                log.warn("com.wanshifu.iop.activity.service.api.ActivityBusinessServiceApi.getActivityDetailBatch 查询为空 ");
            }
            return activityDetailBatch;
        } catch (Exception e) {
            throw new BusException("get_batch_activity_info", e.getMessage());
        }
    }

    /**
     * 批量获取广告位广告，非弹窗、非开屏广告
     */
    @Override
    @ExceptionHandle(note = "广告位广告批量接口获取异常")
    public Map<String, GetUserAdResp> getAdPositionBatch(GetUserAdBatchRqt getUserAdBatchRqt) {
        Long userId = getUserAdBatchRqt.getUserId();
//        Long userId = 61300336427L;
        /**
         *  查询是否已有拉取广告缓存，有则直接使用缓存数据，无则查询
         */
        List<String> adPositionSymbolList = getUserAdBatchRqt.getAdPositionSymbolList();
        List<String> keyList = new ArrayList<>();
        adPositionSymbolList.forEach(symbol -> {
            String batchUserFormatKey = String.format(CacheKeyConstant.BATCH_PULL_AD_POSITION_KEY, getUserAdBatchRqt.getUserClass(), getUserAdBatchRqt.getLaunchPort(), symbol, userId);
            keyList.add(batchUserFormatKey);
        });
        Map<String, GetUserAdResp> resultNew = new HashMap<>();
        HashMap<String, String> cacheValueList = redisHelper.batchGet(keyList);
        List<String> needQueryAdPositionSymbolList = new ArrayList<>();
        adPositionSymbolList.forEach(symbol -> {
            String cacheValue = cacheValueList.get(symbol);
            if (!StringUtils.isEmpty(cacheValue)) {
                GetUserAdResp getUserAdResp = JSON.parseObject(cacheValue, GetUserAdResp.class);
                resultNew.put(symbol, getUserAdResp);
            } else {
                needQueryAdPositionSymbolList.add(symbol);
            }
        });
        if (CollectionUtils.isEmpty(needQueryAdPositionSymbolList)) {
            return resultNew;
        }
        try {
            List<PullAdNonPopupResp> pullAdNonPopupResps = this.getBatchAd(getUserAdBatchRqt, needQueryAdPositionSymbolList, userId);
            if (CollectionUtils.isEmpty(pullAdNonPopupResps)) {
                return resultNew;
            }
            //取出所有的活动id 自动去重
            Optional<PullAdNonPopupResp> existActivityAdOptional = pullAdNonPopupResps.stream().filter(f -> f.getAdBaseInfo().getActivityId() > 0).findFirst();
            //活动批量过滤
            Map<Long, ActivityBaseResp> activityBaseMap = new HashMap<>();
            if (existActivityAdOptional.isPresent()) {
                List<ActivityBaseResp> activityDetailBatch = this.batchSignUpAndNotUpperLimitActivityDetail(pullAdNonPopupResps, userId, getUserAdBatchRqt.getUserClass());
                if (CollectionUtils.isNotEmpty(activityDetailBatch)) {
                    activityBaseMap = activityDetailBatch.stream().collect(Collectors.toMap(ActivityBaseResp::getActivityId, ActivityLandPageBatchResp -> ActivityLandPageBatchResp, (old, newObject) -> old));
                }
                //过滤不符合条件的活动广告
                List<Long> suitActivityIdList = activityDetailBatch.stream().map(m -> m.getActivityId()).collect(Collectors.toList());
                pullAdNonPopupResps = pullAdNonPopupResps.stream().filter(f -> f.getAdBaseInfo().getActivityId() < 1 || (f.getAdBaseInfo().getActivityId() > 0 && suitActivityIdList.contains(f.getAdBaseInfo().getActivityId()))).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(pullAdNonPopupResps)) {
                return resultNew;
            }
            //对多选一广告(一个位置有多个广告位广告，但只展示其中一个广告位广告)进行优先级过滤
            List<PullAdNonPopupResp> choiceOnePullAdNonPopupResps = pullAdNonPopupResps.stream().filter(f -> AdConstant.AD_MULTI_CHOICE_ONE.equals(f.getPriorityType())).collect(Collectors.toList());
            List<PullAdNonPopupResp> notChoiceOnePullAdNonPopupResps = pullAdNonPopupResps.stream().filter(f -> !AdConstant.AD_MULTI_CHOICE_ONE.equals(f.getPriorityType())).collect(Collectors.toList());
            List<PullAdNonPopupResp> finalShowPullAdNonPopupRespList = new ArrayList<>();
            GetMasterActivityDetailAdResp getMasterActivityDetailAdResp = null;
            if (CollectionUtils.isNotEmpty(choiceOnePullAdNonPopupResps)) {
                //根据标识分组
                Map<String, List<PullAdNonPopupResp>> choiceGroupMap = choiceOnePullAdNonPopupResps.stream().collect(Collectors.groupingBy(g -> g.getPriorityAdPositionSymbol()));
                for (Map.Entry<String, List<PullAdNonPopupResp>> entry : choiceGroupMap.entrySet()) {
                    List<PullAdNonPopupResp> symbolNonPopupList = entry.getValue();
                    //根据优先级从小到大排序
                    symbolNonPopupList.sort(Comparator.comparing(PullAdNonPopupResp::getPriorityNumber));
                    String selectedAdSymbol = symbolNonPopupList.get(0).getAdBaseInfo().getAdPositionSymbol();
                    List<PullAdNonPopupResp> selectedSymbolNonPopRespList = symbolNonPopupList.stream().filter(f -> f.getAdBaseInfo().getAdPositionSymbol().equals(selectedAdSymbol)).collect(Collectors.toList());
                    int maxShowNum = selectedSymbolNonPopRespList.get(0).getMaxShowNum();
                    List<PullAdNonPopupResp> addNonPopRespList = selectedSymbolNonPopRespList.size() >= maxShowNum ? selectedSymbolNonPopRespList.subList(0, maxShowNum) : selectedSymbolNonPopRespList;
                    finalShowPullAdNonPopupRespList.addAll(addNonPopRespList);
                    //假如是师傅报价列表顶部多状态广告位(一定是活动广告)要单独去查一下任务信息,2025-05-07 活动未开始不去查询
                    ActivityBaseResp activityBaseResp = activityBaseMap.get(addNonPopRespList.get(0).getAdBaseInfo().getActivityId());
                    if (AdConstant.QUOTED_TOP_BANNER.equals(selectedAdSymbol) && Objects.nonNull(activityBaseResp) && activityBaseResp.getActivityStartTime().before(new Date())) {
                        try {
                            GetMasterActivityDetailAdRqt getActivityDetailRqt = new GetMasterActivityDetailAdRqt();
                            getActivityDetailRqt.setUserId(userId);
                            getActivityDetailRqt.setActivityId(addNonPopRespList.get(0).getAdBaseInfo().getActivityId());
                            getMasterActivityDetailAdResp = landingPageServiceApi.getMasterActivityDetailAd(getActivityDetailRqt);
                        } catch (ApiAccessException e) {
                            throw new BusinessException("get_activity_detail_fail", e.getRetMesg());
                        }
                    }
                }
            }
            //非多选一优先级的广告
            if (CollectionUtils.isNotEmpty(notChoiceOnePullAdNonPopupResps)) {
                Map<String, List<PullAdNonPopupResp>> notChoiceGroupMap = notChoiceOnePullAdNonPopupResps.stream().collect(Collectors.groupingBy(g -> g.getAdBaseInfo().getAdPositionSymbol()));
                notChoiceGroupMap.forEach((adPositionSymbol, symbolNonPopupList) -> {
                    int maxShowNum = symbolNonPopupList.get(0).getMaxShowNum();
                    finalShowPullAdNonPopupRespList.addAll(symbolNonPopupList.size() >= maxShowNum ? symbolNonPopupList.subList(0, maxShowNum) : symbolNonPopupList);
                });
            }
            if (CollectionUtils.isEmpty(finalShowPullAdNonPopupRespList)) {
                return resultNew;
            }
            Map<String, List<PullAdNonPopupResp>> finalGroupMap = finalShowPullAdNonPopupRespList.stream().collect(Collectors.groupingBy(g -> g.getAdBaseInfo().getAdPositionSymbol()));
            //存在尾货广告则特殊处理，查询尾货信息
            List<WeiHuoInfoResp> weiHuoInfoRespList = this.getWeiHuoInfo(userId, finalGroupMap);
            //取出所有的图片id
            Map<Long, String> imageResultMap = this.getBatchAdImg(finalShowPullAdNonPopupRespList);
            List<Long> saveAdIdList = new ArrayList<>();
            Map<String, GetUserAdResp> needCacheMap = new HashMap<>();

            //根据广告位分组赋值
            for (Map.Entry<String, List<PullAdNonPopupResp>> entry : finalGroupMap.entrySet()) {
                List<PullAdNonPopupResp> symbolNonPopupList = entry.getValue();
                GetUserAdResp getUserAdResp = new GetUserAdResp();
                List<GetUserAdResp.AdInfo> adInfoList = new ArrayList<>();
                //该广告位下所有广告赋值
                for (PullAdNonPopupResp childNonpopResp : symbolNonPopupList) {
                    GetUserAdResp.AdInfo adInfo = new GetUserAdResp.AdInfo();
                    AdBaseInfoResp adBaseInfo = childNonpopResp.getAdBaseInfo();
                    adInfo.setActivityId(childNonpopResp.getAdBaseInfo().getActivityId());
                    BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
                    //素材列表
                    List<GetUserAdResp.Meterial> meterialList = new ArrayList<>();
                    //是尾货广告，有返回尾货订单赋值，否则过滤掉
                    Boolean isWeiHuoAdFlag = this.adIsWeiHuoFlag(childNonpopResp.getMeterialList());
                    if (isWeiHuoAdFlag && CollectionUtils.isEmpty(weiHuoInfoRespList)) {
                        continue;
                    }
                    //尾货信息赋值
                    if (isWeiHuoAdFlag && CollectionUtils.isNotEmpty(weiHuoInfoRespList)) {
                        adInfo.setMaterialSourceType(AdConstant.MATERIAL_SOURCE_TYPE_WEIHUO);
                        adInfo.setWeiHuoList(this.copyWeiHuoInfoRespList(weiHuoInfoRespList));
                    }
                    for (CommonMeterialResp commonMeterialResp : childNonpopResp.getMeterialList()) {
                        GetUserAdResp.Meterial meterial = new GetUserAdResp.Meterial();
                        BeanUtils.copyProperties(commonMeterialResp, meterial);
                        meterial.setMeterialTitle(commonMeterialResp.getMaterialTitle());
                        meterial.setPictureUrl(imageResultMap.getOrDefault(commonMeterialResp.getPictureId(), ""));
                        meterial.setClickedPictureUrl(imageResultMap.getOrDefault(commonMeterialResp.getClickedPictureId(), ""));
                        //统一改成由广告服务返回跳转地址
                        meterial.setForwardUrl(commonMeterialResp.getForwardUrl());
                        if (isWeiHuoAdFlag) {
                            AdMaterialExtBo adMaterialExtInfo = commonMeterialResp.getAdMaterialExtInfo();
                            if (Objects.nonNull(adMaterialExtInfo) && Objects.nonNull(adMaterialExtInfo.getMaterialSourceInfo())) {
                                meterial.setGoExploreForwardUrl(adMaterialExtInfo.getMaterialSourceInfo().getGoExploreForwardUrl());
                                meterial.setGoExploreGuideImgUrl(adMaterialExtInfo.getMaterialSourceInfo().getGoExploreGuideImgUrl());
                                meterial.setGoExploreForwardUrlType(adMaterialExtInfo.getMaterialSourceInfo().getForwardUrlType());
                            }
                        }
                        meterialList.add(meterial);
                    }
                    //活动信息赋值
                    GetUserAdResp.ActivityInfo activityInfo = new GetUserAdResp.ActivityInfo();
                    activityInfo.setActivityId(adBaseInfo.getActivityId());
                    if (adBaseInfo.getActivityId() > 0) {
                        ActivityBaseResp activityLandPageBatchResp = activityBaseMap.get(adBaseInfo.getActivityId());
                        activityInfo.setApplyTime(activityLandPageBatchResp.getApplyTime());
                        activityInfo.setActivityId(adBaseInfo.getActivityId());
                        activityInfo.setActivityName(activityLandPageBatchResp.getActivityName());
                        activityInfo.setActivityDescription(activityLandPageBatchResp.getActivityDescription());
                        activityInfo.setActivityStartTime(activityLandPageBatchResp.getActivityStartTime());
                        activityInfo.setActivityEndTime(activityLandPageBatchResp.getActivityEndTime());
                        //活动未开始，处理成未报名：师傅端根据这个字段判断（处理活动未开始展示任务的问题）
                        activityInfo.setIsApply(activityLandPageBatchResp.getActivityStartTime().before(new Date()) ? activityLandPageBatchResp.getApplyState() : 0);
                        adInfo.setIsHelpTime(adBaseInfo.getIsHelpTime());
                        //判断是否真的在保护期
                        if (adBaseInfo.getIsHelpTime() == 1) {
                            Integer isProtected = this.isProtected(activityLandPageBatchResp.getActivityEndTime(), adBaseInfo.getActivityId(), userId, adBaseInfo.getIsHelpTime(), adBaseInfo.getAdId(), adBaseInfo.getAdPositionSymbol());
                            adInfo.setIsHelpTime(isProtected);
                        }
                        //师傅报价列表顶部多状态广告位还需要赋值任务信息
                        if (Objects.nonNull(getMasterActivityDetailAdResp) && AdConstant.QUOTED_TOP_BANNER.equals(adBaseInfo.getAdPositionSymbol())) {
                            List<GetMasterActivityDetailAdResp.ActivityTaskInfo> activityTaskInfos = getMasterActivityDetailAdResp.getActivityTaskInfo();
                            GetUserAdResp.ActivityTaskApiInfo activityTaskInfo = new GetUserAdResp.ActivityTaskApiInfo();
                            if (CollectionUtils.isNotEmpty(activityTaskInfos)) {
                                BeanCopyUtil.copyProperties(activityTaskInfos.get(0), activityTaskInfo);
                                activityInfo.setActivityTaskInfo(activityTaskInfo);
                            }
                        }
                        adInfo.setActivityInfo(activityInfo);
                    }
                    adInfo.setMeterialList(meterialList);
                    adInfoList.add(adInfo);
                    saveAdIdList.add(adInfo.getAdId());
                }
                getUserAdResp.setAdInfo(adInfoList);
                resultNew.put(entry.getKey(), getUserAdResp);
                needCacheMap.put(entry.getKey(), getUserAdResp);
            }
            //选中了哪几个需要回调给平台，截取最大展示个数回调
            if (CollectionUtils.isNotEmpty(saveAdIdList)) {
                this.backAidList(saveAdIdList, userId);
            }
            //设置redis缓存
            if (!saveAdIdList.isEmpty() && redisCacheTime > 0) {
                List<BatchRedisSetBean> batchRedisSetBeans = new ArrayList<>();
                needCacheMap.forEach((adSymbol, getUserAdResp) -> {
                    if (Objects.nonNull(getUserAdResp) && CollectionUtils.isNotEmpty(getUserAdResp.getAdInfo())) {
                        BatchRedisSetBean bean = new BatchRedisSetBean();
                        String batchUserFormatKey = String.format(CacheKeyConstant.BATCH_PULL_AD_POSITION_KEY, getUserAdBatchRqt.getUserClass(), getUserAdBatchRqt.getLaunchPort(), adSymbol, userId);
                        bean.setKey(batchUserFormatKey);
                        bean.setValue(JSON.toJSONString(getUserAdResp));
                        bean.setCacheSeconds(redisCacheTime);
                        batchRedisSetBeans.add(bean);
                    }
                });
                if (CollectionUtils.isNotEmpty(batchRedisSetBeans)) {
                    redisHelper.batchSet(batchRedisSetBeans);
                }
            }
            return resultNew;
        } catch (Exception e) {
            e.printStackTrace();
            log.warn(e.getMessage());
            throw new BusException("get_batch_ad_err", e.getMessage());
        }
    }

    /**
     * 深拷贝问题处理
     *
     * @param weiHuoInfoRespList
     * @return
     */
    private List<WeiHuoInfoResp> copyWeiHuoInfoRespList(List<WeiHuoInfoResp> weiHuoInfoRespList) {
        List<WeiHuoInfoResp> copyWeiHuoInfoRespList = new ArrayList<>();
        weiHuoInfoRespList.forEach(resp -> {
            WeiHuoInfoResp weiHuoInfoResp = new WeiHuoInfoResp();
            BeanUtils.copyProperties(resp, weiHuoInfoResp);
            InfoOrderGoodsInfoResp masterInfoOrderGoods = new InfoOrderGoodsInfoResp();
            BeanUtils.copyProperties(resp.getMasterInfoOrderGoods(), masterInfoOrderGoods);
            weiHuoInfoResp.setMasterInfoOrderGoods(masterInfoOrderGoods);
            copyWeiHuoInfoRespList.add(weiHuoInfoResp);
        });
        return copyWeiHuoInfoRespList;
    }

    public boolean adIsWeiHuoFlag(List<CommonMeterialResp> meterialList) {
        boolean existWeiHuoAd = false;
        for (CommonMeterialResp materialResp : meterialList) {
            if (!AdConstant.MATERIAL_GET_TYPE_AUTO.equals(materialResp.getMaterialGetType()) || Objects.isNull(materialResp.getAdMaterialExtInfo())) {
                continue;
            }
            AdMaterialExtBo adMaterialExtInfo = materialResp.getAdMaterialExtInfo();
            if (AdConstant.MATERIAL_SOURCE_TYPE_WEIHUO.equals(adMaterialExtInfo.getMaterialSourceType())) {
                existWeiHuoAd = true;
                break;
            }
        }
        return existWeiHuoAd;
    }

    private List<WeiHuoInfoResp> getWeiHuoInfo(Long userId, Map<String, List<PullAdNonPopupResp>> finalGroupMap) {
        boolean existWeiHuoAd = false;
        for (Map.Entry<String, List<PullAdNonPopupResp>> entry : finalGroupMap.entrySet()) {
            List<PullAdNonPopupResp> symbolNonPopupList = entry.getValue();
            String selectedAdSymbol = symbolNonPopupList.get(0).getAdBaseInfo().getAdPositionSymbol();
            if (!AdConstant.WEIHUO_INFO_FLOW_AD_SYMBOL_LIST.contains(selectedAdSymbol)) {
                continue;
            }
            for (PullAdNonPopupResp pullAdNonPopupResp : symbolNonPopupList) {
                List<CommonMeterialResp> meterialList = pullAdNonPopupResp.getMeterialList();
                if (this.adIsWeiHuoFlag(meterialList)) {
                    existWeiHuoAd = true;
                    break;
                }
            }
            //为true直接跳出循环
            if (existWeiHuoAd) {
                break;
            }
        }
        if (!existWeiHuoAd) {
            return new ArrayList<>();
        }
        List<Long> thirdDivisionIds = this.getThirdDivisionIdsByMasterId(userId);
        if (CollectionUtils.isEmpty(thirdDivisionIds)) {
            return new ArrayList<>();
        }
        RecommendInfoOrderListRqt recommendInfoOrderListRqt = new RecommendInfoOrderListRqt();
        recommendInfoOrderListRqt.setMasterId(userId);
        recommendInfoOrderListRqt.setSearchType(AdConstant.ORDER_TYPE_WH);
        recommendInfoOrderListRqt.setThirdDivisionIds(thirdDivisionIds);
        recommendInfoOrderListRqt.setSize(orderMaxPullSize);
        log.info("查询尾货订单列表recommendInfoOrderListRqt:" + JSON.toJSONString(recommendInfoOrderListRqt));
        List<InfoOrderBaseComposite> compositeList = infoOrderListApi.recommendInfoOrderList(recommendInfoOrderListRqt);
        if (CollectionUtils.isEmpty(compositeList)) {
            return new ArrayList<>();
        }
        List<WeiHuoInfoResp> weiHuoInfoRespList = new ArrayList<>();
        compositeList.forEach(composite -> {
            WeiHuoInfoResp resp = new WeiHuoInfoResp();
            InfoOrderBase infoOrderBase = composite.getInfoOrderBase();
            InfoOrderExtraData infoOrderExtraData = composite.getInfoOrderExtraData();
            resp.setOrderNo(infoOrderBase.getOrderNo());
            resp.setOrderId(infoOrderBase.getOrderId());
            resp.setOrderStatus(infoOrderBase.getOrderStatus());
            //设置地址信息
            resp.setAddress(this.setAddressStarred(infoOrderExtraData.getAddress()));
            //设置订单商品信息
            List<InfoOrderGoodsComposite> infoOrderGoodsComposite = composite.getInfoOrderGoodsComposite();
            resp.setMasterInfoOrderGoods(this.setInfoOrderGoodsInfo(infoOrderGoodsComposite, infoOrderExtraData));
            //设置价格信息
            InfoOrderPriceInfoResp priceInfoResp = this.setInfoOrderPriceInfo(infoOrderExtraData.getGeneralInfo());
            resp.setExpectedPrice(priceInfoResp.getExpectedPrice());
            resp.setOriginalPrice(priceInfoResp.getOriginalPrice());
            resp.setOrderDetailUrl(String.format("%s/%s", masterOrderDetailUrl, infoOrderBase.getOrderId()));
            weiHuoInfoRespList.add(resp);
        });
        return weiHuoInfoRespList;
    }

    private InfoOrderPriceInfoResp setInfoOrderPriceInfo(String generalInfo) {
        // 解析订单价格信息
        if (com.wanshifu.framework.utils.StringUtils.isBlank(generalInfo)) {
            throw new BusinessException("general_info_is_null", "general_info_is_null");
        }

        return JSONObject.parseObject(generalInfo, InfoOrderPriceInfoResp.class);
    }

    /**
     * 设置订单商品信息
     *
     * @param infoOrderGoodsComposite
     * @return
     */
    private InfoOrderGoodsInfoResp setInfoOrderGoodsInfo(List<InfoOrderGoodsComposite> infoOrderGoodsComposite, InfoOrderExtraData infoOrderExtraData) {
        //设置订单商品信息
        InfoOrderGoodsInfoResp masterInfoOrderGoods = new InfoOrderGoodsInfoResp();

        if (CollectionUtils.isEmpty(infoOrderGoodsComposite)) {
            return masterInfoOrderGoods;
        }

        InfoOrderGoods infoOrderGoods = Optional.ofNullable(infoOrderGoodsComposite).filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(InfoOrderGoodsComposite::getInfoOrderGoods)
                .orElseThrow(() -> new BusinessException("get_order_goods_fail", "商品信息缺失"));

        List<InfoOrderGoodsAttachment> infoOrderGoodsAttachments = infoOrderGoodsComposite.stream().findFirst().map(InfoOrderGoodsComposite::getInfoOrderAttachment)
                .orElseThrow(() -> new BusinessException("get_order_goods_fail", "商品附件信息缺失"));

        InfoOrderGoodsAttachment goodsAttachmentInfo = infoOrderGoodsAttachments.stream().filter(infoOrderGoodsAttachment -> AdConstant.ATTACHMENT_TYPE_IMAGE.equals(infoOrderGoodsAttachment.getAttachmentType()))
                .findFirst().orElseThrow(() -> new BusinessException("get_order_goods_fail", "商品信息图片缺失"));

        String goodsInfo = infoOrderGoods.getGoodsInfo();
        if (com.wanshifu.framework.utils.StringUtils.isBlank(goodsInfo)) {
            throw new BusinessException("goods_info_is_null", "商品信息非法");
        }
        masterInfoOrderGoods = JSONObject.parseObject(goodsInfo, InfoOrderGoodsInfoResp.class);
        //图片转换
        if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(goodsAttachmentInfo.getAttachmentValue())) {
            Map<Long, String> urlsByAid = httpImageUtils.sendPostRequest(Collections.singletonList(goodsAttachmentInfo.getAttachmentValue()));
            masterInfoOrderGoods.setGoodsImageUrl(urlsByAid.get(Long.valueOf(goodsAttachmentInfo.getAttachmentValue())));
        }
        masterInfoOrderGoods.setGoodsTitle(infoOrderGoods.getGoodsName());
        masterInfoOrderGoods.setGoodsDesc(infoOrderGoods.getNote());

        if (null != infoOrderExtraData) {
            masterInfoOrderGoods.setMasterViewCount(infoOrderExtraData.getViewNumber());
        }

        return masterInfoOrderGoods;

    }

    /**
     * 详细地址里面的数字、门牌号替换成 *** 隐藏
     *
     * @param address
     * @return
     */
    private String setAddressStarred(String address) {
        if (com.wanshifu.framework.utils.StringUtils.isBlank(address)) {
            return "";
        }
        //把详细地址address字符里面的数字、门牌号替换成 ***
        String addressRegex = "[０-９\\d]+";
        return address.replaceAll(addressRegex, "*");
    }

    /**
     * 获取实时定位城市下所有三级地址id列表
     *
     * @param masterId
     * @return
     */
    private List<Long> getThirdDivisionIdsByMasterId(Long masterId) {
        //获取师傅信息
        GetMasterInfoRqt getMasterInfoRqt = new GetMasterInfoRqt();
        getMasterInfoRqt.setMasterId(masterId);
        GetMasterInfoResp masterInfo = commonQueryServiceApi.getMasterInfo(getMasterInfoRqt);
        Assert.notNull(masterInfo, "师傅信息查询为空");
        BigDecimal longitude = masterInfo.getCurrentLongitude();
        BigDecimal latitude = masterInfo.getCurrentLatitude();
        if (Objects.isNull(longitude) || Objects.isNull(latitude)) {
            return new ArrayList<>();
        }
        //根据最近一次经纬度定位
        GdGeoCodeQueryForm gdGeoCodeQueryForm = new GdGeoCodeQueryForm();
        gdGeoCodeQueryForm.setLatitude(latitude.toString());
        gdGeoCodeQueryForm.setLongitude(longitude.toString());
        GeoCodeVo geoCodeVo = gdGeoCodeApi.geoCodeV2(gdGeoCodeQueryForm);
        if (Objects.isNull(geoCodeVo) || Objects.isNull(geoCodeVo.getRegeocode()) || Objects.isNull(geoCodeVo.getRegeocode().getAddressComponent())) {
            log.error("获取定位信息查询为空，请开启定位后重试！");
            return new ArrayList<>();
        }
        String cityId = geoCodeVo.getRegeocode().getAddressComponent().getCityId();
        if (StringUtils.isEmpty(cityId)) {
            log.error("高德经纬度转换菜鸟地址失败，查询入参gdGeoCodeQueryForm:{}", JSON.toJSONString(gdGeoCodeQueryForm));
            return new ArrayList<>();
        }
        List<Address> cityAddressList = addressApi.getSubListByDivisionId(Long.valueOf(cityId));
        List<Long> thirdDivisionIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(cityAddressList)) {
            //没有三级情况
            thirdDivisionIdList.add(Long.valueOf(cityId));
        } else {
            thirdDivisionIdList.addAll(cityAddressList.stream().map(Address::getDivisionId).collect(Collectors.toList()));
        }
        return thirdDivisionIdList;
    }

    //选中了哪几个需要回调给平台,mq异步操作
    public void backAidList(List<Long> selectedAidList, Long userId) {
        SavePullNonPopRecordReq savePullNonPopRecordReq = new SavePullNonPopRecordReq();
        savePullNonPopRecordReq.setAdIdList(selectedAidList);
        savePullNonPopRecordReq.setUserId(userId);
        //改成异步线程
//        adPullSaveProducer.sendNonPopAdPullSave(savePullNonPopRecordReq);
        CompletableFuture.supplyAsync(() -> {
            adPullApi.saveAdUserPullNonPopRecord(savePullNonPopRecordReq);
            return null;
        });
    }

    /**
     * 弹窗广告回调平台，保存广告拉取记录
     */
    public void backPopAid(Long adId, Long userId, Long popEffectiveTimeId) {
        SavePullPopRecordReq savePullPopRecordReq = new SavePullPopRecordReq();
        savePullPopRecordReq.setAdId(adId);
        savePullPopRecordReq.setUserId(userId);
        savePullPopRecordReq.setPopEffectiveTimeId(popEffectiveTimeId);
//        adPullSaveProducer.sendPopAdPullSave(savePullPopRecordReq);
        CompletableFuture.supplyAsync(() -> {
            adPullApi.saveAdUserPullPopRecord(savePullPopRecordReq);
            return null;
        });
    }

    //判断是否在保护期
    public Integer isProtected(Date activityEndTime, Long activityId, Long userId, Integer protection, Long adId, String adPositionSymbol) {
        Date nowDate = new Date();
        boolean isProtectedBoolean = true;
        //如果过了结束时间，就过了保护期
        if (nowDate.after(activityEndTime)) {
            isProtectedBoolean = false;
        }
        GetProtectionRqt getProtectionRqt = new GetProtectionRqt();
        getProtectionRqt.setActivityId(activityId);
        getProtectionRqt.setUserId(userId);
        //order_top_banner 广告特殊处理:单次循环/持续循环且全部奖励领取达到上限时，解除保护
        if (AdConstant.NOT_CHECK_AD_HELP_UPPER_LIMIT.contains(adPositionSymbol)) {
            getProtectionRqt.setCheckBannerRewardUpperLimit(1);
        }
        Integer checkProtectedResult = activityBusinessServiceApi.checkProtected(getProtectionRqt);
        //全部任务领取过一次之后就不在保护期了
        if (checkProtectedResult == 1) {
            isProtectedBoolean = false;
        }
        //不调是否在保护期回调 只有在平台返回在保护期 业务判断不在保护期的时候就调用
        if (protection == 1 && !isProtectedBoolean) {
            AdHelpTimeReq adHelpTimeReq = new AdHelpTimeReq();
            adHelpTimeReq.setAdId(adId);
            adHelpTimeReq.setUserId(userId);
            adPullApi.updateHelpAdToUnvalid(adHelpTimeReq);
        }

        if (protection == 0) {
            isProtectedBoolean = false;
        }

        return isProtectedBoolean ? 1 : 0;

    }

    /**
     * 批量拉取非弹窗广告接口，过滤达到领奖上限的活动广告
     *
     * @param adInfos
     * @param userId
     * @param userClass
     */
    public List<GetUserAdResp.AdInfo> filterAdPositionBatchUpperLimit(List<GetUserAdResp.AdInfo> adInfos, Long userId, String userClass) {
        //暂时只对师傅作此处理
        if (!UserTypeEnum.MASTER.type.equals(userClass)) {
            return adInfos;
        }
        if (CollectionUtils.isEmpty(adInfos)) {
            return adInfos;
        }
        List<Long> activityIdList = adInfos.stream().filter(f -> f.getActivityId() != null && f.getActivityId() > 0).map(adInfo -> adInfo.getActivityId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(activityIdList)) {
            GetUserHasUpperLimitBatchRqt getUserHasUpperLimitBatchRqt = new GetUserHasUpperLimitBatchRqt();
            getUserHasUpperLimitBatchRqt.setUserId(userId);
            getUserHasUpperLimitBatchRqt.setActivityIdList(activityIdList);
            Map<Long, Integer> hasUpperLimitMap = activityBusinessServiceApi.getUserHasUpperLimitBatch(getUserHasUpperLimitBatchRqt);
            //返回空则不做过滤处理
            if (hasUpperLimitMap == null) {
                return adInfos;
            }
            //取未达到上限的活动以及非活动广告
            List<Long> checkedActivityList = hasUpperLimitMap.entrySet().stream().filter(f -> f.getValue() == 0).map(m -> m.getKey()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(checkedActivityList)) {
                adInfos = adInfos.stream().filter(f -> checkedActivityList.contains(f.getActivityId()) || f.getActivityId() == 0).collect(Collectors.toList());
            } else {
                adInfos = adInfos.stream().filter(f -> f.getActivityId() == null || f.getActivityId() == 0).collect(Collectors.toList());
            }
        }
        return adInfos;
    }

    /**
     * 用户活动任务奖励领取  todo 包装接口，预计前端无法提供cookie
     *
     * @return
     */
    @Override
    public List<RewardGiveApiResp> taskGiveReward(ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt) {

        try {
            ActivityTaskGiveRewardRqt activityTaskGiveRewardRqt = new ActivityTaskGiveRewardRqt();
            BeanUtils.copyProperties(activityTaskGiveRewardApiRqt, activityTaskGiveRewardRqt);
            activityTaskGiveRewardRqt.setUserId(activityTaskGiveRewardApiRqt.getUserId());
            activityTaskGiveRewardRqt.setUserActivityTaskIdList(Objects.nonNull(activityTaskGiveRewardApiRqt.getUserActivityTaskId()) ? Arrays.asList(activityTaskGiveRewardApiRqt.getUserActivityTaskId()) : null);
            activityTaskGiveRewardRqt.setActivityTaskIdList(Objects.nonNull(activityTaskGiveRewardApiRqt.getActivityTaskId()) ? Arrays.asList(activityTaskGiveRewardApiRqt.getActivityTaskId()) : null);
            //多选一奖励信息
            List<ActivityTaskGiveRewardApiRqt.CombineChooseRewardInfo> chooseRewardList = activityTaskGiveRewardApiRqt.getCombineChooseRewardInfoList();
            if (CollectionUtils.isNotEmpty(chooseRewardList)) {
                List<ChooseRewardInfoBo> boList = new ArrayList<>();
                for (ActivityTaskGiveRewardApiRqt.CombineChooseRewardInfo info : chooseRewardList) {
                    ChooseRewardInfoBo bo = new ChooseRewardInfoBo();
                    bo.setRewardSymbol(info.getRewardSymbol());
                    bo.setRewardExtraId(info.getRewardExtraId());
                    boList.add(bo);
                }
                activityTaskGiveRewardRqt.setChooseRewardInfoList(boList);
            }
            List<RewardGiveResp> rewardGiveRespList = activityBusinessServiceApi.taskGiveReward(activityTaskGiveRewardRqt);
            List<RewardGiveApiResp> rewardGiveRespApiList = new ArrayList<>();
            //添加判断为空
            if (CollectionUtils.isEmpty(rewardGiveRespList)) {
                return rewardGiveRespApiList;
            }

            //奖励类型为抽奖机会且url为空集合
            Map<Long, String> urlMap = new HashMap<>();
            List<Long> taskIdList = new ArrayList<>();
            List<RewardGiveResp> lotteryNotUrlList = rewardGiveRespList.stream().filter(r -> RewardSymbol.LOTTERY_TURNTABLE.type.equals(r.getRewardSymbol()) && r.getIsShowLanding() == 0).collect(Collectors.toList());
            //如该奖励类型是抽奖转盘且抽奖转盘对应的活动未设置落地页时，则匹配关联了该任务的“完成指定活动任务”对应的活动落地页，根据关联关系匹配到多个落地页时，取活动ID最小的落地页；
            if (CollectionUtils.isNotEmpty(lotteryNotUrlList)) {
                taskIdList = lotteryNotUrlList.stream().map(RewardGiveResp::getActivityTaskId).collect(Collectors.toList());
                GetUrlsByLinkTaskIdsRqt rqt = new GetUrlsByLinkTaskIdsRqt();
                rqt.setLinkActivityTaskIds(taskIdList);
                GetUrlsByLinkTaskIdsResp resp = activityBusinessServiceApi.getUrlsByLinkTaskIds(rqt);
                if (Objects.nonNull(resp) && Objects.nonNull(resp.getUrlMap()) && resp.getUrlMap().size() > 0) {
                    urlMap = resp.getUrlMap();
                }
            }

            for (RewardGiveResp rewardGiveResp : rewardGiveRespList) {
                RewardGiveApiResp newRewardGiveResp = new RewardGiveApiResp();
                if (rewardGiveResp != null) {
                    BeanUtils.copyProperties(rewardGiveResp, newRewardGiveResp);
                    newRewardGiveResp.setUrl("");
                    if (rewardGiveResp.getUserClass().equals(UserTypeEnum.MASTER.type)) {
                        if (newRewardGiveResp.getIsShowLanding() == 1) {
                            newRewardGiveResp.setUrl(rewardGiveResp.getUrl() + "&t=" + activityTaskGiveRewardApiRqt.getIsWeb());
                        } else if (taskIdList.contains(rewardGiveResp.getActivityTaskId())) {
                            String url = urlMap.get(rewardGiveResp.getActivityTaskId());
                            newRewardGiveResp.setUrl(StringUtils.isEmpty(url) ? "" : url + "&t=" + activityTaskGiveRewardApiRqt.getIsWeb());
                        }
                    }
                    rewardGiveRespApiList.add(newRewardGiveResp);
                }
            }
            return rewardGiveRespApiList;
        } catch (ApiAccessException e) {
            log.error("taskGiveReward领奖异常", e);
            throw new BusinessException("give_reward_fail", super.getErrorMsgNew(e.getMessage()));
        }

    }

    /**
     * 用户活动任务奖励领取-单个领取
     *
     * @return
     */
    @Override
    @ExceptionHandle(note = "用户活动任务奖励单个领取异常")
    public RewardTypeApiResp singleTaskGiveReward(ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt) {
        try {
            RewardTypeApiResp rewardTypeApiResp = new RewardTypeApiResp();
            GetActivityBaseRqt queryRqt = new GetActivityBaseRqt();
            queryRqt.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
            GetActivityBaseInfoResp activityBase = activityBackendServiceApi.getActivityBaseInfo(queryRqt);
            if (Objects.isNull(activityBase)) {
                rewardTypeApiResp.setReceiveStatus(0);
                rewardTypeApiResp.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
                rewardTypeApiResp.setReason("该活动不存在");
                return rewardTypeApiResp;
            }
            //手动发放校验领奖时间
            if (RewardGiveMethodEnum.MANUAL.type.equals(activityBase.getRewardGiveMethod())) {
                Date rewardGiveStartTime = activityBase.getRewardGiveStartTime();
                Date rewardGiveEndTime = activityBase.getRewardGiveEndTime();
                if (Objects.isNull(rewardGiveStartTime) || Objects.isNull(rewardGiveEndTime)) {
                    rewardTypeApiResp.setReceiveStatus(0);
                    rewardTypeApiResp.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
                    rewardTypeApiResp.setReason("该活动缺失奖励领取时间");
                    return rewardTypeApiResp;
                }
                if (rewardGiveStartTime.after(new Date())) {
                    rewardTypeApiResp.setReceiveStatus(0);
                    rewardTypeApiResp.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
                    rewardTypeApiResp.setReason(DateUtils.formatDate(rewardGiveStartTime, "MM月dd日 HH:mm") + "开始领奖");
                    return rewardTypeApiResp;
                }
                if (rewardGiveEndTime.before(new Date())) {
                    rewardTypeApiResp.setReceiveStatus(0);
                    rewardTypeApiResp.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
                    rewardTypeApiResp.setReason("很抱歉，领奖时间已结束！");
                    return rewardTypeApiResp;
                }
            }
            SimplePageInfo<MasterRewardListResp> rewardListApiRespSimplePageInfo = new SimplePageInfo<>();
            ActivityTaskGiveRewardRqt activityTaskGiveRewardRqt = new ActivityTaskGiveRewardRqt();
            //有批次id直接去领，无需再查询一遍
            if (Objects.nonNull(activityTaskGiveRewardApiRqt.getTaskExecuteBatchId()) && activityTaskGiveRewardApiRqt.getTaskExecuteBatchId() > 0) {
                BeanUtils.copyProperties(activityTaskGiveRewardApiRqt, activityTaskGiveRewardRqt);
            } else {
                //无批次，获取第一个去领取
                MasterRewardListRqt masterRewardListRqt = new MasterRewardListRqt();
                BeanUtils.copyProperties(activityTaskGiveRewardApiRqt, masterRewardListRqt);
                masterRewardListRqt.setMasterId(activityTaskGiveRewardApiRqt.getUserId());
                masterRewardListRqt.setPageSize(1);
                try {
                    rewardListApiRespSimplePageInfo = masterActivityBusinessServiceApi.rewardList(masterRewardListRqt);
                } catch (BusinessException businessException) {
                    rewardTypeApiResp.setReceiveStatus(0);
                    rewardTypeApiResp.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
                    rewardTypeApiResp.setReason(super.getErrorMsg(businessException.getMessage()));
                    return rewardTypeApiResp;
                }
                if (CollectionUtils.isEmpty(rewardListApiRespSimplePageInfo.getList())) {
                    throw new BusException("无待领奖记录");
                }
                MasterRewardListResp masterRewardListResp = rewardListApiRespSimplePageInfo.getList().get(0);
                BeanUtils.copyProperties(masterRewardListResp, activityTaskGiveRewardRqt);
            }
            activityTaskGiveRewardRqt.setUserId(activityTaskGiveRewardApiRqt.getUserId());
            activityTaskGiveRewardRqt.setUserClass(activityTaskGiveRewardApiRqt.getUserClass());
            activityTaskGiveRewardRqt.setUserActivityTaskIdList(Objects.nonNull(activityTaskGiveRewardApiRqt.getUserActivityTaskId()) ? Arrays.asList(activityTaskGiveRewardApiRqt.getUserActivityTaskId()) : null);
            activityTaskGiveRewardRqt.setActivityTaskIdList(Objects.nonNull(activityTaskGiveRewardApiRqt.getActivityTaskId()) ? Arrays.asList(activityTaskGiveRewardApiRqt.getActivityTaskId()) : null);
            List<RewardGiveResp> rewardGiveRespList = null;
            try {
                rewardGiveRespList = activityBusinessServiceApi.taskGiveReward(activityTaskGiveRewardRqt);
            } catch (Exception e) {
                log.warn(e.getMessage());
                rewardTypeApiResp.setReceiveStatus(0);
                rewardTypeApiResp.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
                rewardTypeApiResp.setReason(super.getErrorMsg2(e.getMessage()));
                return rewardTypeApiResp;
            }
            RewardGiveResp rewardGiveResp = rewardGiveRespList.get(0);
            if (rewardGiveResp == null) {
                rewardTypeApiResp.setReceiveStatus(0);
                rewardTypeApiResp.setActivityId(activityTaskGiveRewardApiRqt.getActivityId());
                return rewardTypeApiResp;
            }
            //拆开红包奖励类型
            if (MasterRewardTypeEnum.BONUS.getActivityType().equals(rewardGiveResp.getRewardSymbol())) {
                BonusOpenRespBean bonusOpenRespBean = bonusServiceApi.open(rewardGiveResp.getExtraId().intValue(), rewardGiveResp.getUserId(), rewardGiveResp.getUserClass());
            }
            rewardTypeApiResp.setReceiveStatus(1);
            rewardTypeApiResp.setActivityId(rewardGiveResp.getActivityId());
            rewardTypeApiResp.setRewardType(rewardGiveResp.getRewardSymbol());
            rewardTypeApiResp.setRewardTypeName(RewardSymbol.getCn(rewardGiveResp.getRewardSymbol()));
            rewardTypeApiResp.setRewardUnit(RewardSymbol.getSimpleAlias(rewardGiveResp.getRewardSymbol()));
            rewardTypeApiResp.setRewardValue(rewardGiveResp.getRewardGiveValue());
            rewardTypeApiResp.setActivityIdName(rewardGiveResp.getActivityName());
            rewardTypeApiResp.setIsShowLanding(rewardGiveResp.getIsShowLanding());
            //添加批次id返回。
            rewardTypeApiResp.setTaskExecuteBatchId(rewardGiveResp.getTaskExecuteBatchId());
            rewardTypeApiResp.setUrl(super.returnLandingPageUrl(rewardGiveResp.getActivityModel(), rewardGiveResp.getActivityId()));
            //未获取到活动落地页标题时，取活动标签配置的活动标题，跳转地址同理
            if (StringUtils.isEmpty(rewardTypeApiResp.getActivityIdName()) && Objects.nonNull(activityBase.getActivityLabelInfo())) {
                rewardTypeApiResp.setActivityIdName(activityBase.getActivityLabelInfo().getActivityTitle());
                rewardTypeApiResp.setUrl(activityBase.getActivityLabelInfo().getButtonForwardUrl());
            }
            rewardListApiRespSimplePageInfo = null;
            activityTaskGiveRewardRqt = null;
            rewardGiveRespList = null;
            rewardGiveResp = null;

            return rewardTypeApiResp;
        } catch (ApiAccessException e) {
            throw new BusException("give_reward_fail", super.getErrorMsg(e.getMessage()));
        }
    }


    /**
     * 商家发布订单活动标签接口
     *
     * @param orderTagApiRqt
     * @return
     */

    @Override
    public MerchantPublishOrderTagApiResp getPublishOrderTag(MerchantPublishOrderTagApiRqt orderTagApiRqt) {
        MerchantPublishOrderTagRqt req = new MerchantPublishOrderTagRqt();
        BeanUtils.copyProperties(orderTagApiRqt, req);
        req.setUserId(orderTagApiRqt.getUserId());
        req.setOrderFrom(LaunchPortEnum.MERCHANT_WEB.type.equals(orderTagApiRqt.getLaunchPort()) ? "site" : "android");
        if (CollectionUtils.isEmpty(orderTagApiRqt.getServeIdList())) {
            if (CollectionUtils.isEmpty(orderTagApiRqt.getGoodsIdList()) || orderTagApiRqt.getServeTypeId() == null) {
                throw new BusException("入参信息(serveTypeId+goodsIdList)或者serveId两者不能同时为空！");
            }
            List<GoodsIdAndServeTypeIdReq> reqList = new ArrayList<>();
            orderTagApiRqt.getGoodsIdList().forEach(goodsId -> {
                GoodsIdAndServeTypeIdReq goodsReq = new GoodsIdAndServeTypeIdReq();
                goodsReq.setGoodsId(goodsId);
                goodsReq.setServeTypeId(orderTagApiRqt.getServeTypeId());
                reqList.add(goodsReq);
            });
            List<ServeBaseInfoResp> serveInfoList = serveServiceApi.getServeBaseInfoBatchByGoodsIdAndServeTypeId(reqList);
            if (CollectionUtils.isEmpty(serveInfoList)) {
                log.error("查询order-config-service接口获取服务信息异常，入参：{}" + JSON.toJSONString(reqList));
                return null;
            }
            req.setServeIdList(serveInfoList.stream().map(ServeBaseInfoResp::getServeId).collect(Collectors.toList()));
        }
        //过滤serveId为null的脏数据
        if (CollectionUtils.isEmpty(req.getServeIdList())) {
            log.error("入参serveId为null，无效请求orderTagApiRqt:{}", JSON.toJSONString(orderTagApiRqt));
            return null;
        }
        if (req.getServeIdList().contains(null)) {
            log.error("入参serveId有为null数据，orderTagApiRqt:{}", JSON.toJSONString(orderTagApiRqt));
            return null;
        }
        MerchantPublishOrderTagResp apiResp = null;
        try {
            apiResp = merchantActivityServiceApi.getPublishOrderTag(req);
        } catch (Exception e) {
            log.error("调用平台商家发布订单活动标签接口异常入参:{}" + JSON.toJSONString(req) + "报错信息：{}" + e.getMessage());
        }
        if (apiResp == null) {
            return null;
        }
        MerchantPublishOrderTagApiResp resultResp = new MerchantPublishOrderTagApiResp();
        List<MerchantPublishOrderTagApiResp.MatchActivityTask> resultActivityTaskIds = new ArrayList<>();
        BeanUtils.copyProperties(apiResp, resultResp);
        for (MerchantPublishOrderTagResp.MatchActivityTask taskVo : apiResp.getActivityTaskIds()) {
            MerchantPublishOrderTagApiResp.MatchActivityTask resultTask = new MerchantPublishOrderTagApiResp.MatchActivityTask();
            BeanUtils.copyProperties(taskVo, resultTask);
            resultTask.setMissTargetNum(taskVo.getTargetNum() - taskVo.getFinishNum());
            resultActivityTaskIds.add(resultTask);
        }
        resultResp.setActivityTaskIds(resultActivityTaskIds);
        return resultResp;
    }

    /**
     * 根据实验id查询是否在使用
     *
     * @param isUsedByTryIdRqt
     */
    @Override
    public List<IsUsedByTryIdResp> isUsedByTryId(IsUsedByTryIdRqt isUsedByTryIdRqt) {

        try {
            IsUsedByTryIdServiceRqt isUsedByTryIdServiceRqt = new IsUsedByTryIdServiceRqt();
            isUsedByTryIdServiceRqt.setTryId(isUsedByTryIdRqt.getTryId());
            List<IsUsedByTryIdServiceResp> isUsedByTryId = activityBackendServiceApi.getIsUsedByTryId(isUsedByTryIdServiceRqt);

            List<IsUsedByTryIdResp> result = new ArrayList<>();
            for (IsUsedByTryIdServiceResp isUsedByTryIdServiceResp : isUsedByTryId) {
                IsUsedByTryIdResp isUsedByTryIdResp = new IsUsedByTryIdResp();
                isUsedByTryIdResp.setActivityId(isUsedByTryIdServiceResp.getActivityId());
                isUsedByTryIdResp.setActivityName(isUsedByTryIdServiceResp.getActivityName());
                result.add(isUsedByTryIdResp);
            }
            return result;
        } catch (Exception e) {
            throw new BusException("判断实验状态失败");
        }
    }

    /**
     * yonghu 多选一活动报名
     *
     * @param activityApplyApiChoiceRqt
     * @return
     */
    @Override
    public Integer applyChoice(ActivityApplyApiChoiceRqt activityApplyApiChoiceRqt) {
        ActivityApplyChoiceRqt activityApplyRqt = new ActivityApplyChoiceRqt();
        activityApplyRqt.setApplyTime(new Date());
        activityApplyRqt.setUserId(activityApplyApiChoiceRqt.getUserId());
        activityApplyRqt.setUserClass(activityApplyApiChoiceRqt.getUserClass());
        activityApplyRqt.setActivityTaskIds(activityApplyApiChoiceRqt.getActivityTaskIds());
        activityApplyRqt.setActivityIds(activityApplyApiChoiceRqt.getActivityIds());

        try {

            int apply = activityBusinessServiceApi.applyChoice(activityApplyRqt);
            if (apply == 1) {
                if (activityApplyApiChoiceRqt.getUserClass().equals(UserTypeEnum.MERCHANT.type)) {
                    ActivityTaskRunRqt activityTaskRunRqt = ActivityTaskRunRqt.builder().userClass(activityApplyApiChoiceRqt.getUserClass())
                            .taskSymbol(TaskSymbolEnum.VISIT.taskSymbol)
                            .launchPorts(activityApplyApiChoiceRqt.getIsWeb() == 1 ? LaunchPortEnum.MERCHANT_WEB.type : LaunchPortEnum.MERCHANT_APP.type)
                            .userId(activityApplyApiChoiceRqt.getUserId())
                            .executeTime(new Date())
                            .extraType(TaskSymbolEnum.VISIT.taskSymbol).extraNo(String.valueOf(System.currentTimeMillis())).build();

                    activityBusinessServiceApi.taskRun(activityTaskRunRqt);
                }
                //异步线程,目前没有批量接口，所以循环调用。
                CompletableFuture<Void> f = CompletableFuture.supplyAsync(() -> {
                    ActivityDetailBatchRqt activityDetailBatchRqt = new ActivityDetailBatchRqt();
                    activityDetailBatchRqt.setActivityIds(activityApplyApiChoiceRqt.getActivityIds());
                    List<ActivityBaseInfoResp> getActivityBaseInfoBatch = activityBusinessServiceApi.getActivityBaseInfoBatch(activityDetailBatchRqt);
                    if (CollectionUtils.isEmpty(getActivityBaseInfoBatch)) {
                        throw new BusException("查询活动信息异常");
                    }
                    for (Long activityId : activityApplyApiChoiceRqt.getActivityIds()) {
                        TaskReceiveToAdHelpReq taskReciveToAdHelpReq = new TaskReceiveToAdHelpReq();
                        taskReciveToAdHelpReq.setActivityId(activityId);
                        taskReciveToAdHelpReq.setUserClass(activityApplyApiChoiceRqt.getUserClass());
                        taskReciveToAdHelpReq.setUserId(activityApplyApiChoiceRqt.getUserId());
                        taskReciveToAdHelpReq.setReceiveTime(new Date());
                        taskReciveToAdHelpReq.setTriggerTime(new Date());
                        ActivityBaseInfoResp activityBaseInfo = getActivityBaseInfoBatch.stream().filter(m -> m.getActivityId().equals(activityId)).findFirst().orElseThrow(() -> new BusException("获取活动信息异常！"));
                        taskReciveToAdHelpReq.setActivityEndTime(activityBaseInfo.getActivityEndTime());
                        taskReciveToAdHelpReq.setActivityStartTime(activityBaseInfo.getActivityStartTime());

                        adPullApi.taskReciveToAdHelp(taskReciveToAdHelpReq);
                    }
                    return null;
                });
            }
            return apply;
        } catch (ApiAccessException e) {
            throw new BusinessException("sign_fail", super.getErrorMsg(e.getRetMesg()));
        }
    }

    /**
     * yonghu 多选一活动报名
     *
     * @param activityApplyApiChoiceRqt
     * @return todo 切出自定义对象 ActivityApplyApiChoiceRqt  报未登录异常
     */
    @Override
    public Integer applyChoiceNew(ActivityApplyApiChoiceOfRqt activityApplyApiChoiceRqt) {
        ActivityApplyChoiceRqt activityApplyRqt = new ActivityApplyChoiceRqt();
        Long userId = super.getUserIdByTokenAndUserClass(activityApplyApiChoiceRqt.getToken(), activityApplyApiChoiceRqt.getUserClass());
        activityApplyRqt.setApplyTime(new Date());
        activityApplyRqt.setUserId(userId);
        activityApplyRqt.setUserClass(activityApplyApiChoiceRqt.getUserClass());
        activityApplyRqt.setActivityTaskIds(activityApplyApiChoiceRqt.getActivityTaskIds());
        activityApplyRqt.setActivityIds(activityApplyApiChoiceRqt.getActivityIds());

        try {
            int apply = activityBusinessServiceApi.applyChoice(activityApplyRqt);
            if (apply == 1) {
                if (activityApplyApiChoiceRqt.getUserClass().equals(UserTypeEnum.MERCHANT.type)) {
                    ActivityTaskRunRqt activityTaskRunRqt = ActivityTaskRunRqt.builder().userClass(activityApplyApiChoiceRqt.getUserClass())
                            .taskSymbol(TaskSymbolEnum.VISIT.taskSymbol)
                            .launchPorts(activityApplyApiChoiceRqt.getIsWeb() == 1 ? LaunchPortEnum.MERCHANT_WEB.type : LaunchPortEnum.MERCHANT_APP.type)
                            .userId(userId)
                            .executeTime(new Date())
                            .extraType(TaskSymbolEnum.VISIT.taskSymbol).extraNo(String.valueOf(System.currentTimeMillis())).build();

                    activityBusinessServiceApi.taskRun(activityTaskRunRqt);
                }
                //异步线程,目前没有批量接口，所以循环调用。
                CompletableFuture<Void> f = CompletableFuture.supplyAsync(() -> {
                    ActivityDetailBatchRqt activityDetailBatchRqt = new ActivityDetailBatchRqt();
                    activityDetailBatchRqt.setActivityIds(activityApplyApiChoiceRqt.getActivityIds());
                    List<ActivityBaseInfoResp> getActivityBaseInfoBatch = activityBusinessServiceApi.getActivityBaseInfoBatch(activityDetailBatchRqt);
                    if (CollectionUtils.isEmpty(getActivityBaseInfoBatch)) {
                        throw new BusException("查询活动信息异常");
                    }
                    for (Long activityId : activityApplyApiChoiceRqt.getActivityIds()) {
                        TaskReceiveToAdHelpReq taskReciveToAdHelpReq = new TaskReceiveToAdHelpReq();
                        taskReciveToAdHelpReq.setActivityId(activityId);
                        taskReciveToAdHelpReq.setUserClass(activityApplyApiChoiceRqt.getUserClass());
                        taskReciveToAdHelpReq.setUserId(userId);
                        taskReciveToAdHelpReq.setReceiveTime(new Date());
                        taskReciveToAdHelpReq.setTriggerTime(new Date());
                        ActivityBaseInfoResp activityBaseInfo = getActivityBaseInfoBatch.stream().filter(m -> m.getActivityId().equals(activityId)).findFirst().orElseThrow(() -> new BusException("获取活动信息异常！"));
                        taskReciveToAdHelpReq.setActivityEndTime(activityBaseInfo.getActivityEndTime());
                        taskReciveToAdHelpReq.setActivityStartTime(activityBaseInfo.getActivityStartTime());
                        adPullApi.taskReciveToAdHelp(taskReciveToAdHelpReq);
                    }
                    return null;
                });
            }
            return apply;
        } catch (ApiAccessException e) {
            throw new BusinessException("sign_fail", super.getErrorMsg(e.getRetMesg()));
        }
    }

    /**
     * 家庭活动报名
     *
     * @param activityApplyCustomerRqt
     * @return
     */
    @Override
    public Integer applyCustomer(ActivityApplyCustomerApiRqt activityApplyCustomerRqt) {
        ActivityApplyCustomerRqt activityApplyCustomerRqt1 = new ActivityApplyCustomerRqt();
        Long userId = super.getUserIdByTokenAndUserClass(activityApplyCustomerRqt.getToken(), activityApplyCustomerRqt.getUserClass());

        activityApplyCustomerRqt1.setApplyTime(new Date());
        activityApplyCustomerRqt1.setUserId(userId);
//        List<Long> activityIds = activityApplyCustomerRqt.getApplyList().stream().map(ActivityApplyCustomerApiRqt.ApplyBo::getActivityId).collect(Collectors.toList());
//
//        activityApplyCustomerRqt1.setActivityIds(activityIds);
        List<Long> activityIds = Arrays.asList(activityApplyCustomerRqt.getActivityId());
        activityApplyCustomerRqt1.setActivityIds(activityIds);
        activityApplyCustomerRqt1.setUserClass(activityApplyCustomerRqt.getUserClass());

        try {

            int apply = activityBusinessServiceApi.applyCustomer(activityApplyCustomerRqt1);
            if (apply == 1) {
                //调用家庭访问任务。
                if (activityApplyCustomerRqt.getUserClass().equals(UserTypeEnum.CUSTOMER.type)) {
                    ActivityTaskRunRqt activityTaskRunRqt = ActivityTaskRunRqt.builder().userClass(activityApplyCustomerRqt.getUserClass())
                            .taskSymbol(TaskSymbolEnum.CUSTOMER_VISIT.taskSymbol)
                            .launchPorts(activityApplyCustomerRqt.getIsWeb() == 1 ? LaunchPortEnum.CUSTOMER_APPLET.type : LaunchPortEnum.CUSTOMER_APP.type)
                            .userId(userId)
                            .executeTime(new Date())
                            .extraType(TaskSymbolEnum.CUSTOMER_VISIT.taskSymbol).extraNo(String.valueOf(System.currentTimeMillis())).build();

                    activityBusinessServiceApi.taskRun(activityTaskRunRqt);
                }

                //异步线程,目前没有批量接口，所以循环调用。
                CompletableFuture<Void> f = CompletableFuture.supplyAsync(() -> {
                    ActivityDetailBatchRqt activityDetailBatchRqt = new ActivityDetailBatchRqt();
                    activityDetailBatchRqt.setActivityIds(activityIds);
                    List<ActivityBaseInfoResp> getActivityBaseInfoBatch = activityBusinessServiceApi.getActivityBaseInfoBatch(activityDetailBatchRqt);
                    if (CollectionUtils.isEmpty(getActivityBaseInfoBatch)) {
                        throw new BusException("查询活动信息异常");
                    }
                    for (Long activityId : activityIds) {
                        TaskReceiveToAdHelpReq taskReciveToAdHelpReq = new TaskReceiveToAdHelpReq();
                        taskReciveToAdHelpReq.setActivityId(activityId);
                        taskReciveToAdHelpReq.setUserClass(activityApplyCustomerRqt.getUserClass());
                        taskReciveToAdHelpReq.setUserId(userId);
                        taskReciveToAdHelpReq.setReceiveTime(new Date());
                        taskReciveToAdHelpReq.setTriggerTime(new Date());
                        ActivityBaseInfoResp activityBaseInfo = getActivityBaseInfoBatch.stream().filter(m -> m.getActivityId().equals(activityId)).findFirst().orElseThrow(() -> new BusException("获取活动信息异常！"));
                        taskReciveToAdHelpReq.setActivityEndTime(activityBaseInfo.getActivityEndTime());
                        taskReciveToAdHelpReq.setActivityStartTime(activityBaseInfo.getActivityStartTime());
                        adPullApi.taskReciveToAdHelp(taskReciveToAdHelpReq);
                    }
                    return null;
                });
                //异步线程，调用接口，标记广告为已经报名成功。添加判断，如果没有广告id，则为普通落地页报名
                if (null != activityApplyCustomerRqt.getAdId()) {
                    CompletableFuture<Void> f1 = CompletableFuture.supplyAsync(() -> {
                        AddAdStateInfoReq addAdStateInfoReq = new AddAdStateInfoReq();
                        addAdStateInfoReq.setAdId(activityApplyCustomerRqt.getAdId());
                        addAdStateInfoReq.setUserClass(activityApplyCustomerRqt.getUserClass());
                        addAdStateInfoReq.setUserId(userId);
                        addAdStateInfoReq.setAdPositionSymbol(activityApplyCustomerRqt.getAdPositionSymbol());
                        addAdStateInfoReq.setPullSource(activityApplyCustomerRqt.getPullSource());
                        addAdStateInfoReq.setInfoId(String.valueOf(activityApplyCustomerRqt.getActivityId()));
                        addAdStateInfoReq.setInfoType("activity_id");
                        adBaseInfoApi.addAdStateInfo(addAdStateInfoReq);
                        return null;
                    });
                }

            }
            return apply;
        } catch (ApiAccessException e) {
            throw new BusinessException("sign_fail", super.getErrorMsg(e.getMessage()));
        }
    }

    @Override
    public List<RewardGiveApiResp> taskGiveRewardOf(ActivityTaskGiveRewardApiOfRqt activityTaskGiveRewardApiRqt) {
        try {
            Long userId = super.getUserIdByTokenAndUserClass(activityTaskGiveRewardApiRqt.getToken(), activityTaskGiveRewardApiRqt.getUserClass());

            ActivityTaskGiveRewardRqt activityTaskGiveRewardRqt = new ActivityTaskGiveRewardRqt();
            BeanUtils.copyProperties(activityTaskGiveRewardApiRqt, activityTaskGiveRewardRqt);
            activityTaskGiveRewardRqt.setUserActivityTaskIdList(Objects.nonNull(activityTaskGiveRewardApiRqt.getUserActivityTaskId()) ? Arrays.asList(activityTaskGiveRewardApiRqt.getUserActivityTaskId()) : null);
            activityTaskGiveRewardRqt.setActivityTaskIdList(Objects.nonNull(activityTaskGiveRewardApiRqt.getActivityTaskId()) ? Arrays.asList(activityTaskGiveRewardApiRqt.getActivityTaskId()) : null);
            activityTaskGiveRewardRqt.setUserId(userId);
            List<RewardGiveResp> rewardGiveRespList = activityBusinessServiceApi.taskGiveReward(activityTaskGiveRewardRqt);
            List<RewardGiveApiResp> rewardGiveRespApiList = new ArrayList<>();
            //添加判断为空
            if (CollectionUtils.isEmpty(rewardGiveRespList)) {
                return rewardGiveRespApiList;
            }
            for (RewardGiveResp rewardGiveResp : rewardGiveRespList) {
                RewardGiveApiResp newRewardGiveResp = new RewardGiveApiResp();
                if (rewardGiveResp != null) {
                    BeanUtils.copyProperties(rewardGiveResp, newRewardGiveResp);

                    if (rewardGiveResp.getUserClass().equals(UserTypeEnum.MASTER.type)) {
                        if (newRewardGiveResp.getIsShowLanding() == 0) {
                            //如果没有落地页就没有落地页地址
                            newRewardGiveResp.setUrl("");
                        } else {
                            newRewardGiveResp.setUrl(super.returnLandingPageUrl(rewardGiveResp.getActivityModel(), rewardGiveResp.getActivityId()));
                        }
                    }
                    rewardGiveRespApiList.add(newRewardGiveResp);
                }
            }
            return rewardGiveRespApiList;
        } catch (ApiAccessException e) {
            log.error("taskGiveRewardOf error", e);
            throw new BusinessException("give_reward_fail", e.getRetMesg());
        }
    }

    /**
     * 获取列表广告
     *
     * @param getUserAdRqt
     * @param token
     * @param tokenApp
     * @param signature
     * @return
     */
    @Override
    public GetUserAdResp getListAd(GetUserAdRqt getUserAdRqt, String token, String tokenApp, String signature) {
        if (!ListAdSymbolEnum.include(getUserAdRqt.getAdPositionSymbol())) {
            return null;
        }
        //判断是否是app访问
        boolean isApp = !StringUtils.isEmpty(tokenApp);
        //appEnable是否允许app访问，true是，false否
        if (!appEnable && isApp) {
            return null;
        }
        String isWeb = "app";
        if (!StringUtils.isEmpty(token)) {
            isWeb = "web";
        }
        //签名统一赋值
        String autograph;
        if (getUserAdRqt.getUserClass().equals(UserTypeEnum.MASTER.type)) {
            autograph = signature;
        } else {
            autograph = isApp ? tokenApp : token;
        }
        //获取用户信息
        Long userId = redisExUtil.checkLoginStatus(autograph, getUserAdRqt.getUserClass());
        if (userId == null || userId == 0L) {
            //非登陆情况下的直接返空
            return null;
        }
        //返回结果对象
        GetUserAdResp getUserAdResp = new GetUserAdResp();
        try {
            //组装拉取广告入参
            PullListAdReq pullListAdReq = new PullListAdReq();
            pullListAdReq.setPort(getUserAdRqt.getLaunchPort());
            pullListAdReq.setTagList(this.getTagListFromBigData(getUserAdRqt.getUserClass(), userId, getUserAdRqt.getLaunchPort()));
            pullListAdReq.setUserClass(getUserAdRqt.getUserClass());
            pullListAdReq.setUserId(userId);
            pullListAdReq.setAdPositionSymbol(getUserAdRqt.getAdPositionSymbol());
            //调用接口返回list
            List<PullAdNonPopupResp> pullAdNonPopupRespList = adPullApi.pullAllListAd(pullListAdReq);

            List<PullAdNonPopupResp> pullAdNonPopupResp = new ArrayList<>();
            //选中的aid list
            List<Long> selectedAidList = new ArrayList<>();
            //活动信息
            List<ActivityBaseResp> activityBaseList = new ArrayList<>();
            //过滤广告
            this.getAdPositionFilterAdId(pullAdNonPopupRespList, selectedAidList, pullAdNonPopupResp, userId, getUserAdRqt.getUserClass(), activityBaseList, "");
            /**
             * 组装出参信息:广告信息+活动信息+图片信息
             */
            List<GetUserAdResp.AdInfo> getUserAdRespList = this.packageAdPositionOutput(pullAdNonPopupResp, userId, activityBaseList);
            //列表广告排序,从大到小排序
            getUserAdRespList = this.listAdSort(getUserAdRespList);
            getUserAdResp.setAdInfo(getUserAdRespList);
            return getUserAdResp;
        } catch (ApiAccessException e) {
            log.warn("拉取广告失败,请求参数={},错误信息={}", JSON.toJSONString(getUserAdRqt), e.getMessage());
            return null;
        }
    }

    /**
     * 多状态banner广告位专用批量领奖接口
     *
     * @param rewardGiveBannerAdApiRqt
     * @return
     */
    @Override
    public List<RewardGiveBannerAdApiResp> taskGiveRewardForMultiBannerAd(RewardGiveBannerAdApiRqt rewardGiveBannerAdApiRqt) {
        GetActivityTaskTargetDetailRqt getActivityDetailRqt = new GetActivityTaskTargetDetailRqt();
        BeanUtils.copyProperties(rewardGiveBannerAdApiRqt, getActivityDetailRqt);
        List<GetActivityTaskTargetDetailResp.ActivityTaskTarget> activityTaskTargetList = new ArrayList<>();
        try {
            GetActivityTaskTargetDetailResp targetDetailResp = activityBusinessServiceApi.getActivityTaskTargetDetail(getActivityDetailRqt);
            if (Objects.isNull(targetDetailResp) || CollectionUtils.isEmpty(targetDetailResp.getActivityTaskTargetList())) {
                throw new BusinessException("获取任务配置信息异常，请稍后重试！");
            }
            activityTaskTargetList = targetDetailResp.getActivityTaskTargetList();
        } catch (ApiAccessException e) {
            throw new BusinessException("give_reward_fail", e.getRetMesg());
        }
        try {
            ActivityTaskGiveRewardRqt activityTaskGiveRewardRqt = new ActivityTaskGiveRewardRqt();
            BeanUtils.copyProperties(rewardGiveBannerAdApiRqt, activityTaskGiveRewardRqt);
            activityTaskGiveRewardRqt.setUserId(rewardGiveBannerAdApiRqt.getUserId());
            List<RewardGiveResp> rewardGiveRespList = activityBusinessServiceApi.taskGiveReward(activityTaskGiveRewardRqt);
            List<RewardGiveBannerAdApiResp> rewardGiveRespApiList = new ArrayList<>();
            //添加判断为空
            if (CollectionUtils.isEmpty(rewardGiveRespList)) {
                throw new BusinessException("give_reward_fail", "很遗憾，奖励已抢光！");
            }
            //只获取领取成功的奖励
            rewardGiveRespList = rewardGiveRespList.stream().filter(f -> Objects.nonNull(f.getRewardGiveResultState()) && f.getRewardGiveResultState() == 1).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(rewardGiveRespList)) {
                throw new BusinessException("give_reward_fail", "很遗憾，奖励已抢光！");
            }
            //优惠券查询:加try catch防止报错不展示领取信息
            Map<Long, VoucherRewardBo> voucherBoMap = new HashMap<>();
            Map<Long, VoucherRewardBo> voucherPackBoMap = new HashMap<>();
            try {
                List<Integer> voucherIdList = rewardGiveRespList.stream().filter(f -> RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol())).map(m -> m.getRewardExtraId().intValue()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(voucherIdList)) {
                    voucherBoMap = super.getOnlyVoucherBatchGetReward(voucherIdList);
                }
                List<Integer> voucherPackIdList = rewardGiveRespList.stream().filter(f -> RewardSymbol.VOUCHER_PACK.type.equals(f.getRewardSymbol())).map(m -> m.getRewardExtraId().intValue()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(voucherPackIdList)) {
                    voucherPackBoMap = super.batchQueryVoucherPackValue(voucherPackIdList);
                }
            } catch (Exception e) {
                log.error("调用现金券服务查询异常：" + e.getMessage());
            }
            for (RewardGiveResp rewardGiveResp : rewardGiveRespList) {
                RewardGiveBannerAdApiResp newRewardGiveResp = new RewardGiveBannerAdApiResp();
                if (rewardGiveResp != null) {
                    BeanUtils.copyProperties(rewardGiveResp, newRewardGiveResp);
                    GetActivityTaskTargetDetailResp.ActivityTaskTarget activityTaskTarget = activityTaskTargetList.stream().filter(f -> f.getActivityTaskId().equals(rewardGiveResp.getActivityTaskId())).findFirst().orElseThrow(() -> new BusException("未匹配到对应任务目标"));
                    newRewardGiveResp.setTargetText(this.returnTargetText(activityTaskTarget));
                    RewardValueAndNameBo rewardValueAndNameBo = this.returnGiveValueAndName(rewardGiveResp, voucherBoMap, voucherPackBoMap);
                    newRewardGiveResp.setRewardGiveValue(rewardValueAndNameBo.getRewardValue());
                    newRewardGiveResp.setRewardName(rewardValueAndNameBo.getRewardName());
                    rewardGiveRespApiList.add(newRewardGiveResp);
                }
            }
            return rewardGiveRespApiList;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("give_reward_fail", "很遗憾，奖励已抢光！");
        }
    }

    /**
     * 获取商家充值活动的优惠券列表信息
     *
     * @param getPayPageVoucherListApiRqt
     * @return
     */
    @Override
    public GetPayPageVoucherListApiResp getPayPageVoucherList(GetPayPageVoucherListApiRqt getPayPageVoucherListApiRqt) {
        //获取用户登陆信息
        Long userId = super.getUserIdByTokenAndUserClass(getPayPageVoucherListApiRqt.getToken(), getPayPageVoucherListApiRqt.getUserClass());
        if (null == userId) {
            throw new BusException("请先登录");
        }
        GetPayPageVoucherListApiResp getPayPageVoucherListApiResp = new GetPayPageVoucherListApiResp();
        GetPayPageVoucherListResp getPayPageVoucherListResp = null;
        try {
            GetPayPageVoucherListRqt getPayPageVoucherListRqt = new GetPayPageVoucherListRqt();
            getPayPageVoucherListRqt.setUserClass(getPayPageVoucherListApiRqt.getUserClass());
            getPayPageVoucherListRqt.setUserId(userId);
            getPayPageVoucherListResp = activityBusinessServiceApi.getPayPageVoucherList(getPayPageVoucherListRqt);
        } catch (ApiAccessException e) {
            throw new BusinessException("getPayPageVoucherList_fail", e.getRetMesg());
        }
        if (null != getPayPageVoucherListResp) {
            getPayPageVoucherListApiResp.setVoucherPackIdList(getPayPageVoucherListResp.getVoucherPackIdList());
            List<Long> voucherList = new ArrayList<>();
            //券包拆出
            if (CollectionUtils.isNotEmpty(getPayPageVoucherListResp.getVoucherPackIdList())) {
                //获取券包详情信息
                GetVoucherPackByIdsReq getVoucherPackByIdsReq = new GetVoucherPackByIdsReq();
                String packIdsStr = com.wanshifu.framework.utils.StringUtils.listToCommaSplit(
                        getPayPageVoucherListResp.getVoucherPackIdList().stream().map(String::valueOf).collect(Collectors.toList())
                );
                getVoucherPackByIdsReq.setPackIds(packIdsStr);
                List<VoucherPackConfig> voucherPackConfigList = voucherPackApi.getVoucherPackByIds(getVoucherPackByIdsReq);
                //解析优惠券数据，获取全部的券id

                voucherPackConfigList.forEach(voucherPackConfig -> {
                    List<VoucherRewardBo> voucherBos = JSONArray.parseArray(voucherPackConfig.getVoucher(), VoucherRewardBo.class);
                    List<Long> rewardExtraIdList = voucherBos.stream().map(VoucherRewardBo::getEventId).map(Long::valueOf).collect(Collectors.toList());
                    voucherList.addAll(rewardExtraIdList);
                });
            }
            if (CollectionUtils.isNotEmpty(getPayPageVoucherListResp.getVoucherIdList())) {
                voucherList.addAll(getPayPageVoucherListResp.getVoucherIdList());
            }
            getPayPageVoucherListApiResp.setVoucherIdList(voucherList);
        }


        return getPayPageVoucherListApiResp;
    }

    @Override
    @ExceptionHandle(note = "家庭弹窗广告获取异常")
    public CustomerAdResp listCustomerPopAd(CustomerAdRqt customerAdRqt, String token) {
        try {
            CustomerAdResp customerAdResp = new CustomerAdResp();
            //该用户是否已登录
            Long userId = getUserIdByTokenAndUserClass(token, customerAdRqt.getUserClass());
            if (null == userId) {
                return this.listUnLoginCustomerPopAd(customerAdRqt, customerAdResp);
            }
            customerAdRqt.setUserId(userId);
            List<String> tagList = new ArrayList<>();
            try {
                tagList = this.getTagListFromBigData(customerAdRqt.getUserClass(), userId, customerAdRqt.getLaunchPort());
            } catch (Exception e) {
                log.error("listCustomerPopAd_fail,调用大数据接口异常:{}", e);
            }
            PopAdBo popAdBo = PopAdBo.builder().port(customerAdRqt.getLaunchPort())
                    .adPositionSymbol(customerAdRqt.getAdPositionSymbol())
                    .userClass(customerAdRqt.getUserClass())
                    .userId(userId)
                    .tagList(tagList)
                    .channel(customerAdRqt.getChannel())
                    .firstDivisionId(customerAdRqt.getFirstDivisionId())
                    .secondDivisionId(customerAdRqt.getSecondDivisionId())
                    .thirdDivisionId(customerAdRqt.getThirdDivisionId())
                    .build();

            PullPopAdReq pullPopAdReq = new PullPopAdReq();
            BeanUtils.copyProperties(popAdBo, pullPopAdReq);

            //调用接口返回
            List<PullAdPopupResp> pullAdPopupRespList;

            PullAdPopupResp pullAdPopupResp = null;
            try {
                pullAdPopupRespList = adPullApi.pullCustomerPopup(pullPopAdReq);
                if (CollectionUtils.isEmpty(pullAdPopupRespList)) {
                    return null;
                }
            } catch (ApiAccessException e) {
                log.error("listCustomerPopAd_fail,拉取广告失败:{}", e);
                return null;
            }
            //批量判断是否下发,以及是否达上限
            List<CustomerActivityBaseResp> activityBaseList = new ArrayList<>();
            List<Long> activityIdList = pullAdPopupRespList.stream().filter(f -> f.getAdBaseInfo() != null && f.getAdBaseInfo().getActivityId() != 0).map(m -> m.getAdBaseInfo().getActivityId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(activityIdList)) {
                List<GetCustomerSuitActivityDetailRqt.ActivityLimitInfo> activityLimitInfoList = this.buildPopupAdFilterActivityInfo(pullAdPopupRespList, true);
                //调用活动接口过滤不符合的活动广告信息（达到上限，或者登录态下不在投放人群中或者没下发）
                activityBaseList = this.customerSignUpAndNotUpperLimitActivityDetail(activityLimitInfoList, userId);
            }
            for (PullAdPopupResp adPopupResp : pullAdPopupRespList) {
                //判断活动是否下发给用户，如果没有下发就返回null
                //2023-12-21 13:56:34 6【家庭】家庭广告，去掉活动不下发，广告不展示的限制；
                if (adPopupResp.getAdBaseInfo() == null) {
                    continue;
                }

                if (adPopupResp.getAdBaseInfo().getActivityId() != 0) {
                    Optional<CustomerActivityBaseResp> activityBaseRespOptional = activityBaseList.stream().filter(f -> adPopupResp.getAdBaseInfo().getActivityId().equals(f.getActivityId())).findFirst();
                    //没有活动信息则直接过滤掉
                    if (!activityBaseRespOptional.isPresent()) {
                        continue;
                    }
                    CustomerActivityBaseResp customerActivityBaseResp = activityBaseRespOptional.get();
                    //红包弹窗样式且已经报名则过滤，不再报名时间内也过滤掉
                    if (PopStyleEnum.BONUS_POP_SYMBOL.type.equals(adPopupResp.getAdBaseInfo().getPopStyleSymbol())) {
                        if (customerActivityBaseResp.getApplyState() == 1) {
                            continue;
                        }
                        boolean applyAbleFlag = Objects.nonNull(customerActivityBaseResp.getApplyEndTime()) ? customerActivityBaseResp.getApplyEndTime().after(new Date()) && customerActivityBaseResp.getApplyStartTime().before(new Date()) : customerActivityBaseResp.getLaunchEndTime().after(new Date());
                        if (!applyAbleFlag) {
                            continue;
                        }
                    }
                    //上面条件都满足则退出循环
                    pullAdPopupResp = adPopupResp;
                    break;
                } else {
                    //如果都不是活动类型的广告
                    pullAdPopupResp = adPopupResp;
                    break;
                }
            }
            if (ObjectUtils.isEmpty(pullAdPopupResp)) {
                //如果对象是空，说明没有满足条件的弹窗广告
                return null;
            }
            if (pullAdPopupResp.getMeterialList() == null && pullAdPopupResp.getAdBaseInfo() == null) {
                return null;
            }
            //回调符合条件的弹窗广告aid
            this.backPopAid(pullAdPopupResp.getAdBaseInfo().getAdId(), userId, null);
            CustomerActivityBaseResp activityBaseResp = null;
            Long activityId = pullAdPopupResp.getAdBaseInfo().getActivityId();
            if (CollectionUtils.isNotEmpty(activityBaseList) && activityId != 0) {
                Optional<CustomerActivityBaseResp> activityBaseRespOptional = activityBaseList.stream().filter(f -> f.getActivityId().equals(activityId)).findFirst();
                activityBaseResp = activityBaseRespOptional.isPresent() ? activityBaseRespOptional.get() : null;
            }
            //组装弹窗广告出参信息：广告信息+活动信息+图片信息
            List<CustomerAdResp.AdInfo> getUserAdRespList = this.packageCustomerPopAdOutput(pullAdPopupResp, customerAdRqt, activityBaseResp);
            customerAdResp.setAdInfo(getUserAdRespList);
            return customerAdResp;
        } catch (Exception e) {
            log.error("listCustomerPopAd_fail:{}", e);
            return null;
        }
    }

    private List<GetCustomerSuitActivityDetailRqt.ActivityLimitInfo> buildPopupAdFilterActivityInfo(List<PullAdPopupResp> pullAdPopupRespList, Boolean isLogin) {
        List<GetCustomerSuitActivityDetailRqt.ActivityLimitInfo> activityLimitInfoList = new ArrayList<>();
        for (PullAdPopupResp adPopupResp : pullAdPopupRespList) {
            if (adPopupResp.getAdBaseInfo() == null || adPopupResp.getAdBaseInfo().getActivityId() < 1) {
                continue;
            }
            GetCustomerSuitActivityDetailRqt.ActivityLimitInfo activityLimitInfo = new GetCustomerSuitActivityDetailRqt.ActivityLimitInfo();
            activityLimitInfo.setActivityId(adPopupResp.getAdBaseInfo().getActivityId());
            activityLimitInfo.setIsFilterBudgetUpperLimit(1);
            activityLimitInfo.setIsFilterRewardMaxUpperLimit(1);
            //领券弹窗样式特殊处理：登录状态 需要过滤个人循环期间领取上限
            if (isLogin && PopStyleEnum.RECEIVE_VOUCHER.type.equals(adPopupResp.getAdBaseInfo().getPopStyleSymbol())) {
                activityLimitInfo.setIsFilterUserCycleUpperLimit(1);
            }
            activityLimitInfoList.add(activityLimitInfo);
        }
        return activityLimitInfoList;
    }

    /**
     * 非登录状态拉取家庭弹窗广告
     *
     * @param customerAdRqt
     * @param customerAdResp
     * @return
     */
    private CustomerAdResp listUnLoginCustomerPopAd(CustomerAdRqt customerAdRqt, CustomerAdResp customerAdResp) {
        UnLoginPullAdReq unLoginPullAdReq = new UnLoginPullAdReq();
        BeanUtils.copyProperties(customerAdRqt, unLoginPullAdReq);
        unLoginPullAdReq.setPort(customerAdRqt.getLaunchPort());
        try {
            List<PullAdPopupResp> pullAdPopupRespList = adPullApi.unLoginpullCustomerPopAd(unLoginPullAdReq);

            if (CollectionUtils.isEmpty(pullAdPopupRespList)) {
                return customerAdResp;
            }
            //调用活动接口过滤不符合的活动广告信息（达到上限，或者登录态下不在投放人群中或者没下发）
            List<GetCustomerSuitActivityDetailRqt.ActivityLimitInfo> activityLimitInfoList = new ArrayList<>();
            pullAdPopupRespList.forEach(activityNonPopupResp -> {
                if (Objects.isNull(activityNonPopupResp.getAdBaseInfo()) || activityNonPopupResp.getAdBaseInfo().getActivityId() < 1) {
                    return;
                }
                GetCustomerSuitActivityDetailRqt.ActivityLimitInfo activityLimitInfo = new GetCustomerSuitActivityDetailRqt.ActivityLimitInfo();
                activityLimitInfo.setIsFilterRewardMaxUpperLimit(1);
                activityLimitInfo.setIsFilterBudgetUpperLimit(1);
                activityLimitInfo.setIsFilterUserCycleUpperLimit(0);
                activityLimitInfo.setActivityId(activityNonPopupResp.getAdBaseInfo().getActivityId());
                activityLimitInfoList.add(activityLimitInfo);
            });
            List<CustomerActivityBaseResp> activityBaseList = this.customerSignUpAndNotUpperLimitActivityDetail(activityLimitInfoList, null);
            List<Long> suitActivityIdList = activityBaseList.stream().map(m -> m.getActivityId()).collect(Collectors.toList());
            //移除掉被不符合条件的活动广告
            pullAdPopupRespList = pullAdPopupRespList.stream().filter(f -> Objects.nonNull(f.getAdBaseInfo()) && (f.getAdBaseInfo().getActivityId() == 0 || suitActivityIdList.contains(f.getAdBaseInfo().getActivityId()))).collect(Collectors.toList());
            //组装弹窗广告出参信息：广告信息+图片信息
            PullAdPopupResp pullAdPopupResp = pullAdPopupRespList.get(0);
            String userClass = customerAdRqt.getUserClass();
            Long userId = customerAdRqt.getUserId();
            //返回结果list
            List<CustomerAdResp.AdInfo> customerAdRespList = new ArrayList<>();

            //获取图片urlissue
            List<String> aidList = pullAdPopupResp.getMeterialList().stream().filter(f -> f.getPictureId() != null).map(PopMeterialResp::getPictureId).map(Objects::toString).collect(Collectors.toList());

            Map<Long, String> imageResultMap = new HashMap<>();
            if (aidList.size() > 0) {
                imageResultMap = httpImageUtils.sendPostRequest(aidList);
            }
            //返回的实体类中的子类
            CustomerAdResp.AdInfo adInfo = new CustomerAdResp.AdInfo();
            //广告基本信息
            AdBaseInfoResp adBaseInfo = pullAdPopupResp.getAdBaseInfo();

            adInfo.setHasSignUp(0);
            //判断弹窗样式，如果是红包弹窗，就需要报名
            //只要弹窗的样式是红包样式就可以返回标识告诉前端，需要跳登录
            if (PopStyleEnum.BONUS_POP_SYMBOL.type.equals(adBaseInfo.getPopStyleSymbol())) {
                adInfo.setHasSignUp(2);
            }
            adInfo.setHasReward(0);
            //广告基本信息
            BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
            adInfo.setMaterialClass("picture");

            //广告素材信息
            List<CustomerAdResp.Meterial> meterial = new ArrayList<>();
            if (pullAdPopupResp.getMeterialList() != null) {
                List<PopMeterialResp> meterialList = pullAdPopupResp.getMeterialList();
                if (Arrays.asList(PopStyleEnum.BONUS_POP_SYMBOL.type, PopStyleEnum.RECEIVE_VOUCHER.type).contains(adBaseInfo.getPopStyleSymbol())) {
                    for (PopMeterialResp commonMeterialResp : meterialList) {
                        CustomerAdResp.Meterial meterial1 = new CustomerAdResp.Meterial();
                        meterial1.setForwardUrlType(commonMeterialResp.getForwardUrlType());
                        meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
                        meterial1.setMaterialType(commonMeterialResp.getMaterialType());
                        meterial1.setIsLimitTime(commonMeterialResp.getIsLimitTime());
                        meterial1.setShowTime(commonMeterialResp.getShowTime());
                        meterial1.setMaterialClass("picture");
                        meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());

                        //                CustomerAdResp.Meterial popMaterialList = this.getCustomerPopMaterialList(
                        //                        CustomerPopAdMaterialBo.builder().activityLandingUrl(commonMeterialResp.getForwardUrl())
                        //                                .pullAdPopupResp(pullAdPopupResp)
                        //                                .adBaseInfo(adBaseInfo)
                        //                                .adInfo(adInfo)
                        //                                .commonMeterialResp(commonMeterialResp)
                        //                                .userClass(userClass).build()
                        //                );
                        //                meterial1.setPopIconList(popMaterialList.getPopIconList());
                        meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
                        meterial1.setAdForwardUrlInfo(this.getAdForwardUrlInfo(commonMeterialResp.getAdForwardUrlBo()));
                        meterial.add(meterial1);
                    }
                    adInfo.setHasSignUp(2);
                } else {
                    PopMeterialResp commonMeterialResp = meterialList.get(0);
                    CustomerAdResp.Meterial meterial1 = new CustomerAdResp.Meterial();
                    meterial1.setForwardUrlType(commonMeterialResp.getForwardUrlType());
                    meterial1.setMeterialTitle(commonMeterialResp.getMaterialTitle());
                    meterial1.setMaterialType(commonMeterialResp.getMaterialType());
                    meterial1.setIsLimitTime(commonMeterialResp.getIsLimitTime());
                    meterial1.setShowTime(commonMeterialResp.getShowTime());
                    meterial1.setMaterialClass("picture");
                    meterial1.setForwardUrl(commonMeterialResp.getForwardUrl());

                    //                CustomerAdResp.Meterial popMaterialList = this.getCustomerPopMaterialList(
                    //                        CustomerPopAdMaterialBo.builder().activityLandingUrl(commonMeterialResp.getForwardUrl())
                    //                                .pullAdPopupResp(pullAdPopupResp)
                    //                                .adBaseInfo(adBaseInfo)
                    //                                .adInfo(adInfo)
                    //                                .commonMeterialResp(commonMeterialResp)
                    //                                .userClass(userClass).build()
                    //                );
                    //                meterial1.setPopIconList(popMaterialList.getPopIconList());
                    meterial1.setPictureUrl(imageResultMap.get(commonMeterialResp.getPictureId()));
                    meterial1.setAdForwardUrlInfo(this.getAdForwardUrlInfo(commonMeterialResp.getAdForwardUrlBo()));
                    meterial.add(meterial1);
                }

            }
            adInfo.setMeterialList(meterial);
            customerAdRespList.add(adInfo);
            customerAdResp.setAdInfo(customerAdRespList);

            return customerAdResp;
        } catch (ApiAccessException e) {
            log.error("get_not_login_customer_ad_fail", e);
            throw new BusException("get_not_login_customer_ad_fail", "拉取广告失败");
        }
    }

    @Override
    @ExceptionHandle(note = "家庭广告位广告获取异常")
    public CustomerAdResp listCustomerAdPositionAd(CustomerAdRqt customerAdRqt, String token) {
        try {
            //兼容家庭首页中部轮播广告位不同版本尺寸不同问题
            if (AdConstant.HOMEPAGE_MIDDLE_CIRCLE_APP.equals(customerAdRqt.getAdPositionSymbol())) {
                String appVersion = httpServletRequest.getHeader("appVersion");
                if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(appVersion) && AdConstant.SPECIAL_SIZE_APP_VERSION_LIST.contains(appVersion)) {
                    customerAdRqt.setAdPositionSymbol(AdConstant.HOMEPAGE_MIDDLE_CIRCLE2_APP);
                }
            }

            CustomerAdResp customerAdResp = new CustomerAdResp();
            //该用户是否已登录
            Long userId = getUserIdByTokenAndUserClass(token, customerAdRqt.getUserClass());
            if (null == userId || userId == 0L) {
                //未登录情况下的广告
                return this.listUnLoginCustomerAdPositionAd(customerAdRqt, customerAdResp);
            }
            //查询是否已有拉取广告缓存,有则直接使用缓存数据，无则查询
            String userFormatKey = String.format(CacheKeyConstant.USER_PULL_AD_POSITION_KEY, customerAdRqt.getUserClass(), customerAdRqt.getLaunchPort(), customerAdRqt.getAdPositionSymbol(), userId);
            String cacheValueStr = redisHelper.get(userFormatKey);
            if (!StringUtils.isEmpty(cacheValueStr)) {
                String decodeCacheValue = MathUtils.decompressFromString(cacheValueStr);
                customerAdResp = JSON.parseObject(decodeCacheValue, CustomerAdResp.class);
                return customerAdResp;
            }
            //组装拉取广告入参
            PullNonPopAdReq pullNonPopAdReq = new PullNonPopAdReq();
            List<String> tagList = new ArrayList<>();
            try {
                tagList = this.getTagListFromBigData(customerAdRqt.getUserClass(), userId, customerAdRqt.getLaunchPort());
            } catch (Exception e) {
                log.error("listCustomerAdPositionAd_fail,调用大数据接口异常:", e);
            }
            AdBo build = AdBo.builder().port(customerAdRqt.getLaunchPort())
                    .adPositionSymbol(customerAdRqt.getAdPositionSymbol())
                    .userClass(customerAdRqt.getUserClass())
                    .userId(userId)
                    .channel(customerAdRqt.getChannel())
                    .firstDivisionId(customerAdRqt.getFirstDivisionId())
                    .secondDivisionId(customerAdRqt.getSecondDivisionId())
                    .thirdDivisionId(customerAdRqt.getThirdDivisionId())
                    .fourthDivisionId(customerAdRqt.getFourthDivisionId())
                    .tagList(tagList).build();
            BeanUtils.copyProperties(build, pullNonPopAdReq);

            List<PullAdNonPopupResp> pullAdNonPopupRespList = adPullApi.pullCustomerNonPopup(pullNonPopAdReq);

            List<PullAdNonPopupResp> pullAdNonPopupResps = new ArrayList<>();
            //选中的aid list
            List<Long> selectedAidList = new ArrayList<>();
            //选中的aid list
            List<ActivityBaseResp> activityBaseList = new ArrayList<>();
            //过滤广告
            this.filterCustomerAd(pullAdNonPopupRespList, selectedAidList, pullAdNonPopupResps, userId, customerAdRqt.getUserClass(), activityBaseList);
            //截取最大展示个数
            if (!CollectionUtils.isEmpty(pullAdNonPopupResps)) {
                int maxShowNum = pullAdNonPopupRespList.get(0).getMaxShowNum();
                if (pullAdNonPopupResps.size() >= maxShowNum) {
                    pullAdNonPopupResps = pullAdNonPopupResps.subList(0, maxShowNum);
                    selectedAidList = selectedAidList.subList(0, maxShowNum);
                }
            }
            /**
             * 组装出参信息:广告信息+活动信息+图片信息
             */
            List<CustomerAdResp.AdInfo> getUserAdRespList = this.packageCustomerAdPositionOutput(pullAdNonPopupResps, userId, activityBaseList);
//            if (customerAdRqt.getAdPositionSymbol().equals("activity_center_ad_list")) {
//                //列表广告排序
//                getUserAdRespList = this.listAdSort(getUserAdRespList);
//            }
            customerAdResp.setAdInfo(getUserAdRespList);
            //增加缓存
            if (CollectionUtils.isNotEmpty(getUserAdRespList) && redisCacheTime > 0) {
                String encodeCache = MathUtils.compressToString(JSON.toJSONString(customerAdResp));
                redisHelper.set(userFormatKey, encodeCache, redisCacheTime);
            }
            //选中了哪几个需要回调给平台
            if (selectedAidList.size() > 0) {
                this.backAidList(selectedAidList, userId);
            }
            return customerAdResp;
        } catch (Exception e) {
            log.error("listCustomerAdPositionAd_fail,拉取家庭广告失败,请求参数={},错误信息={}", JSON.toJSONString(customerAdRqt), e);
            return null;
        }
    }

    @Override
    public Integer customerAdClickCollect(CustomerAdClickCollectRqt customerAdClickCollectRqt) {
        CustomerAdClickCollectReq customerAdClickCollectReq = new CustomerAdClickCollectReq();
        ActivityApplyUpResp activityApplyUpResp = null;
        if (Objects.nonNull(customerAdClickCollectRqt.getActivityId()) && customerAdClickCollectRqt.getActivityId() != 0) {
            //查询活动信息
            GetUserHasSignUpRqt activityDetailSignRqt = new GetUserHasSignUpRqt();
            activityDetailSignRqt.setActivityId(customerAdClickCollectRqt.getActivityId());
            activityDetailSignRqt.setUserId(customerAdClickCollectRqt.getUserId());
            activityApplyUpResp = activityBusinessServiceApi.getUserHasApplyUp(activityDetailSignRqt);
            if (ObjectUtils.isEmpty(activityApplyUpResp)) {
                throw new BusException("查询活动信息异常");
            }
            //没有报名就不调用多状态广告点击保存接口
            if (activityApplyUpResp.getIsApply() == null || activityApplyUpResp.getIsApply() != 1 || !ActivityStateEnum.LAUNCHING.code.equals(activityApplyUpResp.getActivityState())) {
                return 2;
            }
        }
        Date date = new Date();
        if (Objects.nonNull(activityApplyUpResp)) {
            customerAdClickCollectReq.setActivityEndTime(activityApplyUpResp.getActivityEndTime());
            customerAdClickCollectReq.setActivityStartTime(activityApplyUpResp.getActivityStartTime());
            customerAdClickCollectReq.setActivityId(customerAdClickCollectRqt.getActivityId());
        }
        customerAdClickCollectReq.setUserId(customerAdClickCollectRqt.getUserId());
        customerAdClickCollectReq.setUserClass(customerAdClickCollectRqt.getUserClass());
        customerAdClickCollectReq.setPort(customerAdClickCollectRqt.getLaunchPort());
        customerAdClickCollectReq.setLookTime(date);
        customerAdClickCollectReq.setAdId(customerAdClickCollectRqt.getAdId());
        customerAdClickCollectReq.setTriggerTime(date);
        try {
            return adPullApi.customerAdClickCollect(customerAdClickCollectReq);
        } catch (ApiAccessException e) {
            log.error("customerAdClickCollect_fail,req:{}", JSON.toJSONString(customerAdClickCollectReq));
            throw new BusException("customerAdClickCollect_fail", "家庭多状态广告点击记录失败");
        }
    }

    @Override
    public GetUserRechargeActivityResp getUserRechargeActivity(GetUserRechargeActivityRqt rqt) {
        GetUserRechargeActivityResp resp = new GetUserRechargeActivityResp();

        GetUserRechargeActivityApiRqt queryRqt = new GetUserRechargeActivityApiRqt();
        queryRqt.setUserClass(rqt.getUserClass());
        queryRqt.setUserId(rqt.getUserId());
        GetUserRechargeActivityApiResp apiResult = activityBusinessServiceApi.getUserRechargeActivity(queryRqt);
        ActivityBase activityBase = apiResult.getActivityBase();
        resp.setIsIssueRechargeActivity(Objects.nonNull(activityBase) ? 1 : 0);
        return resp;
    }

    @Override
    public GetRechargeRewardResp getRechargeRewardList(GetRechargeRewardRqt rqt) {
        GetRechargeRewardResp resp = new GetRechargeRewardResp();

        GetRechargeRewardApiRqt queryRqt = new GetRechargeRewardApiRqt();
        queryRqt.setUserClass(rqt.getUserClass());
        queryRqt.setUserId(rqt.getUserId());
        queryRqt.setRechargeSuccessTime(rqt.getRechargeSuccessTime());
        queryRqt.setIsNeedRewardInfo(rqt.getIsNeedRewardInfo());
        GetRechargeRewardApiResp getRechargeRewardApiResp = activityBusinessServiceApi.getRechargeRewardList(queryRqt);
        //如果url为空,查找活动落地页有无关联该活动
        if (StringUtils.isEmpty(getRechargeRewardApiResp.getUrl())) {
            String port = rqt.getIsWeb() == 1 ? "app" : "web";
            GetUrlByActivityIdRqt queryUrlRqt = new GetUrlByActivityIdRqt();
            queryUrlRqt.setActivityId(getRechargeRewardApiResp.getActivityId());
            queryUrlRqt.setPort(port);
            GetUrlByActivityIdResp urlResp = marketingActivityServiceApi.getUrlByActivityId(queryUrlRqt);
            if (Objects.nonNull(urlResp) && com.wanshifu.framework.utils.StringUtils.isNotEmpty(urlResp.getUniqueCode())) {
                String uniqueCode = urlResp.getUniqueCode();
                String url = landingPageUrl + "/" + port + "/" + uniqueCode;
                resp.setUrl(url);
            }
        } else {
            resp.setUrl(getRechargeRewardApiResp.getUrl() + "&t=" + rqt.getIsWeb());
        }
        resp.setRewardInfoRespList(this.buildRewardInfo(getRechargeRewardApiResp.getRewardInfoRespList(), rqt.getUserClass()));
        resp.setIsTotalRecharge(getRechargeRewardApiResp.getIsTotalRecharge());
        resp.setState(getRechargeRewardApiResp.getState());
        resp.setShowText(getRechargeRewardApiResp.getShowText());
        resp.setNeededRechargeAmount(getRechargeRewardApiResp.getNeededRechargeAmount());
        resp.setRechargeReward(getRechargeRewardApiResp.getRechargeReward());
        return resp;
    }

    @Override
    public RechargeChooseCombineResp getRechargeChooseCombineRewardInfo(RechargeChooseCombineRqt rqt) {
        RechargeChooseCombineResp resp = new RechargeChooseCombineResp();

        RechargeChooseCombineApiRqt queryRqt = new RechargeChooseCombineApiRqt();
        queryRqt.setUserClass(rqt.getUserClass());
        queryRqt.setUserId(rqt.getUserId());
        RechargeChooseCombineApiResp rechargeChooseCombineRewardInfo = activityBusinessServiceApi.getRechargeChooseCombineRewardInfo(queryRqt);

        resp.setRewardInfoRespList(this.buildRewardInfo(rechargeChooseCombineRewardInfo.getRewardInfoRespList(), rqt.getUserClass()));
        return resp;
    }

    @Override
    public BrowsePageResp runBrowsePageTask(BrowsePageRqt rqt) {
        BrowsePageResp resp = new BrowsePageResp();

        BrowsePageApiRqt apiRqt = new BrowsePageApiRqt();
        apiRqt.setActivityTaskId(rqt.getActivityTaskId());
        apiRqt.setUserClass(rqt.getUserClass());
        apiRqt.setUserId(rqt.getUserId());
        apiRqt.setBrowsePageList(rqt.getBrowsePageList());
        apiRqt.setBrowseDuration(rqt.getBrowseDuration());
        BrowsePageApiResp browsePageApiResp = activityBusinessServiceApi.runBrowsePageTask(apiRqt);

        if (Objects.isNull(browsePageApiResp)) {
            resp.setState(0);
            resp.setFailReason("任务执行异常");
        } else {
            resp.setState(browsePageApiResp.getState());
            resp.setFailReason(browsePageApiResp.getFailReason());
        }
        return resp;
    }

    @Override
    public CustomerAdResp batchGetCustomerAdPosition(BatchGetCustomerAdPositionApiRqt customerAdRqt, String token) {
        try {
            //兼容家庭首页中部轮播广告位不同版本尺寸不同问题
            if (customerAdRqt.getAdPositionSymbolList().contains(AdConstant.HOMEPAGE_MIDDLE_CIRCLE_APP)) {
                String appVersion = httpServletRequest.getHeader("appVersion");
                if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(appVersion) && AdConstant.SPECIAL_SIZE_APP_VERSION_LIST.contains(appVersion)) {
                    customerAdRqt.getAdPositionSymbolList().replaceAll(symbol -> AdConstant.HOMEPAGE_MIDDLE_CIRCLE_APP.equals(symbol) ? AdConstant.HOMEPAGE_MIDDLE_CIRCLE2_APP : symbol);
                }
            }
            //该用户是否已登录
            Long userId = getUserIdByTokenAndUserClass(token, customerAdRqt.getUserClass());
            if (null == userId || userId == 0L) {
                //未登录情况下的广告
                return this.unLoginBatchGetCustomerAdPosition(customerAdRqt);
            }
            //查询是否已有拉取广告缓存,有则直接使用缓存数据，无则查询

            String userFormatKey = String.format(CacheKeyConstant.USER_PULL_AD_POSITION_KEY, customerAdRqt.getUserClass(), customerAdRqt.getLaunchPort(), customerAdRqt.getAdPositionSymbolList(), userId);
//            String cacheValueStr = redisHelper.get(userFormatKey);
            CustomerAdResp customerAdResp = new CustomerAdResp();
//            if (!StringUtils.isEmpty(cacheValueStr)) {
//                return this.buildCacheBatchCustomerAdResp(cacheValueStr, userId);
//            }

            //组装拉取广告入参
            List<String> tagList = new ArrayList<>();
            try {
                tagList = this.getTagListFromBigData(customerAdRqt.getUserClass(), userId, customerAdRqt.getLaunchPort());
            } catch (Exception e) {
                log.error("listCustomerAdPositionAd_fail,调用大数据接口异常:", e);
            }
            BatchGetCustomerAdPositionForInnerRqt customerPullNonPopAdReq = new BatchGetCustomerAdPositionForInnerRqt();
            BeanUtils.copyProperties(customerAdRqt, customerPullNonPopAdReq);
            customerPullNonPopAdReq.setUserId(userId);
            customerPullNonPopAdReq.setTagList(tagList);
            customerPullNonPopAdReq.setPort(customerAdRqt.getLaunchPort());
            List<NewAdPositionAdResp> pullAdNonPopupRespList = adPullApi.batchGetCustomerAdPositionForInner(customerPullNonPopAdReq);

            List<NewAdPositionAdResp> suitAdNonPopupRespList = new ArrayList<>();
            //选中的ad list
            List<CustomerActivityBaseBo> activityBaseList = new ArrayList<>();
            //会员权益信息
            VipEquityInfoBo vipEquityInfoBo = new VipEquityInfoBo();
            //过滤活动广告
            this.filterNewCustomerAd(pullAdNonPopupRespList, suitAdNonPopupRespList, userId, activityBaseList, customerAdRqt.getSupportTypeList(), vipEquityInfoBo);
            //对多选一广告(一个位置有多个广告位广告，但只展示其中一个广告位广告)进行优先级过滤
            List<NewAdPositionAdResp> choiceOnePullAdNonPopupResps = pullAdNonPopupRespList.stream().filter(f -> AdConstant.AD_MULTI_CHOICE_ONE.equals(f.getPriorityType())).collect(Collectors.toList());
            List<NewAdPositionAdResp> notChoiceOnePullAdNonPopupResps = pullAdNonPopupRespList.stream().filter(f -> !AdConstant.AD_MULTI_CHOICE_ONE.equals(f.getPriorityType())).collect(Collectors.toList());
            List<NewAdPositionAdResp> finalShowPullAdNonPopupRespList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(choiceOnePullAdNonPopupResps)) {
                //根据标识分组
                Map<String, List<NewAdPositionAdResp>> choiceGroupMap = choiceOnePullAdNonPopupResps.stream().collect(Collectors.groupingBy(g -> g.getPriorityAdPositionSymbol()));
                for (Map.Entry<String, List<NewAdPositionAdResp>> entry : choiceGroupMap.entrySet()) {
                    List<NewAdPositionAdResp> symbolNonPopupList = entry.getValue();
                    //根据优先级从小到大排序
                    symbolNonPopupList.sort(Comparator.comparing(NewAdPositionAdResp::getPriorityNumber));
                    String selectedAdSymbol = symbolNonPopupList.get(0).getAdBaseInfo().getAdPositionSymbol();
                    List<NewAdPositionAdResp> selectedSymbolNonPopRespList = symbolNonPopupList.stream().filter(f -> f.getAdBaseInfo().getAdPositionSymbol().equals(selectedAdSymbol)).collect(Collectors.toList());
                    int maxShowNum = selectedSymbolNonPopRespList.get(0).getMaxShowNum();
                    List<NewAdPositionAdResp> addNonPopRespList = selectedSymbolNonPopRespList.size() >= maxShowNum ? selectedSymbolNonPopRespList.subList(0, maxShowNum) : selectedSymbolNonPopRespList;
                    finalShowPullAdNonPopupRespList.addAll(addNonPopRespList);

                }
            }
            //非多选一优先级的广告
            if (CollectionUtils.isNotEmpty(notChoiceOnePullAdNonPopupResps)) {
                Map<String, List<NewAdPositionAdResp>> notChoiceGroupMap = notChoiceOnePullAdNonPopupResps.stream().collect(Collectors.groupingBy(g -> g.getAdBaseInfo().getAdPositionSymbol()));
                notChoiceGroupMap.forEach((adPositionSymbol, symbolNonPopupList) -> {
                    int maxShowNum = symbolNonPopupList.get(0).getMaxShowNum();
                    finalShowPullAdNonPopupRespList.addAll(symbolNonPopupList.size() >= maxShowNum ? symbolNonPopupList.subList(0, maxShowNum) : symbolNonPopupList);
                });
            }
            if (CollectionUtils.isEmpty(finalShowPullAdNonPopupRespList)) {
                return customerAdResp;
            }
            //根据广告位标识分组截取最大展示个数
            List<NewAdPositionAdResp> selectedPullAdNonPopupRespList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(finalShowPullAdNonPopupRespList)) {
                Map<String, List<NewAdPositionAdResp>> adPositionSymbolGroup = finalShowPullAdNonPopupRespList.stream().collect(Collectors.groupingBy(t -> t.getAdPositionSymbol()));
                adPositionSymbolGroup.forEach((positionSymbol, positionAdList) -> {
                    int maxShowNum = positionAdList.get(0).getMaxShowNum();
                    selectedPullAdNonPopupRespList.addAll(positionAdList.size() >= maxShowNum ? positionAdList.subList(0, maxShowNum) : positionAdList);
                });
            }
            /**
             * 组装出参信息:广告信息+活动信息+图片信息
             */
            List<CustomerAdResp.AdInfo> getUserAdRespList = this.packageNewCustomerAdPositionOutput(selectedPullAdNonPopupRespList, userId, activityBaseList, vipEquityInfoBo);
            customerAdResp.setAdInfo(getUserAdRespList);
            //增加缓存
            if (CollectionUtils.isNotEmpty(getUserAdRespList) && redisCacheTime > 0) {
                String encodeCache = MathUtils.compressToString(JSON.toJSONString(customerAdResp));
                redisHelper.set(userFormatKey, encodeCache, redisCacheTime);
            }
            //选中了哪几个需要回调给平台
            if (selectedPullAdNonPopupRespList.size() > 0) {
                List<Long> selectedAdIdList = selectedPullAdNonPopupRespList.stream().map(m -> m.getAdBaseInfo().getAdId()).collect(Collectors.toList());
                this.backAidList(selectedAdIdList, userId);
            }
            return customerAdResp;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("batchGetCustomerAdPosition_fail,拉取家庭广告失败,请求参数={},错误信息={}", JSON.toJSONString(customerAdRqt), e);
            return null;
        }
    }

    /**
     * 从缓存里获取家庭批量广告信息--未领奖需要去查询是否已领奖或者是否已达到上限
     *
     * @param cacheValueStr
     * @return
     */
    private CustomerAdResp buildCacheBatchCustomerAdResp(String cacheValueStr, Long userId) {
        String decodeCacheValue = MathUtils.decompressFromString(cacheValueStr);
        CustomerAdResp customerAdResp = JSON.parseObject(decodeCacheValue, CustomerAdResp.class);
        //假如有活动且有未领取，此处需再去查询领取状态
        List<CustomerAdResp.AdInfo> adInfoList = customerAdResp.getAdInfo();
        if (CollectionUtils.isEmpty(adInfoList)) {
            return customerAdResp;
        }
        List<Long> activityIdList = adInfoList.stream().filter(adInfo -> adInfo.getActivityId() != 0 && (Objects.isNull(adInfo.getHasReward()) || adInfo.getHasReward() == 0)).map(m -> m.getActivityId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityIdList)) {
            return customerAdResp;
        }
        //查询是否已领取
        List<GetCustomerSuitActivityDetailRqt.ActivityLimitInfo> activityLimitInfoList = new ArrayList<>();
        activityIdList.forEach(activityId -> {
            GetCustomerSuitActivityDetailRqt.ActivityLimitInfo activityLimitInfo = new GetCustomerSuitActivityDetailRqt.ActivityLimitInfo();
            activityLimitInfo.setIsFilterRewardMaxUpperLimit(1);
            activityLimitInfo.setIsFilterBudgetUpperLimit(1);
            activityLimitInfo.setActivityId(activityId);
            activityLimitInfoList.add(activityLimitInfo);
        });
        List<CustomerActivityBaseResp> activityBaseList = this.customerSignUpAndNotUpperLimitActivityDetail(activityLimitInfoList, userId);
        List<CustomerAdResp.AdInfo> newCustomerAdRespList = new ArrayList<>();
        for (CustomerAdResp.AdInfo adInfo : adInfoList) {
            if (adInfo.getActivityId() == 0 || (Objects.nonNull(adInfo.getHasReward()) && adInfo.getHasReward() == 1)) {
                newCustomerAdRespList.add(adInfo);
                continue;
            }
            Optional<CustomerActivityBaseResp> activityBaseRespOptional = activityBaseList.stream().filter(f -> f.getActivityId().equals(adInfo.getActivityId())).findFirst();
            if (!activityBaseRespOptional.isPresent()) {
                log.info("未查询到活动信息,活动已结束或者已达到上限,userId={},activityId={}", userId, adInfo.getActivityId());
                continue;
            }
            adInfo.setHasReward(Objects.nonNull(activityBaseRespOptional.get().getRewardGiveState()) ? activityBaseRespOptional.get().getRewardGiveState() : 0);
            newCustomerAdRespList.add(adInfo);
        }
        customerAdResp.setAdInfo(newCustomerAdRespList);
        return customerAdResp;
    }

    private List<CustomerAdResp.AdInfo> packageNewCustomerAdPositionOutput(List<NewAdPositionAdResp> selectedPullAdNonPopupRespList, Long userId, List<CustomerActivityBaseBo> activityBaseList, VipEquityInfoBo vipEquityInfoBo) {
        List<CustomerAdResp.AdInfo> getUserAdRespList = new ArrayList<>();
        //图片id 批量请求基础平台获取url
        Map<Long, String> imageResultMap = this.getNewAdAidList(selectedPullAdNonPopupRespList);
        for (NewAdPositionAdResp pullAdNonPopupResp : selectedPullAdNonPopupRespList) {
            //返回的实体类中的子类
            CustomerAdResp.AdInfo adInfo = new CustomerAdResp.AdInfo();
            CustomerAdResp.ActivityInfo activityInfo = new CustomerAdResp.ActivityInfo();
            //广告基本信息
            AdBaseInfoResp adBaseInfo = pullAdNonPopupResp.getAdBaseInfo();

            CustomerActivityBaseBo activityDetail;
            CustomerAdResp.VipEquityInfo vipEquityInfo = new CustomerAdResp.VipEquityInfo();
            Integer hasReward = 0;
            //会员广告
            if (this.isVipAd(pullAdNonPopupResp)) {
                BeanUtils.copyProperties(vipEquityInfoBo, vipEquityInfo, "rewardInfo");
                if (CollectionUtils.isNotEmpty(vipEquityInfoBo.getRewardInfo())) {
                    List<CustomerAdResp.RewardInfoBo> rewardInfoList = new ArrayList<>();
                    vipEquityInfoBo.getRewardInfo().forEach(rewardVo -> {
                        CustomerAdResp.RewardInfoBo rewardInfoBo = new CustomerAdResp.RewardInfoBo();
                        BeanUtils.copyProperties(rewardVo, rewardInfoBo);
                        rewardInfoList.add(rewardInfoBo);
                    });
                    vipEquityInfo.setRewardInfo(rewardInfoList);
                }
                hasReward = vipEquityInfoBo.getCouponReceivedState();
                adInfo.setVipQualificationState(vipEquityInfoBo.getVipQualificationState());
                adInfo.setVipEquityInfo(vipEquityInfo);
                adInfo.setRewardSourceType(RewardSourceTypeEnum.VIP_EQUITY.type);
            }
            if (adBaseInfo.getActivityId() != 0) {
                Optional<CustomerActivityBaseBo> baseResp = activityBaseList.stream().filter(base -> base.getActivityId().equals(adBaseInfo.getActivityId())).findFirst();
                if (!baseResp.isPresent()) {
                    continue;
                }
                activityDetail = baseResp.get();
                //活动基本信息
                BeanUtils.copyProperties(activityDetail, activityInfo, "activityName", "activityDescription");
                adInfo.setIsHelpTime(adBaseInfo.getIsHelpTime());
                //判断是否真的在保护期,商家多状态banner不取消保护期
                if (adBaseInfo.getIsHelpTime() == 1) {
                    Integer isProtected = this.isProtected(activityDetail.getActivityEndTime(), adBaseInfo.getActivityId(), userId, adBaseInfo.getIsHelpTime(), adBaseInfo.getAdId(), adBaseInfo.getAdPositionSymbol());
                    adInfo.setIsHelpTime(isProtected);
                }
                if (CollectionUtils.isNotEmpty(activityDetail.getRewardInfo())) {
                    List<CustomerAdResp.RewardInfoBo> rewardInfoList = new ArrayList<>();
                    activityDetail.getRewardInfo().forEach(rewardVo -> {
                        CustomerAdResp.RewardInfoBo rewardInfoBo = new CustomerAdResp.RewardInfoBo();
                        BeanUtils.copyProperties(rewardVo, rewardInfoBo);
                        rewardInfoList.add(rewardInfoBo);
                    });
                    activityInfo.setRewardInfo(rewardInfoList);
                }
                if (AdConstant.CUSTOMER_RECEIVE_VOUCHER_AD_SYMBOL_LIST.contains(adBaseInfo.getAdPositionSymbol())) {
                    adInfo.setRewardSourceType(RewardSourceTypeEnum.FREE_VOUCHER_ACTIVITY.type);
                }
                hasReward = Objects.nonNull(activityDetail.getRewardGiveState()) ? activityDetail.getRewardGiveState() : 0;
            }
            activityInfo.setActivityTitle(adBaseInfo.getActivityTitle());
            adInfo.setActivityInfo(activityInfo);
            //广告基本信息
            BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
            //广告素材信息
            adInfo.setMeterialList(this.getCustomerNoPopAd(pullAdNonPopupResp.getMeterialList(), imageResultMap, adInfo));
            adInfo.setHasReward(hasReward);
            getUserAdRespList.add(adInfo);
        }
        //gc
        activityBaseList = null;
        return getUserAdRespList;
    }

    private CustomerAdResp unLoginBatchGetCustomerAdPosition(BatchGetCustomerAdPositionApiRqt customerAdRqt) {
        CustomerAdResp customerAdResp = new CustomerAdResp();
        List<CustomerAdResp.AdInfo> getUserAdRespList = new ArrayList<>();
        UnLoginBatchGetCustomerAdPositionForInnerRqt unLoginPullAdReq = new UnLoginBatchGetCustomerAdPositionForInnerRqt();
        BeanUtils.copyProperties(customerAdRqt, unLoginPullAdReq);
        unLoginPullAdReq.setPort(customerAdRqt.getLaunchPort());
        try {
            log.info("unLoginBatchGetCustomerAdPosition_start,拉取非登录用户广告,请求参数={}", JSON.toJSONString(unLoginPullAdReq));
            List<NewAdPositionAdResp> pullAdNonPopupRespList = adPullApi.unLoginBatchGetCustomerAdPositionForInner(unLoginPullAdReq);
            if (CollectionUtils.isEmpty(pullAdNonPopupRespList)) {
                return customerAdResp;
            }
            List<NewAdPositionAdResp> suitAdNonPopupRespList = new ArrayList<>();
            //选中的ad list
            List<CustomerActivityBaseBo> activityBaseList = new ArrayList<>();
            //过滤活动广告
            this.filterNewCustomerAd(pullAdNonPopupRespList, suitAdNonPopupRespList, null, activityBaseList, customerAdRqt.getSupportTypeList(), null);
            if (CollectionUtils.isEmpty(suitAdNonPopupRespList)) {
                return customerAdResp;
            }
            //对多选一广告(一个位置有多个广告位广告，但只展示其中一个广告位广告)进行优先级过滤
            List<NewAdPositionAdResp> choiceOnePullAdNonPopupResps = pullAdNonPopupRespList.stream().filter(f -> AdConstant.AD_MULTI_CHOICE_ONE.equals(f.getPriorityType())).collect(Collectors.toList());
            List<NewAdPositionAdResp> notChoiceOnePullAdNonPopupResps = pullAdNonPopupRespList.stream().filter(f -> !AdConstant.AD_MULTI_CHOICE_ONE.equals(f.getPriorityType())).collect(Collectors.toList());
            List<NewAdPositionAdResp> finalShowPullAdNonPopupRespList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(choiceOnePullAdNonPopupResps)) {
                //根据标识分组
                Map<String, List<NewAdPositionAdResp>> choiceGroupMap = choiceOnePullAdNonPopupResps.stream().collect(Collectors.groupingBy(g -> g.getPriorityAdPositionSymbol()));
                for (Map.Entry<String, List<NewAdPositionAdResp>> entry : choiceGroupMap.entrySet()) {
                    List<NewAdPositionAdResp> symbolNonPopupList = entry.getValue();
                    //根据优先级从小到大排序
                    symbolNonPopupList.sort(Comparator.comparing(NewAdPositionAdResp::getPriorityNumber));
                    String selectedAdSymbol = symbolNonPopupList.get(0).getAdBaseInfo().getAdPositionSymbol();
                    List<NewAdPositionAdResp> selectedSymbolNonPopRespList = symbolNonPopupList.stream().filter(f -> f.getAdBaseInfo().getAdPositionSymbol().equals(selectedAdSymbol)).collect(Collectors.toList());
                    int maxShowNum = selectedSymbolNonPopRespList.get(0).getMaxShowNum();
                    List<NewAdPositionAdResp> addNonPopRespList = selectedSymbolNonPopRespList.size() >= maxShowNum ? selectedSymbolNonPopRespList.subList(0, maxShowNum) : selectedSymbolNonPopRespList;
                    finalShowPullAdNonPopupRespList.addAll(addNonPopRespList);

                }
            }
            //非多选一优先级的广告
            if (CollectionUtils.isNotEmpty(notChoiceOnePullAdNonPopupResps)) {
                Map<String, List<NewAdPositionAdResp>> notChoiceGroupMap = notChoiceOnePullAdNonPopupResps.stream().collect(Collectors.groupingBy(g -> g.getAdBaseInfo().getAdPositionSymbol()));
                notChoiceGroupMap.forEach((adPositionSymbol, symbolNonPopupList) -> {
                    int maxShowNum = symbolNonPopupList.get(0).getMaxShowNum();
                    finalShowPullAdNonPopupRespList.addAll(symbolNonPopupList.size() >= maxShowNum ? symbolNonPopupList.subList(0, maxShowNum) : symbolNonPopupList);
                });
            }
            if (CollectionUtils.isEmpty(finalShowPullAdNonPopupRespList)) {
                return customerAdResp;
            }
            //根据广告位标识分组截取最大展示个数
            List<NewAdPositionAdResp> selectedPullAdNonPopupRespList = new ArrayList<>();
            Map<String, List<NewAdPositionAdResp>> adPositionSymbolGroup = finalShowPullAdNonPopupRespList.stream().collect(Collectors.groupingBy(t -> t.getAdPositionSymbol()));
            adPositionSymbolGroup.forEach((positionSymbol, positionAdList) -> {
                int maxShowNum = positionAdList.get(0).getMaxShowNum();
                selectedPullAdNonPopupRespList.addAll(positionAdList.size() >= maxShowNum ? positionAdList.subList(0, maxShowNum) : positionAdList);
            });
            /**
             * 组装出参信息:广告信息+活动信息+图片信息
             */
            //图片id 批量请求基础平台获取url
            Map<Long, String> imageResultMap = this.getNewAdAidList(selectedPullAdNonPopupRespList);
            for (NewAdPositionAdResp pullAdNonPopupResp : selectedPullAdNonPopupRespList) {
                //返回的实体类中的子类
                CustomerAdResp.AdInfo adInfo = new CustomerAdResp.AdInfo();
                CustomerAdResp.ActivityInfo activityInfo = new CustomerAdResp.ActivityInfo();
                //广告基本信息
                AdBaseInfoResp adBaseInfo = pullAdNonPopupResp.getAdBaseInfo();

                CustomerActivityBaseBo activityDetail;
                if (adBaseInfo.getActivityId() != 0) {
                    Optional<CustomerActivityBaseBo> baseResp = activityBaseList.stream().filter(base -> base.getActivityId().equals(adBaseInfo.getActivityId())).findFirst();
                    if (!baseResp.isPresent()) {
                        continue;
                    }
                    activityDetail = baseResp.get();
                    //活动基本信息
                    if (activityDetail != null) {
                        BeanUtils.copyProperties(activityDetail, activityInfo, "activityName", "activityDescription");
                        if (CollectionUtils.isNotEmpty(activityDetail.getRewardInfo())) {
                            List<CustomerAdResp.RewardInfoBo> rewardInfoList = new ArrayList<>();
                            activityDetail.getRewardInfo().forEach(rewardVo -> {
                                CustomerAdResp.RewardInfoBo rewardInfoBo = new CustomerAdResp.RewardInfoBo();
                                BeanUtils.copyProperties(rewardVo, rewardInfoBo);
                                rewardInfoList.add(rewardInfoBo);
                            });
                            activityInfo.setRewardInfo(rewardInfoList);
                        }
                    }
                }
                activityInfo.setActivityTitle(adBaseInfo.getActivityTitle());
                adInfo.setActivityInfo(activityInfo);
                //广告基本信息
                BeanUtils.copyProperties(adBaseInfo, adInfo, "adName", "adDescription");
                //广告素材信息
                adInfo.setMeterialList(this.getCustomerNoPopAd(pullAdNonPopupResp.getMeterialList(), imageResultMap, adInfo));
                adInfo.setShowMaxNumber(pullAdNonPopupResp.getMaxShowNum());
                getUserAdRespList.add(adInfo);
            }
            customerAdResp.setAdInfo(getUserAdRespList);
            return customerAdResp;
        } catch (ApiAccessException e) {
            e.printStackTrace();
            log.error("get_not_login_customer_ad_positionad_fail", e);
            throw new BusException("get_not_login_customer_ad_positionad_ad_fail", "拉取广告失败");
        }
    }


    /**
     * 返回奖励值：带单位
     *
     * @param rewardGiveResp
     * @param voucherBoMap
     * @param voucherPackBoMap
     * @return
     */
    private RewardValueAndNameBo returnGiveValueAndName(RewardGiveResp rewardGiveResp, Map<Long, VoucherRewardBo> voucherBoMap, Map<Long, VoucherRewardBo> voucherPackBoMap) {
        RewardValueAndNameBo returnBo = new RewardValueAndNameBo();
        StringBuilder rewardTextBuilder = new StringBuilder();
        String rewardValueStr = Objects.nonNull(rewardGiveResp.getRewardGiveValue()) ? MathUtils.decimalCutZero(rewardGiveResp.getRewardGiveValue()) : "0";
        RewardSymbol rewardSymbol = RewardSymbol.fromType(rewardGiveResp.getRewardSymbol());
        switch (rewardSymbol) {
            case VOUCHER:
                //xx元优惠券/x.x折优惠券
                returnBo.setRewardName("优惠券");
                VoucherRewardBo voucherRewardBo = voucherBoMap.get(rewardGiveResp.getRewardExtraId());
                if (Objects.isNull(voucherRewardBo)) {
                    break;
                }
                String unitStr = ActivityConstant.VOUCHER_DISCOUNT.equals(voucherRewardBo.getVoucherType()) ? "折" : "元";
                rewardValueStr = MathUtils.decimalCutZero(voucherRewardBo.getRewardValue());
                //折扣券可能有95折，9折2种情况:直接干掉0.即可
                if (ActivityConstant.VOUCHER_DISCOUNT.equals(voucherRewardBo.getVoucherType())) {
                    rewardValueStr = rewardValueStr.split("\\.")[1];
                }
                rewardTextBuilder.append(rewardValueStr).append(unitStr);
                break;
            case VOUCHER_PACK:
                returnBo.setRewardName("优惠券礼包");
                VoucherRewardBo voucherPackBo = voucherPackBoMap.get(rewardGiveResp.getRewardExtraId());
                if (Objects.isNull(voucherPackBo)) {
                    break;
                }
                BigDecimal packNum = Objects.nonNull(rewardGiveResp.getRewardGiveValue()) && rewardGiveResp.getRewardGiveValue().compareTo(BigDecimal.ZERO) > 0 ? rewardGiveResp.getRewardGiveValue() : new BigDecimal("1");
                BigDecimal rewardValue = voucherPackBo.getRewardValue().multiply(packNum).setScale(2, BigDecimal.ROUND_HALF_UP);
                rewardTextBuilder.append(MathUtils.decimalCutZero(rewardValue)).append("元");
                break;
            case COIN:
                returnBo.setRewardName("金币");
                rewardTextBuilder.append(rewardValueStr);
                break;
            case SECURITY_FUND:
                returnBo.setRewardName("服务保障金");
                rewardTextBuilder.append(rewardValueStr).append("元");
                break;
            case LOTTERY_DRAW:
                returnBo.setRewardName("抽奖机会");
                rewardTextBuilder.append(rewardValueStr).append("次");
                break;
            default:
                break;
        }
        returnBo.setRewardValue(rewardTextBuilder.toString());
        return returnBo;
    }

    /**
     * 获取目标文案
     *
     * @param activityTaskTarget
     * @return
     */
    private String returnTargetText(GetActivityTaskTargetDetailResp.ActivityTaskTarget activityTaskTarget) {
        StringBuilder targetTextBuilder = new StringBuilder();
        BigDecimal targetQuantityValue = activityTaskTarget.getTargetQuantityValue();
        BigDecimal targetAmountValue = activityTaskTarget.getTargetAmountValue();
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.fromSymbol(activityTaskTarget.getTaskSymbol());
        switch (taskTypeEnum) {
            case CREATE_ORDER:
                targetTextBuilder.append("下").append(MathUtils.decimalCutZero(targetQuantityValue)).append("单");
                break;
            case APPOINT_MASTER:
                targetTextBuilder.append("指派").append(MathUtils.decimalCutZero(targetQuantityValue)).append("单");
                break;
            case ORDER_PAY:
                String targetValueStr = MathUtils.decimalCutZero(targetAmountValue.compareTo(BigDecimal.ZERO) > 0 ? targetAmountValue : targetQuantityValue);
                targetTextBuilder.append("付款").append(targetValueStr).append(targetAmountValue.compareTo(BigDecimal.ZERO) > 0 ? "元" : "单");
                break;
            case ORDER_CHECK:
                String orderCheckTargetValueStr = MathUtils.decimalCutZero(targetAmountValue.compareTo(BigDecimal.ZERO) > 0 ? targetAmountValue : targetQuantityValue);
                targetTextBuilder.append("验收").append(orderCheckTargetValueStr).append(targetAmountValue.compareTo(BigDecimal.ZERO) > 0 ? "元" : "单");
                break;
            default:
                break;
        }
        return targetTextBuilder.toString();
    }


    @Override
    public List<GetUserActivityTaskShortApiResp> getUserActivityTaskShort(GetActivityDetailApiRqt getActivityDetailApiRqt) {


        try {
            GetActivityDetailRqt getActivityDetailRqt = new GetActivityDetailRqt();
            BeanUtils.copyProperties(getActivityDetailApiRqt, getActivityDetailRqt);
            getActivityDetailRqt.setUserId(getActivityDetailApiRqt.getUserId());
            List<GetUserActivityTaskShortResp> shortResultList = activityBusinessServiceApi.getActivityTaskShort(getActivityDetailRqt);

            List<GetUserActivityTaskShortApiResp> respList = new ArrayList<>();
            if (CollectionUtils.isEmpty(shortResultList)) {
                return respList;
            }
            shortResultList.stream().forEach(result -> {
                GetUserActivityTaskShortApiResp resp = new GetUserActivityTaskShortApiResp();
                BeanUtils.copyProperties(result, resp);
                respList.add(resp);
            });
            return respList;
        } catch (ApiAccessException e) {
            throw new BusException("task_short_fail", super.getErrorMsg(e.getMessage()));
        }
    }

    /**
     * 商家充值页面优惠活动信息
     * 返回单笔充值文案：
     *
     * @param getRechargeDiscountActivityApiRqt
     * @return
     */
    @Override
    public GetRechargeDiscountActivityApiResp getRechargeDiscountInfo(GetRechargeDiscountActivityApiRqt getRechargeDiscountActivityApiRqt) {
        try {
            GetRechargeDiscountActivityRqt getRechargeDiscountRqt = new GetRechargeDiscountActivityRqt();
            getRechargeDiscountRqt.setUserClass(getRechargeDiscountActivityApiRqt.getUserClass());
            getRechargeDiscountRqt.setUserId(getRechargeDiscountActivityApiRqt.getUserId());
            GetRechargeDiscountActivityResp rechargeDiscountResp = activityBusinessServiceApi.getRechargeDiscountInfo(getRechargeDiscountRqt);

            GetRechargeDiscountActivityApiResp resp = new GetRechargeDiscountActivityApiResp();
            if (ObjectUtils.isEmpty(rechargeDiscountResp)) {
                return resp;
            }
            resp.setIsShowRechargeDiscount(rechargeDiscountResp.getIsShowRechargeDiscount());
            if (CollectionUtils.isEmpty(rechargeDiscountResp.getActivityTaskList())) {
                return resp;
            }

            //获取所有组合奖励id，批量查询
            List<GetRechargeDiscountActivityResp.RechargeActivityTask> activityTaskList = rechargeDiscountResp.getActivityTaskList();
            List<Long> combineRewardConfigIdList = activityTaskList.stream()
                    .filter(e -> RewardSymbol.COMBINE.type.equals(e.getRewardSymbol()) || RewardSymbol.COMBINE_CHOOSE_REWARD.type.equals(e.getRewardSymbol())).map(GetRechargeDiscountActivityResp.RechargeActivityTask::getRewardExtraId).distinct().collect(Collectors.toList());

            //批量获取组合奖励数据
            List<CombineRewardConfig> combineRewardConfigList = null;
            if (CollectionUtils.isNotEmpty(combineRewardConfigIdList)) {
                BatchSelectRqt batchSelectRqt = new BatchSelectRqt();
                batchSelectRqt.setList(combineRewardConfigIdList);
                combineRewardConfigList = combinedPageServiceApi.batchSelect(batchSelectRqt);
            }
            //防止下面匹配组合礼包报空指针
            List<CombineRewardConfig> finalCombineRewardConfigList = CollectionUtils.isEmpty(combineRewardConfigList) ? new ArrayList<>() : combineRewardConfigList;
            //批量查询现金券信息：单张折扣券需要展示成折扣值
            Map<Integer, BigDecimal> combineRewardDiscountMap = this.queryCombineRewardSingleDiscount(finalCombineRewardConfigList);
            List<GetRechargeDiscountActivityApiResp.RechargeActivityTask> taskList = new ArrayList<>();
            rechargeDiscountResp.getActivityTaskList().stream().forEach(vo -> {
                GetRechargeDiscountActivityApiResp.RechargeActivityTask taskVo = new GetRechargeDiscountActivityApiResp.RechargeActivityTask();
                taskVo.setActivityTaskId(vo.getActivityTaskId());
                if (!StringUtils.isEmpty(vo.getTargetAmountValueRange())) {
                    List<Integer> rangeList = JSONArray.parseArray(vo.getTargetAmountValueRange(), Integer.class);
                    taskVo.setTargetAmountValueRange(rangeList.toArray(new Integer[rangeList.size()]));
                }
                RewardSymbol rewardSymbol = RewardSymbol.fromType(vo.getRewardSymbol());
                BigDecimal rewardValue;
                String rewardValueStr;
                GetActivityDetailApiResp.ActivityTaskList activityTaskListTmp = new GetActivityDetailApiResp.ActivityTaskList();
                StringBuilder showTitle = new StringBuilder();
                //判断是否使用自定义文案
                if (!StringUtils.isEmpty(vo.getTagText())) {
                    try {
                        RechargeRewardTextBo rechargeRewardTextBo = JSONObject.parseObject(vo.getTagText(), RechargeRewardTextBo.class);
                        if (RewardSymbol.COMBINE_CHOOSE_REWARD.code.equals(rewardSymbol.code)) {
                            String customizeRewardText = rechargeRewardTextBo.getCustomizeRewardText();
                            String text = customizeRewardText.replace(",", "/");
                            taskVo.setShowTitle(text + "(二选一)");
                            taskList.add(taskVo);
                            return;
                        }

                        if (rechargeRewardTextBo.getRewardTextType() != null && rechargeRewardTextBo.getRewardTextType() == 1 && !StringUtils.isEmpty(rechargeRewardTextBo.getCustomizeRewardText())) {
                            taskVo.setShowTitle(rechargeRewardTextBo.getCustomizeRewardText());
                            taskList.add(taskVo);
                            return;
                        }
                    } catch (Exception e) {
                        log.error("充值标签转换异常tagText:{}", vo.getTagText());
                    }
                }
                switch (rewardSymbol) {
                    case VOUCHER:
                        rewardValue = this.getRewardValue(vo.getRewardExtraId(), vo.getRewardGiveValue(), vo.getRewardSymbol(), activityTaskListTmp);
                        if (ActivityConstant.VOUCHER_DISCOUNT.equals(activityTaskListTmp.getVoucherType())) {
                            //折扣需要先乘10
                            rewardValueStr = MathUtils.decimalCutZero(rewardValue.multiply(new BigDecimal(10)));
                            showTitle.append("送").append(rewardValueStr).append("折优惠券");
                        } else {
                            rewardValueStr = MathUtils.decimalCutZero(rewardValue);
                            showTitle.append("送").append(rewardValueStr).append("元优惠券");
                        }
                        break;
                    case VOUCHER_PACK:
                        rewardValue = this.getRewardValue(vo.getRewardExtraId(), vo.getRewardGiveValue(), vo.getRewardSymbol(), activityTaskListTmp);
                        rewardValueStr = MathUtils.decimalCutZero(rewardValue);
                        showTitle.append("送").append(rewardValueStr).append("元优惠券礼包");
                        break;
                    case COIN:
                        rewardValueStr = MathUtils.decimalCutZero(vo.getRewardGiveValue());
                        showTitle.append("送").append(rewardValueStr).append("金币");
                        break;
                    case SECURITY_FUND:
                        if (RewardGiveTypeEnum.FIXED_VALUE.type.equals(vo.getRewardGiveType())) {
                            rewardValueStr = MathUtils.decimalCutZero(vo.getRewardGiveValue());
                            showTitle.append("送").append(rewardValueStr).append("元服务保障金");
                        } else {
                            showTitle.append("送n元服务保障金");
                            taskVo.setPointValue(vo.getRewardGiveValue());
                        }
                        break;
                    case COMBINE:
                        BigDecimal totalValue = this.setCombineShowTitle(showTitle, finalCombineRewardConfigList, combineRewardDiscountMap, vo.getRewardExtraId(), taskVo, "+");
                        taskVo.setShowLabelValue(totalValue);
                        break;
                    case COMBINE_CHOOSE_REWARD:
                        this.setChooseCombineShowTitle(showTitle, finalCombineRewardConfigList, combineRewardDiscountMap, vo.getRewardExtraId(), taskVo, "/");
                        break;
                    case LOTTERY_DRAW:
                        rewardValueStr = MathUtils.decimalCutZero(vo.getRewardGiveValue());
                        showTitle.append("送").append(rewardValueStr).append("次抽奖机会");
                        break;
                    default:
                        new BusException("未找到匹配的奖励类型");
                }
                taskVo.setShowTitle(showTitle.toString());
                taskList.add(taskVo);
            });
            resp.setActivityTaskList(taskList);
            return resp;
        } catch (Exception e) {
            throw new BusException("recharge_discount_fail", super.getErrorMsg(e.getMessage()));
        }
    }

    /**
     * 设置组合奖励(组合领取和多选一领取)文案
     *
     * @param showTitle
     * @param finalCombineRewardConfigList
     * @param combineRewardDiscountMap
     * @param rewardExtraId
     * @param taskVo
     * @param intervalSymbol               间隔符号
     */
    public BigDecimal setCombineShowTitle(StringBuilder showTitle
            , List<CombineRewardConfig> finalCombineRewardConfigList
            , Map<Integer, BigDecimal> combineRewardDiscountMap
            , Long rewardExtraId
            , GetRechargeDiscountActivityApiResp.RechargeActivityTask taskVo
            , String intervalSymbol) {
        CombineRewardConfig combineRewardConfig = finalCombineRewardConfigList.stream().filter(e -> e.getCombineRewardConfigId().equals(rewardExtraId)).findFirst().orElseThrow(() -> new BusException("找不到组合奖励数据"));
        List<CombinePackageItemBo> combinePackageItemBos = JSONArray.parseArray(combineRewardConfig.getPackageList(), CombinePackageItemBo.class);

        BigDecimal totalValue = new BigDecimal("0");
        int j = 0;
        for (CombinePackageItemBo combinePackageItemBo : combinePackageItemBos) {
            String combineRewardSymbol = combinePackageItemBo.getRewardSymbol();
            String combineRewardValueStr;
            if (j != 0) {
                showTitle.append(intervalSymbol);
                if (",".equals(intervalSymbol)) {
                    showTitle.append("送");
                }
            }
            if (j == 0) {
                showTitle.append("送");
            }
            //折扣券,折扣展示值需乘10
            if (this.combineRewardIsDiscount(combinePackageItemBo.getRewardExtraId(), combineRewardDiscountMap)) {
                BigDecimal discountValue = combineRewardDiscountMap.get(combinePackageItemBo.getRewardExtraId()).multiply(new BigDecimal(10));
                combineRewardValueStr = MathUtils.decimalCutZero(discountValue);
                showTitle.append(combineRewardValueStr).append("折优惠券");

                totalValue = totalValue.add(discountValue);
            } else if (RewardSymbol.VOUCHER.type.equals(combineRewardSymbol)) {
                //普通优惠券
                BigDecimal voucherValue = this.getRewardValue(Long.valueOf(combinePackageItemBo.getRewardExtraId()), null, combinePackageItemBo.getRewardSymbol(), null);
                combineRewardValueStr = MathUtils.decimalCutZero(voucherValue);
                showTitle.append(combineRewardValueStr).append("元优惠券");

                totalValue = totalValue.add(voucherValue);
            } else if (RewardSymbol.VOUCHER_PACK.type.equals(combineRewardSymbol)) {
                //券包
                BigDecimal voucherPackValue = this.getRewardValue(Long.valueOf(combinePackageItemBo.getRewardExtraId()), null, combineRewardSymbol, null);
                combineRewardValueStr = MathUtils.decimalCutZero(voucherPackValue);
                showTitle.append(combineRewardValueStr).append("元优惠券礼包");

                totalValue = totalValue.add(voucherPackValue);
            } else if (RewardSymbol.COIN.type.equals(combineRewardSymbol)) {
                BigDecimal coinValue = combinePackageItemBo.getGiveValue();
                combineRewardValueStr = MathUtils.decimalCutZero(coinValue);
                showTitle.append(combineRewardValueStr).append("金币");

                totalValue = totalValue.add(coinValue.divide(new BigDecimal("10")));
            } else if (RewardSymbol.SECURITY_FUND.type.equals(combineRewardSymbol)) {
                //服务保障金-固定金额
                if (RewardGiveTypeEnum.FIXED_VALUE.type.equals(combinePackageItemBo.getRewardGiveType())) {
                    BigDecimal securityFundValue = combinePackageItemBo.getGiveValue();
                    combineRewardValueStr = MathUtils.decimalCutZero(securityFundValue);
                    showTitle.append(combineRewardValueStr).append("元服务保障金");

                    totalValue = totalValue.add(securityFundValue);
                } else if (RewardGiveTypeEnum.RETURN_POINT.type.equals(combinePackageItemBo.getRewardGiveType())) {
                    //服务保障金-返点
                    showTitle.append("n元服务保障金");
                    taskVo.setPointValue(combinePackageItemBo.getGiveValue());
                }
            } else if (RewardSymbol.LOTTERY_DRAW.type.equals(combineRewardSymbol)) {
                combineRewardValueStr = MathUtils.decimalCutZero(combinePackageItemBo.getGiveValue());
                showTitle.append(combineRewardValueStr).append("次抽奖机会");
            }
            j++;
        }
        return totalValue;
    }

    /**
     * 设置组合奖励(组合领取和多选一领取)文案
     *
     * @param showTitle
     * @param finalCombineRewardConfigList
     * @param combineRewardDiscountMap
     * @param rewardExtraId
     * @param taskVo
     * @param intervalSymbol               间隔符号
     */
    public BigDecimal setChooseCombineShowTitle(StringBuilder showTitle
            , List<CombineRewardConfig> finalCombineRewardConfigList
            , Map<Integer, BigDecimal> combineRewardDiscountMap
            , Long rewardExtraId
            , GetRechargeDiscountActivityApiResp.RechargeActivityTask taskVo
            , String intervalSymbol) {
        CombineRewardConfig combineRewardConfig = finalCombineRewardConfigList.stream().filter(e -> e.getCombineRewardConfigId().equals(rewardExtraId)).findFirst().orElseThrow(() -> new BusException("找不到组合奖励数据"));
        List<CombinePackageItemBo> combinePackageItemBos = JSONArray.parseArray(combineRewardConfig.getPackageList(), CombinePackageItemBo.class);

        BigDecimal totalValue = new BigDecimal("0");
        int j = 0;
        for (CombinePackageItemBo combinePackageItemBo : combinePackageItemBos) {
            String combineRewardSymbol = combinePackageItemBo.getRewardSymbol();
            String combineRewardValueStr;
            if (j != 0) {
                showTitle.append(intervalSymbol);
                if ("/".equals(intervalSymbol)) {
                    showTitle.append("送");
                }
            }
            if (j == 0) {
                showTitle.append("送");
            }
            //折扣券,折扣展示值需乘10
            if (this.combineRewardIsDiscount(combinePackageItemBo.getRewardExtraId(), combineRewardDiscountMap)) {
                BigDecimal discountValue = combineRewardDiscountMap.get(combinePackageItemBo.getRewardExtraId()).multiply(new BigDecimal(10));
                combineRewardValueStr = MathUtils.decimalCutZero(discountValue);
                showTitle.append(combineRewardValueStr).append("折优惠券");

                totalValue = totalValue.add(discountValue);
            } else if (RewardSymbol.VOUCHER.type.equals(combineRewardSymbol)) {
                //普通优惠券
                BigDecimal voucherValue = this.getRewardValue(Long.valueOf(combinePackageItemBo.getRewardExtraId()), null, combinePackageItemBo.getRewardSymbol(), null);
                combineRewardValueStr = MathUtils.decimalCutZero(voucherValue);
                showTitle.append(combineRewardValueStr).append("元优惠券");

                totalValue = totalValue.add(voucherValue);
            } else if (RewardSymbol.VOUCHER_PACK.type.equals(combineRewardSymbol)) {
                //券包
                BigDecimal voucherPackValue = this.getRewardValue(Long.valueOf(combinePackageItemBo.getRewardExtraId()), null, combineRewardSymbol, null);
                combineRewardValueStr = MathUtils.decimalCutZero(voucherPackValue);
                showTitle.append(combineRewardValueStr).append("元优惠券礼包");

                totalValue = totalValue.add(voucherPackValue);
            } else if (RewardSymbol.COIN.type.equals(combineRewardSymbol)) {
                BigDecimal coinValue = combinePackageItemBo.getGiveValue();
                combineRewardValueStr = MathUtils.decimalCutZero(coinValue);
                showTitle.append(combineRewardValueStr).append("金币");

                totalValue = totalValue.add(coinValue.divide(new BigDecimal("10")));
            } else if (RewardSymbol.SECURITY_FUND.type.equals(combineRewardSymbol)) {
                //服务保障金-固定金额
                if (RewardGiveTypeEnum.FIXED_VALUE.type.equals(combinePackageItemBo.getRewardGiveType())) {
                    BigDecimal securityFundValue = combinePackageItemBo.getGiveValue();
                    combineRewardValueStr = MathUtils.decimalCutZero(securityFundValue);
                    showTitle.append(combineRewardValueStr).append("元服务保障金");

                    totalValue = totalValue.add(securityFundValue);
                } else if (RewardGiveTypeEnum.RETURN_POINT.type.equals(combinePackageItemBo.getRewardGiveType())) {
                    //服务保障金-返点
                    showTitle.append("n元服务保障金");
                    taskVo.setPointValue(combinePackageItemBo.getGiveValue());
                }
            } else if (RewardSymbol.LOTTERY_DRAW.type.equals(combineRewardSymbol)) {
                combineRewardValueStr = MathUtils.decimalCutZero(combinePackageItemBo.getGiveValue());
                showTitle.append(combineRewardValueStr).append("次抽奖机会");
            }
            j++;
        }
        showTitle.append("(二选一)");
        return totalValue;
    }

    /**
     * 判断组合奖励是否是优惠券且是折扣券
     */
    public boolean combineRewardIsDiscount(Integer rewardExtraId, Map<Integer, BigDecimal> combineRewardDiscountMap) {
        if (rewardExtraId != null && rewardExtraId > 0 && combineRewardDiscountMap != null && combineRewardDiscountMap.get(rewardExtraId) != null) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 查询组合奖励中只有一张优惠券
     *
     * @param finalCombineRewardConfigList
     * @return
     */
    private Map<Integer, BigDecimal> queryCombineRewardSingleDiscount(List<CombineRewardConfig> finalCombineRewardConfigList) {
        if (CollectionUtils.isEmpty(finalCombineRewardConfigList)) {
            return null;
        }
        List<Integer> queryRewardExtraIdList = new ArrayList<>();
        for (CombineRewardConfig combineVo : finalCombineRewardConfigList) {
            List<CombinePackageItemBo> combinePackageList = JSONArray.parseArray(combineVo.getPackageList(), CombinePackageItemBo.class);
            List<CombinePackageItemBo> filterCombinePackageList = combinePackageList.stream().filter(f -> RewardSymbol.VOUCHER.type.equals(f.getRewardSymbol())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterCombinePackageList) && filterCombinePackageList.size() == 1) {
                queryRewardExtraIdList.addAll(filterCombinePackageList.stream().map(CombinePackageItemBo::getRewardExtraId).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(queryRewardExtraIdList)) {
            return null;
        }
        Map<Long, VoucherRewardBo> voucherEventMap = super.getOnlyVoucherBatchGetReward(queryRewardExtraIdList);
        if (voucherEventMap == null) {
            return null;
        }
        Map<Integer, BigDecimal> resultMap = new HashMap<>();
        voucherEventMap.forEach((k, v) -> {
            if (v != null && ActivityConstant.VOUCHER_DISCOUNT.equals(v.getVoucherType())) {
                resultMap.put(k.intValue(), v.getRewardValue());
            }
        });
        return resultMap;
    }

    /**
     * 商家订单列表活动匹配标签
     */
    @Override
//    @UserLoginInfo
    @ExceptionHandle(note = "商家订单列表活动匹配标签异常")
    public MatchUserOrderListTagApiResp matchUserOrderListTag(MatchUserOrderListTagRqt matchUserOrderListTagRqt) {
        try {
            String valueBykey = redisExUtil.getValueBykey(
                    StringUtils.isEmpty(matchUserOrderListTagRqt.getToken()) ?
                            matchUserOrderListTagRqt.getTokenApp() : matchUserOrderListTagRqt.getToken(), UserTypeEnum.MERCHANT.type);
            JSONObject userInfo = JSONObject.parseObject(valueBykey);
            UserInfoWebDtoResp userInfoWebDtoResp = JSONObject.toJavaObject(userInfo, UserInfoWebDtoResp.class);

            Long userId = userInfoWebDtoResp.getUserId();
//            List<String> orderNoList = matchUserOrderListTagRqt.getOrderLists().stream().map(MatchUserOrderListTag::getOrderNo).collect(Collectors.toList());
//            GetOrderBaseByOrderNoListRqt getOrderBaseByOrderNoListRqt = new GetOrderBaseByOrderNoListRqt();
//            getOrderBaseByOrderNoListRqt.setOrderNoList(orderNoList);
//
//            List<OrderBase> orderBasesList = userOrderResourceControllerApi.getOrderBaseByOrderNoList(getOrderBaseByOrderNoListRqt);

            MatchUserOrderListTagApiResp matchUserOrderListTagApiResp = new MatchUserOrderListTagApiResp();
            matchUserOrderListTagApiResp.setMatchOrderFlag(0);//默认不匹配
            List<Long> userGroupIds = new ArrayList<>();
            try {
                //            userGroupIds = bigDataAliYunUtils.getUserGroupIds(String.valueOf(matchUserOrderListTag.getAccountId()), "1", "1");
                userGroupIds = bigDataAliYunUtils.getUserGroupIds(userId.toString(), "1", "1");
            } catch (ParseException e) {
                e.printStackTrace();
            }
            List<Long> finalUserGroupIds = userGroupIds;
            List<MasterOrderListTaskRqt.MatchUserOrderListTag> orderListTagsList = matchUserOrderListTagRqt.getOrderLists().stream().
                    map(matchUserOrderListTag -> {
                        MasterOrderListTaskRqt.MatchUserOrderListTag matchUserOrderListTagNew = new MasterOrderListTaskRqt.MatchUserOrderListTag();
                        BeanUtils.copyProperties(matchUserOrderListTag, matchUserOrderListTagNew);
                        if (!StringUtils.isEmpty(matchUserOrderListTag.getServeIds())) {
                            List<Long> serveIdList = null;
                            if (matchUserOrderListTag.getServeIds().contains("[") && matchUserOrderListTag.getServeIds().contains("]")) {
                                serveIdList = JSONArray.parseArray(matchUserOrderListTag.getServeIds(), Long.class).stream().distinct().collect(Collectors.toList());
                            } else {
                                serveIdList = Arrays.stream(matchUserOrderListTag.getServeIds().split(",")).distinct().map(Long::valueOf).collect(Collectors.toList());
                            }
                            matchUserOrderListTagNew.setServeIds(serveIdList);
//                            matchUserOrderListTagNew.setOrderGoodsLv3Id(serveIdList.get(0).intValue());
                            serveIdList = null;
                        }
                        if (matchUserOrderListTag.getOfferNum() != null) {
                            matchUserOrderListTagNew.setOfferNum(Long.valueOf(matchUserOrderListTag.getOfferNum()));
                        }
                        //补充用户id
//                        Optional<OrderBase> orderBaseOptional = orderBasesList.stream().filter(f -> f.getOrderNo().equals(matchUserOrderListTag.getOrderNo())).findFirst();
//                        orderBaseOptional.ifPresent(
//                                orderBase -> matchUserOrderListTag.setAccountId(orderBase.getUserId()));
                        Map<String, Object> ruleVariableMap = this.getRuleVatableMapListTag(matchUserOrderListTag, userId,
                                finalUserGroupIds);
                        matchUserOrderListTagNew.setRuleVariableMap(ruleVariableMap);
                        matchUserOrderListTagNew.setUserCrowIds(finalUserGroupIds);

                        matchUserOrderListTagNew.setCreateOrderUserId(userId);
                        return matchUserOrderListTagNew;
                    }).collect(Collectors.toList());

            MasterOrderListTaskRqt masterOrderListTaskRqt = new MasterOrderListTaskRqt();
            masterOrderListTaskRqt.setOrderLists(orderListTagsList);
            masterOrderListTaskRqt.setUserId(userId);
            log.info("masterOrderListTaskRqt::" + JSONObject.toJSONString(masterOrderListTaskRqt));
            MatchUserOrderListTagResp matchUserOrderListTagResp = merchantActivityServiceApi.matchUserOrderListTag(masterOrderListTaskRqt);
            if (matchUserOrderListTagResp == null || ActivityConstant.NO_FALSE == matchUserOrderListTagResp.getMatchOrderFlag()) {
                masterOrderListTaskRqt = null;
                return matchUserOrderListTagApiResp;
            }
            matchUserOrderListTagApiResp.setMatchOrderFlag(matchUserOrderListTagResp.getMatchOrderFlag());
            List<MatchUserOrderListTagApiResp.UserOrderItem> userOrderListItemList = matchUserOrderListTagResp.getUserOrderListItemList().stream().
                    map(userOrderItem -> {
                        MatchUserOrderListTagApiResp.UserOrderItem userOrderItemNew = new MatchUserOrderListTagApiResp.UserOrderItem();
                        BeanUtils.copyProperties(userOrderItem, userOrderItemNew);
                        return userOrderItemNew;
                    }).collect(Collectors.toList());

            matchUserOrderListTagApiResp.setUserOrderListItemList(userOrderListItemList);

            userOrderListItemList = null;
            masterOrderListTaskRqt = null;

            return matchUserOrderListTagApiResp;
        } catch (Exception e) {
            throw new BusException("get_merchant_orderList_fail", super.getErrorMsg(e.getMessage()));
        }

    }

    private Map<String, Object> getRuleVatableMapListTag(MatchUserOrderListTag matchUserOrderListTag, Long userId, List<Long> userGroupIds) {
        Map<String, Object> ruleVariableMap = new HashMap<>();
        MasterOrderInfoBo masterOrderInfoBo = new MasterOrderInfoBo();
        BeanUtils.copyProperties(matchUserOrderListTag, masterOrderInfoBo);

        masterOrderInfoBo.setServeIds(
                com.wanshifu.framework.utils.StringUtils.splitCommaToList(matchUserOrderListTag.getServeIds()).stream().map(Long::valueOf).collect(
                        Collectors.toList()));
        masterOrderInfoBo.setThirdDivisionId(matchUserOrderListTag.getThirdDivisionId());
        masterOrderInfoBo.setServeType(matchUserOrderListTag.getServeType());
        masterOrderInfoBo.setOrderNo(matchUserOrderListTag.getOrderNo());
        masterOrderInfoBo.setGlobalOrderTraceId(matchUserOrderListTag.getGlobalOrderTraceId());
        masterOrderInfoBo.setUserId(userId);

        ruleVariableMap.put("appointMethod", matchUserOrderListTag.getAppointMethod());
        ruleVariableMap.put("groupGoodNum", matchUserOrderListTag.getGroupGoodNum());
        ruleVariableMap.put("offerNum", matchUserOrderListTag.getOfferNum());
        ruleVariableMap.put("userCrowIds", userGroupIds);
        //执行时间
        ruleVariableMap.put("currentDateTime", new Date().getTime());
        ruleVariableMap.put("orderCreateTime", Objects.nonNull(matchUserOrderListTag.getCreateOrderTime()) ? matchUserOrderListTag.getCreateOrderTime() : null);
        ruleVariableMap.put("orderCreateAbsoluteContainTime", Objects.nonNull(matchUserOrderListTag.getCreateOrderTime()) ? matchUserOrderListTag.getCreateOrderTime().getTime() : null);
        ruleVariableMap.put("orderInfoBo", JSONObject.toJSONString(masterOrderInfoBo));

        return ruleVariableMap;
    }

    /**
     * 商家订单列表活动匹配标签
     */
    @Override
//    @UserLoginInfo
    @ExceptionHandle(note = "商家订单详情匹配标签异常")
    public MatchOrderActivityDetailApiResp matchOrderActivityDetail(MatchOrderActivityDetailApiRqt matchOrderActivityDetailApiRqt) {
        try {
            String valueBykey = redisExUtil.getValueBykey(
                    StringUtils.isEmpty(matchOrderActivityDetailApiRqt.getToken()) ?
                            matchOrderActivityDetailApiRqt.getTokenApp() : matchOrderActivityDetailApiRqt.getToken(), UserTypeEnum.MERCHANT.type);
            JSONObject userInfo = JSONObject.parseObject(valueBykey);
            UserInfoWebDtoResp userInfoWebDtoResp = JSONObject.toJavaObject(userInfo, UserInfoWebDtoResp.class);

            Long userId = userInfoWebDtoResp.getUserId();
            MatchOrderActivityDetailRqt matchOrderActivityDetailRqt = new MatchOrderActivityDetailRqt();
            BeanUtils.copyProperties(matchOrderActivityDetailApiRqt, matchOrderActivityDetailRqt);

//            GetOrderBaseByOrderNoRqt getOrderBaseByOrderNoRqt = new GetOrderBaseByOrderNoRqt();
//            getOrderBaseByOrderNoRqt.setOrderNo(matchOrderActivityDetailApiRqt.getOrderNo());
//
//            OrderBase orderBase = userOrderResourceControllerApi.getOrderBaseByOrderNo(getOrderBaseByOrderNoRqt);
            if (!StringUtils.isEmpty(matchOrderActivityDetailApiRqt.getServeIds())) {
                List<Long> serveIdList = Arrays.stream(matchOrderActivityDetailApiRqt.getServeIds().split(",")).distinct().map(Long::valueOf).collect(Collectors.toList());
                matchOrderActivityDetailRqt.setServeIds(serveIdList);
                serveIdList = null;
            }
            if (matchOrderActivityDetailApiRqt.getOfferNum() != null) {
                matchOrderActivityDetailRqt.setOfferNum(Long.valueOf(matchOrderActivityDetailApiRqt.getOfferNum()));
            }
//            if(orderBase!=null){
//                matchOrderActivityDetailApiRqt.setAccountId(orderBase.getUserId());
//            }
            matchOrderActivityDetailRqt.setUserId(userId);
            //添加下单用户人群
            List<Long> userGroupIds = new ArrayList<>();
            try {
                //            userGroupIds = bigDataAliYunUtils.getUserGroupIds(String.valueOf(matchUserOrderListTag.getAccountId()), "1", "1");
                userGroupIds = bigDataAliYunUtils.getUserGroupIds(userId.toString(), "1", "1");
            } catch (ParseException e) {
                e.printStackTrace();
            }
            matchOrderActivityDetailRqt.setUserCrowIds(userGroupIds);
            Map<String, Object> ruleVariableMap = this.getRuleVatableMapDetail(matchOrderActivityDetailApiRqt, userId, userGroupIds);
            matchOrderActivityDetailRqt.setRuleVariableMap(ruleVariableMap);
            log.info(JSONObject.toJSONString(matchOrderActivityDetailRqt));
            MatchOrderActivityDetailResp matchOrderActivityDetail = merchantActivityServiceApi.matchOrderActivityDetail(matchOrderActivityDetailRqt);
            if (matchOrderActivityDetail == null) {
                return null;
            }

            MatchOrderActivityDetailApiResp matchOrderActivityDetailApiResp = new MatchOrderActivityDetailApiResp();
            BeanUtils.copyProperties(matchOrderActivityDetail, matchOrderActivityDetailApiResp);
            //优惠券且为可变额度优惠券
            if (matchOrderActivityDetail.getRewardExtraId() != null && matchOrderActivityDetail.getEventType() == 1) {
                List<MasterOfferRuleApiResp> masterOfferRuleApiResps = matchOrderActivityDetail.getMasterOfferRuleResps().stream().map(masterOfferRuleResp -> {
                    MasterOfferRuleApiResp masterOfferRuleApiResp = new MasterOfferRuleApiResp();
                    masterOfferRuleApiResp.setMasterOfferPrice(masterOfferRuleResp.getMasterOfferPrice());
                    masterOfferRuleApiResp.setMasterId(masterOfferRuleResp.getMasterId());
                    masterOfferRuleApiResp.setRewardExtraAmount(masterOfferRuleResp.getRewardExtraAmount());
                    masterOfferRuleApiResp.setThresholdAmount(masterOfferRuleResp.getThresholdAmount());
                    return masterOfferRuleApiResp;
                }).collect(Collectors.toList());
                matchOrderActivityDetailApiResp.setMasterOfferRuleResps(masterOfferRuleApiResps);
            }

            return matchOrderActivityDetailApiResp;
        } catch (Exception e) {
            throw new BusException("get_merchant_order_detail_fail", super.getErrorMsg(e.getMessage()));
        }

    }

    private Map<String, Object> getRuleVatableMapDetail(MatchOrderActivityDetailApiRqt matchOrderActivityDetailApiRqt,
                                                        Long userId, List<Long> userGroupIds) {
        Map<String, Object> ruleVariableMap = new HashMap<>();
        MasterOrderInfoBo masterOrderInfoBo = new MasterOrderInfoBo();
        BeanUtils.copyProperties(matchOrderActivityDetailApiRqt, masterOrderInfoBo);
        masterOrderInfoBo.setServeIds(
                com.wanshifu.framework.utils.StringUtils.splitCommaToList(matchOrderActivityDetailApiRqt.getServeIds()).stream().map(Long::valueOf).collect(
                        Collectors.toList()));
        masterOrderInfoBo.setThirdDivisionId(matchOrderActivityDetailApiRqt.getThirdDivisionId());
        masterOrderInfoBo.setServeType(matchOrderActivityDetailApiRqt.getServeType());
        masterOrderInfoBo.setOrderNo(matchOrderActivityDetailApiRqt.getOrderNo());
        masterOrderInfoBo.setGlobalOrderTraceId(matchOrderActivityDetailApiRqt.getGlobalOrderTraceId());
        masterOrderInfoBo.setUserId(matchOrderActivityDetailApiRqt.getUserId());

        ruleVariableMap.put("appointMethod", matchOrderActivityDetailApiRqt.getAppointMethod());
        ruleVariableMap.put("groupGoodNum", matchOrderActivityDetailApiRqt.getGroupGoodNum());
        ruleVariableMap.put("offerNum", matchOrderActivityDetailApiRqt.getOfferNum());
        ruleVariableMap.put("userCrowIds", userGroupIds);
        //执行时间
        ruleVariableMap.put("currentDateTime", new Date().getTime());
        ruleVariableMap.put("orderCreateTime", Objects.nonNull(matchOrderActivityDetailApiRqt.getCreateOrderTime()) ? matchOrderActivityDetailApiRqt.getCreateOrderTime() : null);
        ruleVariableMap.put("orderCreateAbsoluteContainTime", Objects.nonNull(matchOrderActivityDetailApiRqt.getCreateOrderTime()) ? matchOrderActivityDetailApiRqt.getCreateOrderTime().getTime() : null);
        ruleVariableMap.put("orderInfoBo", JSONObject.toJSONString(masterOrderInfoBo));

        return ruleVariableMap;
    }

    /**
     * 家庭免费领券活动 - 领取优惠券
     */
    @Override
    public ReceiveVoucherResp receiveVoucher(ReceiveVoucherRqt rqt, String token) {
        ReceiveVoucherResp resp = new ReceiveVoucherResp();
        try {

            //获取用户登陆信息
            Long userId = getLoginUserId(token);
            if (this.checkQualification(rqt.getActivityId(), userId, ActivityConstant.USER_CLASS_CUSTOMER)) {
                resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_HAS_NO_QUALIFICATION);
                resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_ERROR_TIP_HAS_NO_QUALIFICATION);
                return resp;
            }
            GetActivityBaseRqt getActivityBaseRqt = new GetActivityBaseRqt();
            getActivityBaseRqt.setActivityId(rqt.getActivityId());
            ActivityBase activityBase = activityBackendServiceApi.getActivityBase(getActivityBaseRqt);

            if (ActivityStateEnum.CANCELED.code.equals(activityBase.getActivityState()) || ActivityStateEnum.FINISHED.code.equals(activityBase.getActivityState())) {
                resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_ACTIVITY_END);
                resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_ERROR_TIP_ACTIVITY_END);
                return resp;
            }

            DeductInventoryApiRqt deductInventoryApiRqt = new DeductInventoryApiRqt();
            deductInventoryApiRqt.setActivityId(rqt.getActivityId());
            deductInventoryApiRqt.setUserId(rqt.getUserId());
            DeductInventoryApiResp deductInventoryApiResp = freeVoucherGetBackendServiceApi.deductInventory(deductInventoryApiRqt);
            if (Objects.nonNull(deductInventoryApiResp)) {
                resp.setState(deductInventoryApiResp.getDeductStatus());
                resp.setFailReason(deductInventoryApiResp.getFailMessage());
            }
        } catch (Exception e) {
            log.error("领券异常", e);
            feishuUtils.sendErrorInfoFeishu(e.getMessage());
            resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_FAIL);
            resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_STATUS_EXCEPTION);
        }

        return resp;
    }

    /**
     * 家庭免费领券活动 - 领取优惠券
     */
    public ReceiveVoucherResp receiveVoucherGetOne(ReceiveVoucherGetOneRqt rqt, String token) {
        ReceiveVoucherResp resp = new ReceiveVoucherResp();
        try {

            //获取用户登陆信息
            Long userId = getLoginUserId(token);
            if (this.checkQualification(rqt.getActivityId(), userId, ActivityConstant.USER_CLASS_CUSTOMER)) {
                resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_HAS_NO_QUALIFICATION);
                resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_ERROR_TIP_HAS_NO_QUALIFICATION);
                return resp;
            }
            GetActivityBaseRqt getActivityBaseRqt = new GetActivityBaseRqt();
            getActivityBaseRqt.setActivityId(rqt.getActivityId());
            ActivityBase activityBase = activityBackendServiceApi.getActivityBase(getActivityBaseRqt);

            if (ActivityStateEnum.CANCELED.code.equals(activityBase.getActivityState()) || ActivityStateEnum.FINISHED.code.equals(activityBase.getActivityState())) {
                resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_ACTIVITY_END);
                resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_ERROR_TIP_SOLD_OUT_1);
                return resp;
            }

            DeductInventoryGetOneRqt deductInventoryApiRqt = new DeductInventoryGetOneRqt();
            deductInventoryApiRqt.setActivityId(rqt.getActivityId());
            deductInventoryApiRqt.setUserId(rqt.getUserId());
            deductInventoryApiRqt.setExtraId(rqt.getExtraId());
            deductInventoryApiRqt.setExtraType(rqt.getExtraType());
            DeductInventoryGetOneResp deductInventoryApiResp = freeVoucherGetBackendServiceApi.deductInventoryGetOne(deductInventoryApiRqt);
            if (Objects.nonNull(deductInventoryApiResp)) {
                resp.setState(deductInventoryApiResp.getDeductStatus());
                resp.setFailReason(deductInventoryApiResp.getFailMessage());
            }
        } catch (Exception e) {
            log.error("领券异常", e);
            feishuUtils.sendErrorInfoFeishu(e.getMessage());
            resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_FAIL);
            resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_STATUS_EXCEPTION);
        }

        return resp;
    }

    @Override
    public ReceiveVoucherResp receiveVoucherV2(ReceiveVoucherV2Rqt rqt, String token) {
        //领券活动逻辑
        if (RewardSourceTypeEnum.FREE_VOUCHER_ACTIVITY.type.equals(rqt.getRewardSourceType())) {
            ReceiveVoucherRqt receiveVoucherRqt = new ReceiveVoucherRqt();
            BeanUtils.copyProperties(rqt, receiveVoucherRqt);
            return this.receiveVoucher(receiveVoucherRqt, token);
        } else if (RewardSourceTypeEnum.VIP_EQUITY.type.equals(rqt.getRewardSourceType())) {
            //会员权益领券逻辑
            //获取用户登陆信息
            Long userId = getLoginUserId(token);
            rqt.setUserId(userId);
            return customerService.receiveVipVoucher(rqt);
            //免费领券，单张领取
        } else if (RewardSourceTypeEnum.FREE_VOUCHER_GET_ONE_ACTIVITY.type.equals(rqt.getRewardSourceType())) {
            ReceiveVoucherGetOneRqt receiveVoucherRqt = new ReceiveVoucherGetOneRqt();
            BeanUtils.copyProperties(rqt, receiveVoucherRqt);
            receiveVoucherRqt.setExtraId(Long.valueOf(rqt.getExtraId()));
            return this.receiveVoucherGetOne(receiveVoucherRqt, token);
        }

        ReceiveVoucherResp resp = new ReceiveVoucherResp();
        feishuUtils.sendErrorInfoFeishu("领券接口异常,不支持的奖励来源类型:rqt:" + JSONObject.toJSONString(rqt));
        resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_FAIL);
        resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_STATUS_EXCEPTION);
        return resp;
    }

    /**
     * 获取家庭服务详情标签优惠券列表接口
     *
     * @param rqt
     * @return
     */
    @Override
    public List<GetCustomerServeTagVoucherListApiResp> getCustomerServeTagVoucherList(GetCustomerServeTagVoucherListApiRqt rqt) {
        //获取用户登陆信息
        //家庭token
        String customerToken = httpServletRequest.getHeader("token");
        //获取用户登陆信息
        Long userId = null;
        if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(customerToken)) {
            userId = redisExUtil.checkLoginStatus(customerToken, UserTypeApiEnum.CUSTOMER.type);
        }
        GetCustomerServeTagVoucherListRqt queryRqt = new GetCustomerServeTagVoucherListRqt();
        BeanUtils.copyProperties(rqt, queryRqt);
        queryRqt.setUserId(userId);
        List<GetCustomerServeTagVoucherListApiResp> resp = new ArrayList<>();

        List<GetCustomerServeTagVoucherListResp> getCustomerServeTagVoucherListRespList = activityBusinessServiceApi.getCustomerServeTagVoucherList(queryRqt);
        if (CollectionUtils.isEmpty(getCustomerServeTagVoucherListRespList)) {
            return resp;
        }
        for (GetCustomerServeTagVoucherListResp getCustomerServeTagVoucherListResp : getCustomerServeTagVoucherListRespList) {

            GetCustomerServeTagVoucherListApiResp getCustomerServeTagVoucherListApiResp = new GetCustomerServeTagVoucherListApiResp();
            BeanUtils.copyProperties(getCustomerServeTagVoucherListResp, getCustomerServeTagVoucherListApiResp);
            resp.add(getCustomerServeTagVoucherListApiResp);
        }
        return resp;
    }

    /**
     * 获取登陆用户ID
     */
    private Long getLoginUserId(String token) {
        Long userId = null;
        if (com.wanshifu.framework.utils.StringUtils.isNotEmpty(token)) {
            userId = redisExUtil.checkLoginStatus(token, UserTypeApiEnum.CUSTOMER.type);
        }
        if (Objects.isNull(userId)) {
            throw new BusinessException(ActivityConstant.FREE_VOUCHER_GET_STATUS_HAS_NO_LOGIN);
        }
        return userId;
    }

    private boolean checkQualification(Long activityId, Long userId, String type) {
        CheckUserQualificationApiRqt checkUserQualificationApiRqt = new CheckUserQualificationApiRqt();
        checkUserQualificationApiRqt.setActivityId(activityId);
        checkUserQualificationApiRqt.setUserId(userId);
        checkUserQualificationApiRqt.setUserClass(type);
        Integer i = freeVoucherActivityServiceApi.checkUserQualification(checkUserQualificationApiRqt);
        return (i != 1);
    }

    /**
     * 转换会员权益类型
     *
     * @param comboType
     * @param userId
     * @return
     */
    private String convertVipType(Integer comboType, Long userId) {
        //套餐类型（0-灯火大会员 1-体验会员）
        String vipType;
        switch (comboType) {
            case 0:
                vipType = VipTypeEnum.COMMON.type;
                break;
            case 1:
                vipType = VipTypeEnum.EXPERIENCE.type;
                break;
            default:
                feishuUtils.sendErrorInfoFeishu("未定义的套餐类型:userId:" + userId + ",comboType:" + comboType);
                throw new RuntimeException("系统异常");
        }
        return vipType;
    }

    /**
     * 获取会员权益
     *
     * @param userId
     * @param userClass
     * @param vipType
     * @param vipRewardType
     * @return
     */
    private GetVipEquityInfoBo getVipEquityInfoBo(Long userId, String userClass, String vipType, String vipRewardType, Boolean isCouponReceived, boolean isVip) {
        GetVipEquityInfoBo resp = new GetVipEquityInfoBo();

        GetVipEquityRqt getVipEquityRqt = new GetVipEquityRqt();
        getVipEquityRqt.setUserId(userId);
        getVipEquityRqt.setUserClass(userClass);
        getVipEquityRqt.setVipType(vipType);
        getVipEquityRqt.setVipRewardType(vipRewardType);
        GetVipEquityResp vipEquity = vipEquityBusinessServiceApi.getVipEquity(getVipEquityRqt);
        if (Objects.isNull(vipEquity)) {
            if (EquityRewardTypeEnum.FIRST_MONTH.type.equals(vipRewardType) || EquityRewardTypeEnum.NEXT_MONTH.type.equals(vipRewardType)) {
                feishuUtils.sendErrorInfoFeishu("会员广告获取会员权益为空,rqt:" + JSONObject.toJSONString(getVipEquityRqt));
            }
            return null;
        }
        BeanCopyUtil.copyProperties(vipEquity, resp);
//        resp.setIsCouponReceived(isCouponReceived);
        resp.setCouponReceivedState(isCouponReceived ? 1 : 0);
        EquityRewardBo equityRewardInfo = vipEquity.getEquityRewardInfo();
        if (Objects.isNull(equityRewardInfo)) {
            feishuUtils.sendErrorInfoFeishu("会员广告获取首月会员权益奖励json为空,vipEquity:" + JSONObject.toJSONString(vipEquity));
            return null;
        }
        resp.setExtraId(equityRewardInfo.getExtraId());
        resp.setExtraType(equityRewardInfo.getExtraType());
        resp.setVipQualificationState(isVip ? 1 : 0);
        return resp;
    }

    /**
     * 是否是会员广告
     *
     * @param adResp
     * @return
     */
    private boolean isVipAd(NewAdPositionAdResp adResp) {
        return AdConstant.CUSTOMER_RECEIVE_VOUCHER_AD_SYMBOL_LIST.contains(adResp.getAdPositionSymbol()) && CollectionUtils.isNotEmpty(adResp.getAdBaseInfo().getAdAttributeTypeList()) && adResp.getAdBaseInfo().getAdAttributeTypeList().contains(AdAttributeTypeEnum.VIP_EQUITY_TYPE.type);
    }
}
