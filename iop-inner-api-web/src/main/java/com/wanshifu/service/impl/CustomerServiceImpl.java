package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.InterfaceFeign.CustomerInterface;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.constant.CacheKeyConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.framework.utils.StringUtils;
import com.wanshifu.iop.activity.domain.api.request.customer.ComputeMoneyByOrderIdFeeReq;
import com.wanshifu.iop.activity.domain.api.request.customer.OrderNoListByActivityIdUserIdReq;
import com.wanshifu.iop.activity.domain.api.request.customer.OrderRewardInviteeLandingPageReq;
import com.wanshifu.iop.activity.domain.api.request.customer.OrderRewardInviterLandingPageReq;
import com.wanshifu.iop.activity.domain.api.request.out.CreateTeamBuyCheckForGoodDetailRqt;
import com.wanshifu.iop.activity.domain.api.request.out.GetActivityPriorityTagRqt;
import com.wanshifu.iop.activity.domain.api.request.out.GetSeckillTagRqt;
import com.wanshifu.iop.activity.domain.api.request.out.GetTeamGroupTagRqt;
import com.wanshifu.iop.activity.domain.api.response.customer.OrderListFeeInfoResp;
import com.wanshifu.iop.activity.domain.api.response.customer.OrderRewardInviteeLandingPageResp;
import com.wanshifu.iop.activity.domain.api.response.customer.OrderRewardInviterLandingPageResp;
import com.wanshifu.iop.activity.domain.api.response.out.CreateTeamBuyCheckForGoodDetailResp;
import com.wanshifu.iop.activity.domain.api.response.out.GetActivityPriorityTagResp;
import com.wanshifu.iop.activity.domain.api.response.out.GetSeckillTagResp;
import com.wanshifu.iop.activity.domain.api.response.out.GetTeamBuyTagResp;
import com.wanshifu.iop.activity.domain.bo.activityLandingDetail.OrderActivityCustomerBo;
import com.wanshifu.iop.activity.domain.bo.activityLandingDetail.PrizeTaskCustomerBo;
import com.wanshifu.iop.activity.domain.enums.ActivityStateEnum;
import com.wanshifu.iop.activity.domain.enums.CustomerTagTypeEnum;
import com.wanshifu.iop.activity.domain.po.ActivityBase;
import com.wanshifu.iop.activity.service.api.CombineActivityBusinessServiceApi;
import com.wanshifu.iop.activity.service.api.CustomerActivityServiceApi;
import com.wanshifu.iop.activity.service.api.OutBusinessServiceApi;
import com.wanshifu.iop.equity.domain.api.request.vipEquity.ReceiveVipVoucherRqt;
import com.wanshifu.iop.equity.domain.api.response.ReceiveVipVoucherResp;
import com.wanshifu.iop.inner.api.domains.bo.CustomerInviterOrderFeeListBo;
import com.wanshifu.iop.inner.api.domains.bo.CustomerInviterOrderListBo;
import com.wanshifu.iop.inner.api.domains.enums.RewardStatusEnum;
import com.wanshifu.iop.inner.api.domains.enums.UserOrderStatusEnum;
import com.wanshifu.iop.inner.api.domains.enums.UserTypeApiEnum;
import com.wanshifu.iop.inner.api.domains.request.ReceiveVoucherV2Rqt;
import com.wanshifu.iop.inner.api.domains.request.customer.*;
import com.wanshifu.iop.inner.api.domains.response.OrderInfoForIocResp;
import com.wanshifu.iop.inner.api.domains.response.ReceiveVoucherResp;
import com.wanshifu.iop.inner.api.domains.response.customer.*;
import com.wanshifu.iop.marketing.config.domain.api.request.ChannelItemCreateReq;
import com.wanshifu.iop.marketing.config.domain.api.request.ShareCodeActivityInfoReq;
import com.wanshifu.iop.marketing.config.domain.api.response.ShareCodeActivityInfoResp;
import com.wanshifu.iop.marketing.config.domain.enums.BusinessTypeEnum;
import com.wanshifu.iop.marketing.config.domain.enums.ChannelTypeEnum;
import com.wanshifu.iop.marketing.config.service.api.MarketingServiceApi;
import com.wanshifu.payment.domains.api.OrderTransactionQueryResp;
import com.wanshifu.payment.domains.api.PaymentRefundResp;
import com.wanshifu.payment.domains.api.reqbean.OrderTransactionBatchQueryRqt;
import com.wanshifu.payment.domains.api.reqbean.PaymentRefundBatchQueryByOrderIdRqt;
import com.wanshifu.payment.service.api.UserPaymentServiceApi;
import com.wanshifu.service.CustomerService;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import com.wanshifu.user.api.UserInfoApi;
import com.wanshifu.user.api.toc.TocAppletApi;
import com.wanshifu.user.domain.req.toc.GenerateCodeRqt;
import com.wanshifu.user.domain.req.toc.GenerateLongLinkRqt;
import com.wanshifu.user.domain.resp.GetAppletsUserListResp;
import com.wanshifu.user.order.api.user.GetOrderListApi;
import com.wanshifu.user.order.domains.po.OrderBase;
import com.wanshifu.user.order.domains.user.api.request.toc.OrderIdsRqt;
import com.wanshifu.util.HttpImageUtils;
import com.wanshifu.util.PaymentHelper;
import com.wanshifu.util.RedisExUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: kexianyun
 * @time:2023/7/22 0022 11:33
 */
@Service
@Slf4j
public class CustomerServiceImpl extends AbstractService implements CustomerService {

    @Resource
    private RedisExUtil redisExUtil;

    @Resource
    private MarketingServiceApi marketingServiceApi;

    @Resource
    private CustomerActivityServiceApi customerActivityServiceApi;
    //    @Resource
//    private TocUserOrderApi tocUserOrderApi;
    @Resource
    private GetOrderListApi getOrderListApi;
    @Resource
    private UserInfoApi userInfoApi;
    @Resource
    private UserPaymentServiceApi userPaymentServiceApi;
    @Resource
    private HttpImageUtils httpImageUtils;
    @Resource
    private TocAppletApi tocAppletApi;
    @Resource
    private RedisHelper redisHelper;

    @Resource
    private OutBusinessServiceApi outBusinessServiceApi;
    @Resource
    private CombineActivityBusinessServiceApi combineActivityBusinessServiceApi;
    @Value("${wanshifu.customer.order.payFeeThreshold}")
    private BigDecimal payFeeThreshold;

    @Override
    public OrderRewardInviteeLandingPageApiResp inviteeLandingPage(OrderRewardInviteeLandingPageApiReq req, String token) {

        //获取code码对应的分享信息
        if (Objects.nonNull(req.getShareCode())) {
            ShareCodeActivityInfoReq shareCodeActivityInfoReq = new ShareCodeActivityInfoReq();
            shareCodeActivityInfoReq.setShareCode(req.getShareCode());
            ShareCodeActivityInfoResp channelInfo = marketingServiceApi.getChannelInfo(shareCodeActivityInfoReq);
            if (channelInfo == null) {
                throw new BusinessException("landing_page_error", "邀请链接不存在");
            }
        }

        //获取用户登陆信息
        Long userId = null;
        if (StringUtils.isNotEmpty(token)) {
            userId = redisExUtil.checkLoginStatus(token, UserTypeApiEnum.CUSTOMER.type);
        }

        OrderRewardInviteeLandingPageReq orderRewardInviteeLandingPageReq
                = new OrderRewardInviteeLandingPageReq();
        orderRewardInviteeLandingPageReq.setUserId(userId);
        orderRewardInviteeLandingPageReq.setActivityId(req.getActivityId());

        OrderRewardInviteeLandingPageResp orderRewardInviteeLandingPageResp = null;
        try {
            orderRewardInviteeLandingPageResp = customerActivityServiceApi.orderRewardInviteeLandingPage(orderRewardInviteeLandingPageReq);
        } catch (ApiAccessException e) {
            throw new BusinessException("landing_page_error", e.getRetMesg());
        }

        if (orderRewardInviteeLandingPageResp == null) {
            return null;
        }

        if (ActivityStateEnum.CANCELED.code.equals(orderRewardInviteeLandingPageResp.getActivityBase().getActivityState())
                || ActivityStateEnum.FINISHED.code.equals(orderRewardInviteeLandingPageResp.getActivityBase().getActivityState())
        ) {
            throw new BusinessException("landing_page_error", "活动已结束");
        }


        OrderRewardInviteeLandingPageApiResp result = new OrderRewardInviteeLandingPageApiResp();

        result.setActivityId(orderRewardInviteeLandingPageResp.getActivityId());
        result.setUserActivityTaskId(orderRewardInviteeLandingPageResp.getUserActivityTaskId());
        PrizeTaskCustomerBo prizeTaskCustomerBo = orderRewardInviteeLandingPageResp.getPrizeTaskCustomerBo();

        result.setTopImageUrl(prizeTaskCustomerBo.getPreviewImageUrl());
        result.setBgImageUrl(prizeTaskCustomerBo.getBgImageUrl());
        result.setBeforeVoucherImage(prizeTaskCustomerBo.getBeforeRewardUrl());
        result.setAfterVoucherImage(prizeTaskCustomerBo.getAfterRewardUrl());
        result.setCustomShow(prizeTaskCustomerBo.getIsShowModelDetail());
        result.setCustomTitle(prizeTaskCustomerBo.getActivityModelTitle());
        result.setCreateOrderImage(prizeTaskCustomerBo.getCreateOrderButtonImageUrl());
        result.setActivityName(prizeTaskCustomerBo.getActivityName());
        result.setActivityDescription(prizeTaskCustomerBo.getActivityDescription());
        result.setAfterVoucherAppletUrl(prizeTaskCustomerBo.getAfterVoucherAppletUrl());
        result.setOrderBtnAppletUrl(prizeTaskCustomerBo.getOrderBtnAppletUrl());
        result.setHasReward(orderRewardInviteeLandingPageResp.getHasReward() == null ? 0 : orderRewardInviteeLandingPageResp.getHasReward());
        //是否报名
        result.setHasApply(orderRewardInviteeLandingPageResp.getHasApply());
        //报名方式
        result.setApplyMethod(orderRewardInviteeLandingPageResp.getActivityBase().getApplyMethod());
        //领奖方式
        result.setRewardGiveMethod(orderRewardInviteeLandingPageResp.getActivityBase().getRewardGiveMethod());

        List<PrizeTaskCustomerBo.ActivityModelDetailItem> activityModelDetail = prizeTaskCustomerBo.getActivityModelDetails();

        List<OrderRewardInviteeLandingPageApiResp.CustomItem> customItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityModelDetail)) {
            for (PrizeTaskCustomerBo.ActivityModelDetailItem activityModelDetailItem : activityModelDetail) {
                OrderRewardInviteeLandingPageApiResp.CustomItem customItem = new OrderRewardInviteeLandingPageApiResp.CustomItem();
                Integer forwardType = activityModelDetailItem.getForwardType();
                String url;
                switch (forwardType) {
                    //不跳转
                    case 0:
                        url = null;
                        break;
                    //h5链接
                    case 2:
                        url = activityModelDetailItem.getH5Link();
                        break;
                    //小程序链接
                    case 3:
                        url = activityModelDetailItem.getAppletLink();
                        break;
                    default:
                        url = null;
                }
                customItem.setForwardType(forwardType);
                customItem.setImageUrl(activityModelDetailItem.getUrl());
                customItem.setUrl(url);
                customItemList.add(customItem);
            }
        }

        result.setCustomList(customItemList);
        return result;
    }

    @Override
    public ShareLinkApiResp shareLink(ShareLinkApiReq req) {

        ChannelItemCreateReq channelItemCreateReq = new ChannelItemCreateReq();
        channelItemCreateReq.setChannelType(ChannelTypeEnum.ACTIVITY.code);
        channelItemCreateReq.setBusinessId(req.getActivityId());
        channelItemCreateReq.setBusinessType(BusinessTypeEnum.ACTIVITY.code);
        channelItemCreateReq.setUserId(req.getUserId());
        channelItemCreateReq.setUserClass(UserTypeApiEnum.CUSTOMER.type);

        String channelCode = null;
        try {
            channelCode = marketingServiceApi.createChannelCode(channelItemCreateReq);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("生成验证码异常");
        }

        OrderRewardInviterLandingPageReq orderRewardInviterLandingPageReq = new OrderRewardInviterLandingPageReq();
        orderRewardInviterLandingPageReq.setActivityId(req.getActivityId());

        OrderRewardInviterLandingPageResp orderRewardInviterLandingPageResp = customerActivityServiceApi.orderRewardInviterLandingPage(orderRewardInviterLandingPageReq);
        if (orderRewardInviterLandingPageResp == null) {
            log.error("获取邀请人落地页异常，参数={}", JSON.toJSONString(orderRewardInviterLandingPageReq));
            return null;
        }
        ActivityBase activityBase = orderRewardInviterLandingPageResp.getActivityBase();

        Date nowDate = new Date();
        if (nowDate.before(activityBase.getActivityStartTime()) || nowDate.after(activityBase.getActivityEndTime())) {
            throw new BusinessException("活动已结束不能分享");
        }

        String stringKey = String.format(CacheKeyConstant.CUSTOMER_USER_SHARECODE_KEY, req.getActivityId(), req.getUserId());
        String s = redisHelper.get(stringKey);
        if (StringUtils.isNotEmpty(s)) {
            return JSONObject.parseObject(s, ShareLinkApiResp.class);
        }

        ShareLinkApiResp result = new ShareLinkApiResp();

        //跳转原始url
        String inviteOrderForwardUrl = orderRewardInviterLandingPageResp.getOrderActivityCustomerBo().getInviteOrderForwardUrl();
        result.setOriginalUrl(inviteOrderForwardUrl);

//        result.setUrl(this.generateAppletCode(req.getActivityId(), channelCode, ActivityConstant.CUSTOMER_LINK, inviteOrderForwardUrl));

        // 对接家庭生成二维码接口
        result.setAppletCode(this.generateAppletCode(orderRewardInviterLandingPageResp.getInviteeActivityId(), channelCode, ActivityConstant.CUSTOMER_APPLET, inviteOrderForwardUrl));

        result.setShareCode(channelCode);

        result.setInviteeActivityName(orderRewardInviterLandingPageResp.getInviteeActivityName());

        result.setInviteeActivityId(orderRewardInviterLandingPageResp.getInviteeActivityId());
        if (StringUtils.isNotEmpty(result.getUrl()) && StringUtils.isNotEmpty(result.getAppletCode())) {
            redisHelper.set(stringKey, JSON.toJSONString(result), CacheKeyConstant.CUSTOMER_USER_SHARECODE_TIME);
        }
        return result;
    }

    @Resource
    private CustomerInterface customerInterface;

    /**
     * 生成小程序码 或者链接
     */
    public String generateAppletCode(Long activityId, String code, String type, String inviteOrderForwardUrl) {
        //小程序固定路径
        try {
            String query;
            String path;
            if (activityId != null) {
                path = ActivityConstant.PATH;
                query = String.format(ActivityConstant.PATH_CODE, code, activityId);
            } else {
                path = inviteOrderForwardUrl;
                query = String.format(ActivityConstant.PATH_CODE_NOT_ACTIVITYID, code);
            }
            if (ActivityConstant.CUSTOMER_APPLET.equals(type)) {
                GenerateCodeRqt generateCodeRqt = new GenerateCodeRqt();
                generateCodeRqt.setPath(path);
                generateCodeRqt.setQuery(query);
                return customerInterface.generateCode(generateCodeRqt);
            } else {
                GenerateLongLinkRqt generateLongLinkRqt = new GenerateLongLinkRqt();
                generateLongLinkRqt.setPath(path);
                generateLongLinkRqt.setQuery(query);
                return customerInterface.generateLongLink(generateLongLinkRqt);
            }
        } catch (Exception e) {
            throw new BusinessException("生成异常请稍后重试");
        }
    }

    /**
     * 邀请人落地页信息
     */
    @Override
    public OrderRewardInviterLandingPageApiResp inviterLandingPage(OrderRewardInviterLandingPageApiReq req, String token) {

        //获取用户登陆信息
        Long userId = null;
        if (StringUtils.isNotEmpty(token)) {
            userId = redisExUtil.checkLoginStatus(token, UserTypeApiEnum.CUSTOMER.type);
        }

        OrderRewardInviterLandingPageReq orderRewardInviterLandingPageReq = new OrderRewardInviterLandingPageReq();
        orderRewardInviterLandingPageReq.setActivityId(req.getActivityId());
        orderRewardInviterLandingPageReq.setUserId(userId);

        //获取平台层邀请人落地页信息
        OrderRewardInviterLandingPageResp orderRewardInviterLandingPageResp = null;
        try {
            orderRewardInviterLandingPageResp =
                    customerActivityServiceApi.orderRewardInviterLandingPage(orderRewardInviterLandingPageReq);
        } catch (ApiAccessException e) {
            throw new BusinessException("landing_page_error", e.getRetMesg());
        } catch (Exception e) {
            throw new BusinessException("landing_page_error", "抱歉，无法访问该活动");
        }

        if (orderRewardInviterLandingPageResp == null) {
            return null;
        }

        OrderRewardInviterLandingPageApiResp result = new OrderRewardInviterLandingPageApiResp();

        OrderActivityCustomerBo orderActivityCustomerBo = orderRewardInviterLandingPageResp.getOrderActivityCustomerBo();
        result.setActivityId(req.getActivityId());
        result.setTopImageUrl(orderActivityCustomerBo.getAppTopImageUrl());
        result.setBgImageUrl(orderActivityCustomerBo.getBackgroundImageUrl());
        result.setActivityTitle(orderActivityCustomerBo.getActivityName());
        result.setActivityDesc(orderActivityCustomerBo.getActivityDescription());
        result.setShareImageUrl(orderActivityCustomerBo.getShareImageUrl());
        result.setActivituRule(orderActivityCustomerBo.getRuleDescription());
        result.setCustomShow(orderActivityCustomerBo.getIsShowModelDetail());
        result.setCustomTitle(orderActivityCustomerBo.getActivityModelTitle());
        result.setSharePosterUrl(orderActivityCustomerBo.getSharePosterUrl());
        result.setAppletCardTitle(orderActivityCustomerBo.getAppletCardTitle());
        result.setAppletCardCoverImageUrl(orderActivityCustomerBo.getAppletCardCoverImageUrl());
        result.setInviteOrderText(orderActivityCustomerBo.getInviteOrderText());
        result.setIsIssue(orderRewardInviterLandingPageResp.getIsIssue());
        List<OrderActivityCustomerBo.ActivityModelDetailItem> activityModelDetailList = orderActivityCustomerBo.getActivityModelDetailList();

        List<OrderRewardInviterLandingPageApiResp.CustomItem> customList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(activityModelDetailList)) {
            for (OrderActivityCustomerBo.ActivityModelDetailItem activityModelDetailItem : activityModelDetailList) {
                OrderRewardInviterLandingPageApiResp.CustomItem customItem = new OrderRewardInviterLandingPageApiResp.CustomItem();
                Integer forwardType = activityModelDetailItem.getForwardType();
                String url;
                switch (forwardType) {
                    //不跳转
                    case 0:
                        url = null;
                        break;
                    //h5链接
                    case 2:
                        url = activityModelDetailItem.getForwardHFive();
                        break;
                    //小程序链接
                    case 3:
                        url = activityModelDetailItem.getAppletLink();
                        break;
                    default:
                        url = null;
                }
                customItem.setImageUrl(activityModelDetailItem.getUrl());
                customItem.setUrl(url);
                customItem.setForwardType(forwardType);
                customList.add(customItem);
            }
        }

        result.setCustomList(customList);
        return result;
    }

    /**
     * 邀请人落地页订单列表
     */
    @Override
    public SimplePageInfo<OrderRewardInviterOrderListApiResp> orderList(OrderRewardInviterOrderListApiReq req) {

        Long userId = req.getUserId();

        //分享过的活动id
        List<Long> activityIdList = new ArrayList<>();
        activityIdList.add(req.getActivityId());

        OrderNoListByActivityIdUserIdReq orderNoListByActivityIdUserIdReq = new OrderNoListByActivityIdUserIdReq();
        orderNoListByActivityIdUserIdReq.setUserId(userId);
        orderNoListByActivityIdUserIdReq.setActivityIdList(activityIdList);
        orderNoListByActivityIdUserIdReq.setPageSize(req.getPageSize());
        orderNoListByActivityIdUserIdReq.setPageNum(req.getPageNum());
        //获取分享出去的链接所产生的所有订单
        SimplePageInfo<OrderListFeeInfoResp> orderNoList = customerActivityServiceApi.getOrderNoListBy(orderNoListByActivityIdUserIdReq);
        if (CollectionUtils.isEmpty(orderNoList.getList())) {
            return new SimplePageInfo<>();
        }

        // 过滤掉已经发过奖的数据 , 获取订单信息
        List<Long> orderIdListNotComplete = orderNoList.getList().stream().map(OrderListFeeInfoResp::getOrderId).collect(Collectors.toList());

        //  请求家庭端获取订单信息
        List<CustomerInviterOrderListBo> orderListInfo = this.getOrderListInfo(orderIdListNotComplete);

        //计算每个订单能获取的奖励金额  需要过滤已经完成的订单
        List<OrderListFeeInfoResp> orderListFeeInfoRespList = orderNoList.getList();
        List<Long> notCompleteOrderNoList = orderListFeeInfoRespList.stream().filter(f -> f.getTradeAmount() == null).map(OrderListFeeInfoResp::getOrderId).collect(Collectors.toList());

        List<CustomerInviterOrderListBo> customerInviterOrderListBoList = orderListInfo.stream().filter(f -> notCompleteOrderNoList.contains(f.getOrderId())).collect(Collectors.toList());
        List<CustomerInviterOrderFeeListBo> customerInviterOrderFeeListBoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customerInviterOrderListBoList)) {
            //调平台层 计算奖励
            customerInviterOrderFeeListBoList = this.returnOrderRewardFeeList(customerInviterOrderListBoList, activityIdList, userId);
        }

        SimplePageInfo<OrderRewardInviterOrderListApiResp> result = new SimplePageInfo<>();

        result.setPageNum(orderNoList.getPageNum());
        result.setPageSize(orderNoList.getPageSize());
        result.setTotal(orderNoList.getTotal());
        result.setPages(orderNoList.getPages());

        List<OrderRewardInviterOrderListApiResp> orderInfoListBean = this.createOrderInfoListBean(orderListFeeInfoRespList, orderListInfo, notCompleteOrderNoList, customerInviterOrderFeeListBoList);
        result.setList(orderInfoListBean);
        return result;
    }

    @Override
    public OrderRewardNumResp rewardNum(RewardNumReq req) {

        Long userId = req.getUserId();

        //分享过的活动id
        List<Long> activityIdList = new ArrayList<>();
        activityIdList.add(req.getActivityId());

        OrderNoListByActivityIdUserIdReq orderNoListByActivityIdUserIdReq = new OrderNoListByActivityIdUserIdReq();
        orderNoListByActivityIdUserIdReq.setUserId(userId);
        orderNoListByActivityIdUserIdReq.setActivityIdList(activityIdList);
        orderNoListByActivityIdUserIdReq.setPageNum(1);
        orderNoListByActivityIdUserIdReq.setPageSize(10000);

        OrderRewardNumResp result = new OrderRewardNumResp();
        result.setHasRewardNum(new BigDecimal(0));
        result.setWaitRewardNum(new BigDecimal(0));

        //获取分享出去的链接所产生的所有订单
        SimplePageInfo<OrderListFeeInfoResp> orderNoList = customerActivityServiceApi.getOrderNoListBy(orderNoListByActivityIdUserIdReq);
        if (CollectionUtils.isEmpty(orderNoList.getList())) {
            return result;
        }

        List<OrderListFeeInfoResp> orderListFeeInfoRespList = orderNoList.getList();

        //已经获取奖励的总数
        BigDecimal reduce = orderListFeeInfoRespList.stream().filter(f -> f.getCommissionAmount() != null && f.getRewardGiveState() == 1).map(OrderListFeeInfoResp::getCommissionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        result.setHasRewardNum(reduce != null ? reduce : new BigDecimal(0));
        List<Long> notCompleteOrderIdList = orderListFeeInfoRespList.stream().filter(f -> f.getTradeAmount() == null).map(OrderListFeeInfoResp::getOrderId).collect(Collectors.toList());
        List<CustomerInviterOrderListBo> customerInviterOrderFeeListBoList = new ArrayList<>();
        List<CustomerInviterOrderFeeListBo> customerInviterOrderFeeListBoList1 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(notCompleteOrderIdList)) {
            //批量请求用户端实付金额
            customerInviterOrderFeeListBoList = this.getOrderListInfo(notCompleteOrderIdList);

            //调活动平台计算奖励值
            customerInviterOrderFeeListBoList1 = this.returnOrderRewardFeeList(customerInviterOrderFeeListBoList, activityIdList, userId);

        }
        if (CollectionUtils.isNotEmpty(customerInviterOrderFeeListBoList1)) {
            BigDecimal reduce1 = customerInviterOrderFeeListBoList1.stream().map(CustomerInviterOrderFeeListBo::getOrderRewardFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            result.setWaitRewardNum(reduce1);
        }

        return result;
    }

    @Override
    public GetSeckillTagResp getSeckillTag(GetSeckillTagInnerRqt rqt) {
        GetSeckillTagRqt getSeckillTagRqt = new GetSeckillTagRqt();
        BeanUtils.copyProperties(rqt, getSeckillTagRqt);
        getSeckillTagRqt.setActivityId(rqt.getSeckillActivityId());
        getSeckillTagRqt.setActivityTaskId(rqt.getSeckillActivityTaskId());
        return outBusinessServiceApi.getSeckillTag(getSeckillTagRqt);
    }

    @Override
    public ReceiveVoucherResp receiveVipVoucher(ReceiveVoucherV2Rqt rqt) {
        ReceiveVoucherResp resp = new ReceiveVoucherResp();

        ReceiveVipVoucherRqt serviceRqt = new ReceiveVipVoucherRqt();
        BeanUtils.copyProperties(rqt, serviceRqt);
        try {
            ReceiveVipVoucherResp vipResp = vipEquityBusinessServiceApi.receiveVipVoucher(serviceRqt);
//            resp.setState(vipResp.getResult());
//            resp.setFailReason(vipResp.getShowMessage());
//            return resp;
            if (vipResp.getResult() == 1) {
                resp.setState(vipResp.getResult());
                return resp;
            } else {
                resp.setState(ActivityConstant.VOUCHER_GET_STATUS_NEED_REPULL);
                resp.setFailReason(vipResp.getShowMessage());
                return resp;
            }
        } catch (Exception e) {
            log.error("会员领券异常", e);
            feishuUtils.sendErrorInfoFeishu(e.getMessage());
            resp.setState(ActivityConstant.FREE_VOUCHER_GET_STATUS_FAIL);
            resp.setFailReason(ActivityConstant.FREE_VOUCHER_GET_STATUS_EXCEPTION);
            return resp;
        }
    }

    @Override
    public GetCustomerActivityTagApiResp getCustomerActivityTag(GetCustomerActivityTagInnerRqt rqt) {
        CustomerTagTypeEnum tagTypeEnum = CustomerTagTypeEnum.getEnumByType(rqt.getTagType());
        if (Objects.isNull(tagTypeEnum)) {
            return getCustomerPriorityTagResp(rqt);
        }
        switch (tagTypeEnum) {
            case TEAM_BUY:
                return this.getTeamBuyTagResp(rqt);
            case SECKILL:
                return this.getSeckillTagResp(rqt);
            default:
                return getCustomerPriorityTagResp(rqt);
        }
    }

    @Override
    public CreateTeamBuyCheckOutApiResp createTeamBuyCheckForOut(CreateTeamBuyCheckOutApiRqt rqt) {
        CreateTeamBuyCheckForGoodDetailRqt createTeamBuyCheckRqt = new CreateTeamBuyCheckForGoodDetailRqt();
        BeanUtils.copyProperties(rqt, createTeamBuyCheckRqt);
        CreateTeamBuyCheckForGoodDetailResp checkResp = outBusinessServiceApi.createTeamBuyCheckForGoodDetail(createTeamBuyCheckRqt);
        CreateTeamBuyCheckOutApiResp createTeamBuyCheckApiResp = new CreateTeamBuyCheckOutApiResp();
        BeanUtils.copyProperties(checkResp, createTeamBuyCheckApiResp);
        createTeamBuyCheckApiResp.setServeName(checkResp.getServerName());
        return createTeamBuyCheckApiResp;
    }

    private GetCustomerActivityTagApiResp getCustomerPriorityTagResp(GetCustomerActivityTagInnerRqt rqt) {
        GetCustomerActivityTagApiResp resp = new GetCustomerActivityTagApiResp();
        GetActivityPriorityTagRqt getActivityPriorityTagRqt = new GetActivityPriorityTagRqt();
        BeanUtils.copyProperties(rqt, getActivityPriorityTagRqt);
        try {
            GetActivityPriorityTagResp getActivityPriorityTagResp = outBusinessServiceApi.getActivityPriorityTag(getActivityPriorityTagRqt);
            if (Objects.isNull(getActivityPriorityTagResp) || StringUtils.isEmpty(getActivityPriorityTagResp.getResultTagType())) {
                return resp;
            }
            resp.setResultTagType(getActivityPriorityTagResp.getResultTagType());
            if (CustomerTagTypeEnum.SECKILL.type.equals(getActivityPriorityTagResp.getResultTagType())) {
                resp.setSeckillTagInfo(this.copySeckillTagInfo(getActivityPriorityTagResp.getGetSeckillTagResp()));
            }
            if (CustomerTagTypeEnum.TEAM_BUY.type.equals(getActivityPriorityTagResp.getResultTagType())) {
                resp.setTeamBuyTagInfo(this.copyTeamBuyTagInfo(getActivityPriorityTagResp.getGetTeamBuyTagResp()));
            }
        } catch (ApiAccessException e) {
            e.printStackTrace();
            log.error("获取家庭活动标签信息异常:{}", e);
            throw new BusinessException("活动太火爆，请稍后重试！");
        }
        return resp;
    }

    private GetCustomerActivityTagApiResp getSeckillTagResp(GetCustomerActivityTagInnerRqt rqt) {
        GetCustomerActivityTagApiResp resp = new GetCustomerActivityTagApiResp();
        resp.setResultTagType(rqt.getTagType());
        GetSeckillTagRqt getSeckillTagRqt = new GetSeckillTagRqt();
        BeanUtils.copyProperties(rqt, getSeckillTagRqt);
        String errorMsg = "";
        try {
            GetSeckillTagResp getSeckillTagResp = outBusinessServiceApi.getSeckillTag(getSeckillTagRqt);
            if (Objects.isNull(getSeckillTagResp)) {
                errorMsg = "您所选择的上门地址暂不支持秒杀活动,请返回重新下单";
                resp.setFailMsg(errorMsg);
                return resp;
            }
            if (getSeckillTagResp.getHasSeckillPrice() != 1) {
                resp.setFailMsg(getSeckillTagResp.getFailMsg());
                return resp;
            }
            resp.setSeckillTagInfo(this.copySeckillTagInfo(getSeckillTagResp));
        } catch (ApiAccessException e) {
            e.printStackTrace();
            log.error("获取秒杀标签异常:{}", e);
        }
        resp.setFailMsg(errorMsg);
        return resp;
    }

    private GetSeckillTagApiResp copySeckillTagInfo(GetSeckillTagResp getSeckillTagResp) {
        GetSeckillTagApiResp seckillTagResp = new GetSeckillTagApiResp();
        BeanUtils.copyProperties(getSeckillTagResp, seckillTagResp);
        List<GetSeckillTagApiResp.SeckillPriceRangeBo> seckillPriceRangeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(getSeckillTagResp.getSeckillPriceRangeList())) {
            getSeckillTagResp.getSeckillPriceRangeList().forEach(vo -> {
                GetSeckillTagApiResp.SeckillPriceRangeBo rangeBo = new GetSeckillTagApiResp.SeckillPriceRangeBo();
                BeanUtils.copyProperties(vo, rangeBo);
                seckillPriceRangeList.add(rangeBo);
            });
        }
        seckillTagResp.setSeckillPriceRangeList(seckillPriceRangeList);
        return seckillTagResp;
    }

    private GetCustomerActivityTagApiResp getTeamBuyTagResp(GetCustomerActivityTagInnerRqt rqt) {
        GetCustomerActivityTagApiResp resp = new GetCustomerActivityTagApiResp();
        GetTeamGroupTagRqt getTeamGroupTagRqt = new GetTeamGroupTagRqt();
        BeanUtils.copyProperties(rqt, getTeamGroupTagRqt);
        String errorMsg = "该服务无拼团价";
        try {
            GetTeamBuyTagResp getTeamBuyTagResp = outBusinessServiceApi.getTeamBuyTag(getTeamGroupTagRqt);
            if (Objects.isNull(getTeamBuyTagResp)) {
                resp.setFailMsg(errorMsg);
                return resp;
            }
            if (getTeamBuyTagResp.getHasTeamPrice() != 1) {
                log.info("获取拼团标签为空:{}", getTeamBuyTagResp.getFailMsg());
                resp.setFailMsg(errorMsg);
                return resp;
            }
            errorMsg = "";
            resp.setResultTagType(rqt.getTagType());
            resp.setTeamBuyTagInfo(this.copyTeamBuyTagInfo(getTeamBuyTagResp));
        } catch (ApiAccessException e) {
            e.printStackTrace();
            log.error("获取拼团标签异常:{}", e);
        }
        resp.setFailMsg(errorMsg);
        return resp;
    }

    private GetTeamBuyTagApiResp copyTeamBuyTagInfo(GetTeamBuyTagResp getTeamBuyTagResp) {
        GetTeamBuyTagApiResp teamBuyTagInfo = new GetTeamBuyTagApiResp();
        BeanUtils.copyProperties(getTeamBuyTagResp, teamBuyTagInfo);
        List<GetTeamBuyTagApiResp.TeamPriceRangeBo> priceRangeInfoList = new ArrayList<>();
        getTeamBuyTagResp.getPriceRangeInfoList().forEach(rangeVo -> {
            GetTeamBuyTagApiResp.TeamPriceRangeBo priceRangeInfo = new GetTeamBuyTagApiResp.TeamPriceRangeBo();
            BeanUtils.copyProperties(rangeVo, priceRangeInfo);
            List<GetTeamBuyTagApiResp.TeamPriceBo> teamBuyPriceBoList = new ArrayList<>();
            rangeVo.getTeamBuyPriceList().forEach(vo -> {
                GetTeamBuyTagApiResp.TeamPriceBo teamPriceBo = new GetTeamBuyTagApiResp.TeamPriceBo();
                teamPriceBo.setTeamBuyPrice(vo.getTeamBuyPrice());
                teamPriceBo.setTeamTargetNumber(vo.getTeamTargetNumber());
                teamBuyPriceBoList.add(teamPriceBo);
            });
            priceRangeInfo.setTeamBuyPriceList(teamBuyPriceBoList);
            priceRangeInfoList.add(priceRangeInfo);
        });
        return teamBuyTagInfo;
    }

    /**
     * 组装数据
     */
    private List<OrderRewardInviterOrderListApiResp> createOrderInfoListBean(List<OrderListFeeInfoResp> orderListFeeInfoRespList, List<CustomerInviterOrderListBo> orderListInfo, List<Long> notCompleteOrderNoList, List<CustomerInviterOrderFeeListBo> customerInviterOrderFeeListBoList) {
        List<OrderRewardInviterOrderListApiResp> orderRewardInviterOrderListApiRespList = new ArrayList<>();
        for (OrderListFeeInfoResp orderListFeeInfoResp : orderListFeeInfoRespList) {
            OrderRewardInviterOrderListApiResp orderRewardInviterOrderListApiResp = new OrderRewardInviterOrderListApiResp();
            orderRewardInviterOrderListApiResp.setOrderNo(orderListFeeInfoResp.getOrderNo());
            CustomerInviterOrderListBo cu = orderListInfo.stream().filter(f -> f.getOrderId().equals(orderListFeeInfoResp.getOrderId())).findFirst().orElseThrow(() -> new BusException("查询订单信息失败"));

            orderRewardInviterOrderListApiResp.setHeadImage(cu.getHeadImage());
            orderRewardInviterOrderListApiResp.setPhone("*** **** " + cu.getPhone().substring(cu.getPhone().length() - 4));
            orderRewardInviterOrderListApiResp.setCreateTime(cu.getCreateTime());
            orderRewardInviterOrderListApiResp.setOrderStatus(UserOrderStatusEnum.STOP.type.equals(cu.getOrderStatus()) ? UserOrderStatusEnum.STOP.type : null);
            orderRewardInviterOrderListApiResp.setRewardStatus(null);
            orderRewardInviterOrderListApiResp.setRewardNum("-");
            orderRewardInviterOrderListApiResp.setOrderRealFee(cu.getOrderRealityFee());
            if (cu.getOrderRealityFee().compareTo(payFeeThreshold) < 0 && !UserOrderStatusEnum.STOP.type.equals(cu.getOrderStatus())) {
                //如果小于门槛，并且订单状态不是停止
                orderRewardInviterOrderListApiResp.setRewardStatus(RewardStatusEnum.UNLOCKED.type);
            }

            if (UserOrderStatusEnum.STOP.type.equals(cu.getOrderStatus())) {
                //订单已关闭
                orderRewardInviterOrderListApiResp.setOrderStatus(UserOrderStatusEnum.STOP.type);
                orderRewardInviterOrderListApiResp.setExceptionMsg(ActivityConstant.ORDER_CLOSE);
            }

            if (cu.getOrderRealityFee().compareTo(payFeeThreshold) < 0
                    && UserOrderStatusEnum.FINISH.type.equals(cu.getOrderStatus())) {
                //如果小于门槛，并且订单状态是已经完成
                orderRewardInviterOrderListApiResp.setOrderStatus(UserOrderStatusEnum.STOP.type);
                orderRewardInviterOrderListApiResp.setExceptionMsg(String.format(ActivityConstant.ORDER_COMPLETE_LESS, payFeeThreshold));
            }

            if (cu.getOrderStatus() != null && cu.getOrderRealityFee().compareTo(payFeeThreshold) >= 0) {
                if (notCompleteOrderNoList.contains(orderListFeeInfoResp.getOrderId())) {
                    //订单没有完成
                    orderRewardInviterOrderListApiResp.setRewardStatus(RewardStatusEnum.UNLOCKED.type);
                    //奖励金额
                    Optional<CustomerInviterOrderFeeListBo> first = customerInviterOrderFeeListBoList.stream().filter(f -> f.getOrderNo().equals(orderListFeeInfoResp.getOrderNo())).findFirst();
                    if (first.isPresent()) {
                        orderRewardInviterOrderListApiResp.setRewardNum(first.get().getOrderRewardFee() + "");
                    }
                } else {
                    orderRewardInviterOrderListApiResp.setRewardStatus(RewardStatusEnum.UNLOCKED.type);
                    //订单已经完成
                    if (orderListFeeInfoResp.getRewardGiveState() == 1) {
                        orderRewardInviterOrderListApiResp.setRewardStatus(RewardStatusEnum.AWARDED.type);
                    }

                    //奖励金额
                    Optional<OrderListFeeInfoResp> first = orderListFeeInfoRespList.stream().filter(f -> f.getOrderNo().equals(orderListFeeInfoResp.getOrderNo())).findFirst();
                    if (first.isPresent()) {
                        orderRewardInviterOrderListApiResp.setRewardNum(first.get().getCommissionAmount() + "");
                    }
                }
            }

            orderRewardInviterOrderListApiRespList.add(orderRewardInviterOrderListApiResp);
        }
        return orderRewardInviterOrderListApiRespList;
    }

    /**
     * 计算奖励金额
     */
    public List<CustomerInviterOrderFeeListBo> returnOrderRewardFeeList(List<CustomerInviterOrderListBo> req, List<Long> activityIdList, Long userId) {
        List<CustomerInviterOrderFeeListBo> result = new ArrayList<>();
        ComputeMoneyByOrderIdFeeReq computeMoneyByOrderIdFeeReq = new ComputeMoneyByOrderIdFeeReq();
        // 调用平台层 计算奖励数据 并放入result中
        computeMoneyByOrderIdFeeReq.setUserId(userId);

        List<ComputeMoneyByOrderIdFeeReq.Item> itemList = new ArrayList<>();
        for (CustomerInviterOrderListBo customerInviterOrderListBo : req) {
            ComputeMoneyByOrderIdFeeReq.Item item = new ComputeMoneyByOrderIdFeeReq.Item();
            item.setFee(customerInviterOrderListBo.getOrderRealityFee());
            if (customerInviterOrderListBo.getOrderRealityFee().compareTo(new BigDecimal(0)) == 0) {
                continue;
            }
            item.setOrderId(customerInviterOrderListBo.getOrderId());
            itemList.add(item);
        }
        if (CollectionUtils.isEmpty(itemList)) {
            return result;
        }
        computeMoneyByOrderIdFeeReq.setItemList(itemList);
        computeMoneyByOrderIdFeeReq.setActivityId(activityIdList);

        List<OrderListFeeInfoResp> orderIdFeeList = customerActivityServiceApi.getOrderIdFeeList(computeMoneyByOrderIdFeeReq);
        if (CollectionUtils.isEmpty(orderIdFeeList)) {
            return result;
        }
        for (OrderListFeeInfoResp orderListFeeInfoResp : orderIdFeeList) {
            if (orderListFeeInfoResp.getTradeAmount().compareTo(payFeeThreshold) < 0) {
                continue;
            }
            CustomerInviterOrderFeeListBo customerInviterOrderFeeListBo = new CustomerInviterOrderFeeListBo();
            customerInviterOrderFeeListBo.setOrderNo(orderListFeeInfoResp.getOrderNo());
            customerInviterOrderFeeListBo.setOrderId(orderListFeeInfoResp.getOrderId());
            customerInviterOrderFeeListBo.setOrderRealityFee(orderListFeeInfoResp.getTradeAmount());
            customerInviterOrderFeeListBo.setOrderRewardFee(orderListFeeInfoResp.getCommissionAmount());
            result.add(customerInviterOrderFeeListBo);
        }

        return result;
    }

    /**
     * 请求用户端接口
     * 获取订单信息包含实付金额
     */
    public List<CustomerInviterOrderListBo> getOrderListInfo(List<Long> orderIdList) {

        List<CustomerInviterOrderListBo> customerInviterOrderListBoList = new ArrayList<>();

        List<OrderInfoForIocResp> byOrderIds = null;
        try {
            byOrderIds = this.getByOrderIds(orderIdList);
        } catch (ApiAccessException e) {
            return customerInviterOrderListBoList;
        }
        if (CollectionUtils.isEmpty(byOrderIds)) {
            return customerInviterOrderListBoList;
        }

        for (OrderInfoForIocResp byOrderId : byOrderIds) {
            CustomerInviterOrderListBo customerInviterOrderListBo = new CustomerInviterOrderListBo();
            customerInviterOrderListBo.setHeadImage(
                    StringUtils.isNotEmpty(byOrderId.getAvatar()) ? byOrderId.getAvatar() : ActivityConstant.DEFAULT_HEAD_IMG);
            customerInviterOrderListBo.setPhone(byOrderId.getPhone());
            customerInviterOrderListBo.setCreateTime(byOrderId.getCreateTime());
            customerInviterOrderListBo.setOrderStatus(byOrderId.getOrderStatus());
            customerInviterOrderListBo.setOrderRealityFee(byOrderId.getOrderAmount());
            customerInviterOrderListBo.setOrderId(byOrderId.getOrderId());
            customerInviterOrderListBoList.add(customerInviterOrderListBo);
        }
        return customerInviterOrderListBoList;
    }

    /**
     * 根据订单号批量查询订单信息
     *
     * @param orderIds
     * @return
     */
    public List<OrderInfoForIocResp> getByOrderIds(List<Long> orderIds) {
        if (orderIds.size() > 100) {
            throw new BusException("批量查询不能超过100条");
        }
        List<OrderInfoForIocResp> resps = new ArrayList<>();

        OrderIdsRqt orderIdsRqt = new OrderIdsRqt();
        orderIdsRqt.setOrderIds(orderIds);
        List<OrderBase> orderBases = getOrderListApi.getOrderBaseByOrderIds(orderIdsRqt);
        if (CollectionUtils.isEmpty(orderBases)) {
            return resps;
        }
        String userIds = orderBases.stream().map(e -> String.valueOf(e.getUserId())).collect(Collectors.joining(","));
        List<GetAppletsUserListResp> appletsUserList = userInfoApi.getAppletsUserByUserId(userIds);
        Map<Long, GetAppletsUserListResp> appletsUserMap = appletsUserList.stream()
                .collect(Collectors.toMap(GetAppletsUserListResp::getUserId, Function.identity(), (e1, e2) -> e1));

        // 头像图片
        List<String> aidList = appletsUserList.stream().map(GetAppletsUserListResp::getAid)
                .filter(Objects::nonNull).distinct().map(String::valueOf).collect(Collectors.toList());
//        Map<String, String> urlsByAid = commonImageService.getUrlsByAid(aids);
        Map<Long, String> urlsByAid = httpImageUtils.sendPostRequest(aidList);
        // 支付信息
        OrderTransactionBatchQueryRqt orderTransactionBatchQueryRqt = new OrderTransactionBatchQueryRqt();
        orderTransactionBatchQueryRqt.setOrderList(orderIds);
        List<OrderTransactionQueryResp> orderTransactionQueryResps = userPaymentServiceApi.orderTransactionBatchQuery(orderTransactionBatchQueryRqt);
        // 退款信息
        PaymentRefundBatchQueryByOrderIdRqt paymentRefundBatchQuery = new PaymentRefundBatchQueryByOrderIdRqt();
        paymentRefundBatchQuery.setOrderList(orderIds);
        List<PaymentRefundResp> paymentRefundResps = userPaymentServiceApi.orderRefundBatchQuery(paymentRefundBatchQuery);
        Map<Long, OrderTransactionQueryResp> mapOrderTransactions = orderTransactionQueryResps.stream().collect(Collectors.toMap(OrderTransactionQueryResp::getOrderId, t -> t));
        Map<Long, PaymentRefundResp> mapPaymentRefund = paymentRefundResps.stream().collect(Collectors.toMap(PaymentRefundResp::getOrderId, t -> t));

        orderBases.forEach(orderBase -> {
            OrderInfoForIocResp resp = new OrderInfoForIocResp();
            resp.setOrderId(orderBase.getOrderId());
            resp.setOrderStatus(orderBase.getOrderStatus());
            resp.setCreateTime(orderBase.getCreateTime());
            resp.setPhone(appletsUserMap.getOrDefault(orderBase.getUserId(), new GetAppletsUserListResp()).getPhone());
            Optional<GetAppletsUserListResp> respOptional = appletsUserList.stream().filter(f -> f.getUserId().equals(orderBase.getUserId())).findFirst();
            resp.setAvatar(urlsByAid.get(respOptional.isPresent() ? respOptional.get().getAid() : ""));
            resp.setOrderAmount(PaymentHelper.getPayAmount(orderBase.getOrderId(), mapOrderTransactions, mapPaymentRefund));
            resps.add(resp);
        });

        return resps;
    }
}
