package com.wanshifu.service.impl;

import com.wanshifu.constant.CacheKeyConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.redis.autoconfigure.component.RedisHelper;
import com.wanshifu.iop.ad.api.AdConfigInfoApi;
import com.wanshifu.iop.ad.api.AdPullApi;
import com.wanshifu.iop.ad.domain.req.common.GetAdSwitchServiceRqt;
import com.wanshifu.iop.ad.domain.req.common.SaveUnInterestAdReq;
import com.wanshifu.iop.ad.domain.req.common.UpdateAdSwitchServiceRqt;
import com.wanshifu.iop.ad.domain.resp.common.GetAdSwitchServiceResp;
import com.wanshifu.iop.inner.api.domains.request.GetAdSwitchRqt;
import com.wanshifu.iop.inner.api.domains.request.UpdateAdSwitchRqt;
import com.wanshifu.iop.inner.api.domains.request.ad.SaveUnInterestAdRqt;
import com.wanshifu.iop.inner.api.domains.response.GetAdSwitchResp;
import com.wanshifu.service.AdCommonService;
import com.wanshifu.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date Created in 2025/4/9 10:55
 */
@Service
@Slf4j
public class AdCommonServiceImpl implements AdCommonService {
    @Resource
    private AdPullApi adPullApi;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private AdConfigInfoApi adConfigInfoApi;

    @Override
    public Integer saveUnInterestAd(SaveUnInterestAdRqt rqt) {
        //删除redis缓存
        String batchUserFormatKey = String.format(CacheKeyConstant.BATCH_PULL_AD_POSITION_KEY, rqt.getUserClass(), rqt.getLaunchPort(), rqt.getAdPositionSymbol(), rqt.getUserId());
        redisHelper.del(batchUserFormatKey);
        //插入记录
        SaveUnInterestAdReq saveUnInterestAdReq = new SaveUnInterestAdReq();
        saveUnInterestAdReq.setAdId(rqt.getAdId());
        saveUnInterestAdReq.setUserId(rqt.getUserId());
        Integer result = 0;
        try {
            result = adPullApi.saveUnInterestAd(saveUnInterestAdReq);
        } catch (Exception e) {
            log.error("保存不感兴趣异常:{}", e);
            throw new BusException("保存不感兴趣异常，请刷新后重试！");
        }
        return result;
    }

    @Override
    public Integer updatePersonalizationAdSwitch(UpdateAdSwitchRqt rqt) {
        UpdateAdSwitchServiceRqt serviceRqt = new UpdateAdSwitchServiceRqt();
        BeanCopyUtil.copyProperties(rqt, serviceRqt);
        return adConfigInfoApi.updatePersonalizationAdSwitch(serviceRqt);
    }

    @Override
    public GetAdSwitchResp getPersonalizationAdSwitch(GetAdSwitchRqt rqt) {
        GetAdSwitchResp resp = new GetAdSwitchResp();

        GetAdSwitchServiceRqt serviceRqt = new GetAdSwitchServiceRqt();
        BeanCopyUtil.copyProperties(rqt, serviceRqt);
        GetAdSwitchServiceResp personalizationAdSwitch = adConfigInfoApi.getPersonalizationAdSwitch(serviceRqt);
        resp.setAdStatus(personalizationAdSwitch.getAdStatus());
        return resp;
    }
}
