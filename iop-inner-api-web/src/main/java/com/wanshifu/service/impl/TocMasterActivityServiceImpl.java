package com.wanshifu.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanshifu.annotation.ExceptionHandle;
import com.wanshifu.constant.ActivityConstant;
import com.wanshifu.framework.core.BusException;
import com.wanshifu.framework.core.BusinessException;
import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.framework.utils.CollectionUtils;
import com.wanshifu.iop.activity.domain.api.request.GetCompleteActivityTaskUnitsRqt;
import com.wanshifu.iop.activity.domain.api.request.master.*;
import com.wanshifu.iop.activity.domain.api.request.master.OrderDetailRewardRemindBatchRqt.OrderInfoItem;
import com.wanshifu.iop.activity.domain.api.request.masterSignCheck.OrderCheckInfoDetailRqt;
import com.wanshifu.iop.activity.domain.api.request.masterSignCheck.OrderCheckListSomeTagRqt;
import com.wanshifu.iop.activity.domain.api.request.masterSignCheck.ReservationListReq;
import com.wanshifu.iop.activity.domain.api.response.GetCompleteActivityTaskUnitsResp;
import com.wanshifu.iop.activity.domain.api.response.IsReachRewardUpperResp;
import com.wanshifu.iop.activity.domain.api.response.master.*;
import com.wanshifu.iop.activity.domain.api.response.master.MasterActivityListResp.ActivityList.TaskLabelTextBo;
import com.wanshifu.iop.activity.domain.api.response.masterSignCheck.*;
import com.wanshifu.iop.activity.domain.bo.MasterOrderInfoBo;
import com.wanshifu.iop.activity.domain.bo.masterOrderTask.TradeAmountRateBo;
import com.wanshifu.iop.activity.domain.enums.*;
import com.wanshifu.iop.activity.domain.po.ActivityBase;
import com.wanshifu.iop.activity.domain.po.ActivityOrderRelation;
import com.wanshifu.iop.activity.domain.po.ActivityTask;
import com.wanshifu.iop.activity.domain.po.UserActivityTaskExecuteBatch;
import com.wanshifu.iop.equity.domain.api.response.ListRemainNumResp;
import com.wanshifu.iop.inner.api.domains.enums.AppointMethodEnum;
import com.wanshifu.iop.inner.api.domains.enums.OrderPropertyEnum;
import com.wanshifu.iop.inner.api.domains.enums.*;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.request.master.*;
import com.wanshifu.iop.inner.api.domains.request.master.OrderInfoExtTemp;
import com.wanshifu.iop.inner.api.domains.request.master.OrderInfoTemp;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.*;
import com.wanshifu.iop.inner.api.domains.response.AwardingNumAndUnLockNumResp;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.master.*;
import com.wanshifu.manager.ActivityCenterManager;
import com.wanshifu.master.bonus.api.BonusOperateApi;
import com.wanshifu.master.innerapi.domains.response.ocs.BatchGetPushDistanceResp;
import com.wanshifu.master.innerapi.service.api.ioc.RewardApi;
import com.wanshifu.service.TocMasterActivityService;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import com.wanshifu.util.ActivityUtil;
import com.wanshifu.util.DateHelper;
import com.wanshifu.util.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/6/27 9:58
 */
@Service
@Slf4j
public class TocMasterActivityServiceImpl extends AbstractService implements TocMasterActivityService {

    /**
     * 图片aid换url
     */
    @Value("${wanshifu.image-service.url}")
    private String imageUrl;

    /**
     * 师傅有奖任务链接
     */
    @Value("${activity.master.awardTask.url}")
    private String awardTaskUrl;

    /**
     * 师傅订单活动
     */
    @Value("${wanshifu.master.orderActivity.url}")
    private String orderActivityUrl;

    @Resource
    private ActivityCenterManager activityCenterManager;
    @Resource
    private RewardApi rewardApi;
    @Resource
    private BonusOperateApi bonusOperateApi;

    /**
     * 活动中心列表
     */
    @Override
    @ExceptionHandle(note = "师傅活动中心列表接口异常")
    public MasterActivityListApiResp list(TocMasterActivityListApiRqt masterActivityListApiRqt) {
        MasterActivityListApiResp masterActivityListApiResp = new MasterActivityListApiResp();


        List<MasterActivityListItemResp> masterActivityListItemResps = new ArrayList<>();

        MasterActivityListRqt masterActivityListRqt = new MasterActivityListRqt();
        masterActivityListRqt.setMasterId(masterActivityListApiRqt.getUserId());
        masterActivityListRqt.setType(masterActivityListApiRqt.getType());
        MasterActivityListResp masterActivityListResp;

        try {
            log.info(JSON.toJSONString(masterActivityListRqt));
            masterActivityListResp = masterActivityBusinessServiceApi.activityList(masterActivityListRqt);
            if (CollectionUtils.isEmpty(masterActivityListResp.getActivityLists())) {
                return masterActivityListApiResp;
            }
        } catch (ApiAccessException e) {
            throw new BusException("get_master_activity_list_fail", e.getMessage());
        }

        List<MasterActivityListResp.ActivityList> activityLists = masterActivityListResp.getActivityLists();

        //图
        List<String> imageAidList = new ArrayList<>();
        for (MasterActivityListResp.ActivityList activityList : activityLists) {
            if (activityList.getImgAid() != null && !imageAidList.contains(activityList.getImgAid().toString())) {
                imageAidList.add(activityList.getImgAid().toString());
            }
        }

        Map<Long, String> imageResults = null;
        //批量获取图片返回map<aid,url>
        if (imageAidList.size() > 0) {
            imageResults = httpImageUtils.sendPostRequest(imageAidList);
        }

        List<Long> pIds = activityLists.stream().
                filter(f -> f.getRewardExtraId() != null && f.getRewardExtraId() > 0).map(
                        MasterActivityListResp.ActivityList::getRewardExtraId).collect(
                        Collectors.toList());
        List<ListRemainNumResp> resultRespList = super.getListRemainNumResp(pIds);
        List<Long> activityIdList = activityLists.stream()
                .filter(a -> Objects.nonNull(a.getTaskLabelTextBo()) && TaskTypeEnum.COMPLETE_ACTIVITY_TASK.symbol.equals(a.getTaskLabelTextBo().getTaskSymbol()))
                .map(a -> a.getActivityId()).collect(Collectors.toList());
        Map<Long, String> unitMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(activityIdList)) {
            GetCompleteActivityTaskUnitsRqt queryRqt = new GetCompleteActivityTaskUnitsRqt();
            queryRqt.setActivityIdList(activityIdList);
            GetCompleteActivityTaskUnitsResp completeActivityTaskUnits = activityBusinessServiceApi.getCompleteActivityTaskUnits(queryRqt);

            if (Objects.nonNull(completeActivityTaskUnits)) {
                unitMap = completeActivityTaskUnits.getUnitMap();
            }
        }

        for (MasterActivityListResp.ActivityList activityList : activityLists) {
            MasterActivityListItemResp masterActivityListItemResp = new MasterActivityListItemResp();
            BeanUtils.copyProperties(activityList, masterActivityListItemResp);

            masterActivityListItemResp.setUpdateTime(activityList.getUpdateTime());
            assert imageResults != null;
            masterActivityListItemResp.setImgUrl(
                    imageResults.getOrDefault(activityList.getImgAid(), "")
            );
            //设置活动落地页地址
//            masterActivityListItemResp.setUrl(this.returnLandingPageUrl(activityList.getActivityModel(), activityList.getActivityId()));
            masterActivityListItemResp.setUrl(activityList.getActivityUrl());
            masterActivityListItemResp.setRightTipText(this.setRightTipTextResp(activityList, unitMap.get(activityList.getActivityId())));
            //处理抽奖机会库存用光的展示
            if (activityList.getRewardExtraId() != null) {
                Optional<ListRemainNumResp> listRemainNumResp = resultRespList.stream().filter(f -> f.getPrizeWheelId().equals(activityList.getRewardExtraId())).findFirst();
                if (!listRemainNumResp.isPresent()) {
                    masterActivityListItemResp.setJoinStatus(MasterActivityListButtonEnum.ATTENDED.getListType());
                }
            }
            masterActivityListItemResps.add(masterActivityListItemResp);
        }

        //实现排序
        masterActivityListItemResps.sort(new Comparator<MasterActivityListItemResp>() {
            @Override
            public int compare(MasterActivityListItemResp u1, MasterActivityListItemResp u2) {
                if (u1.getActivityId() > (u2.getActivityId())) {
                    //return -1:即为正序排序
                    return -1;
                } else if (u1.getActivityId().equals(u2.getActivityId())) {
                    return 0;
                } else {
                    //return 1: 即为倒序排序
                    return 1;
                }
            }
        });

        if (masterActivityListApiRqt.getType().equals(MasterActivityListEnum.RECOMMEND.getListType())) {
            //为你优选排序
            List<MasterActivityListItemResp> masterActivityListItemResps1 = activityCenterManager.masterActivityListItemSort(masterActivityListApiRqt.getUserId(), masterActivityListItemResps);
            masterActivityListApiResp.setActivityList(masterActivityListItemResps1);
        } else {

            masterActivityListApiResp.setActivityList(masterActivityListItemResps);
        }


        return masterActivityListApiResp;
    }

    private String setRightTipTextResp(MasterActivityListResp.ActivityList activityList, String unit) {
        if (null == activityList.getTaskLabelTextBo()) {
            return null;
        }
        TaskLabelTextBo taskLabelTextBo = activityList.getTaskLabelTextBo();
        RewardSymbol rewardSymbol = RewardSymbol.fromType(taskLabelTextBo.getRewardSymbol());
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("还差").append(taskLabelTextBo.getNeedRewardNum()).append(StringUtils.isEmpty(unit) ? "单" : unit)
                .append(taskLabelTextBo.getTaskName()).append("可领")
                .append(MathUtils.decimalCutZero(taskLabelTextBo.getRewardValue())).append(rewardSymbol.simpleAlias).append(rewardSymbol.cn);


        return stringBuilder.toString();
    }

    /**
     * 待预约列表 标签
     *
     * @param req
     */
    @Override
    public Map<String, ReservationListApiResp> reservationList(TocReservationListApiReq req) {
        Map<String, ReservationListApiResp> result = new HashMap<>();
        List<Long> orderIdList = req.getOrderIdList();
        if(CollectionUtils.isEmpty(orderIdList)){
            return result;
        }
        if(orderIdList.size() > 20){
            throw new BusException("订单不能超过20个");
        }
        //获取订单信息
        List<OrderInfoExtTemp> orderInfoTemps = super.returnOrderBaseExtInfo(req.getOrderIdList(), req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return result;
        }

        Date nowDate = new Date();
        //过滤出不在免打扰时间段内的订单
        List<OrderInfoExtTemp> notFreeTimeRangeOrders = orderInfoTemps.stream().filter(f ->
                        (f.getFreeEndTime() != null && f.getFreeStartTime() != null && !(nowDate.after(f.getFreeStartTime()) && nowDate.before(f.getFreeEndTime()))))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notFreeTimeRangeOrders)) {
            return result;
        }

        //获取人群信息

        List<Long> merchantCreateUserIdList = orderInfoTemps.stream().map(OrderInfoExtTemp::getCreateOrderUserId).collect(Collectors.toList());
        //获取商家人群信息
        Map<Long, List<Long>> merchantGroupIdMap = this.getMerchantGroupIdMap(merchantCreateUserIdList);


        ReservationListReq reservationListReq = new ReservationListReq();
        List<ReservationListReq.OrderIntoExtItem> orderList = new ArrayList<>();

        for (OrderInfoExtTemp notFreeTimeRangeOrder : notFreeTimeRangeOrders) {
            ReservationListReq.OrderIntoExtItem orderInfoItem = new ReservationListReq.OrderIntoExtItem();
            BeanUtils.copyProperties(notFreeTimeRangeOrder, orderInfoItem);
            orderInfoItem.setServeLevel1Id(StringUtils.isEmpty(notFreeTimeRangeOrder.getServeLevel1Ids()) ? null : Long.valueOf(notFreeTimeRangeOrder.getServeLevel1Ids()));

            if (!StringUtils.isEmpty(notFreeTimeRangeOrder.getServeIds())) {
                String[] strArr = notFreeTimeRangeOrder.getServeIds().split(",");
                List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
                orderInfoItem.setServeIds(serveIdList);
            }
            orderInfoItem.setAppointMethod(AppointMethodEnum.getTypeTxt(notFreeTimeRangeOrder.getAppointType()));

            orderInfoItem.setUserCrowIds(merchantGroupIdMap.getOrDefault(notFreeTimeRangeOrder.getCreateOrderUserId(), new ArrayList<>()));

            orderInfoItem.setOtherOrderFilterCondition(notFreeTimeRangeOrder.getPropertyList());
//            List<String> otherFilterCondition = new ArrayList<>();
//            //团队师傅
//            if (notFreeTimeRangeOrder.getIsOrderContract() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_CONTRACT);
//            }
//
//            //订单包
//            if (notFreeTimeRangeOrder.getIsOrderPackage() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_PACKAGE);
//            }
//
//            //师傅店铺
//            if (notFreeTimeRangeOrder.getIsOrderShop() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_SHOP);
//            }
//            orderInfoItem.setOtherOrderFilterCondition(otherFilterCondition);

            orderList.add(orderInfoItem);
        }
        reservationListReq.setMasterId(req.getUserId());
        reservationListReq.setOrderList(orderList);
        reservationListReq.setTaskSymbol(req.getTaskSymbol());

        try {
            log.info(JSON.toJSONString(reservationListReq));
            ReservationListResp reservationListResp = masterActivityBusinessServiceApi.reservationListTag(reservationListReq);
            if (reservationListResp == null|| CollectionUtils.isEmpty(reservationListResp.getLists())) {
                return result;
            }
            List<ReservationListResp.ReservationItem> lists = reservationListResp.getLists();

            for (ReservationListResp.ReservationItem list : lists) {
                ReservationListApiResp reservationListApiResp = new ReservationListApiResp();
                OrderInfoExtTemp orderInfoExtTemp = notFreeTimeRangeOrders.stream().filter(f -> f.getOrderNo().equals(list.getOrderNo())).findFirst().orElseThrow(() -> new BusException("找不到订单信息"));
                reservationListApiResp.setFreeEndTime(this.returnTimeStamp(orderInfoExtTemp.getFreeEndTime()));
                reservationListApiResp.setFreeStartTime(this.returnTimeStamp(orderInfoExtTemp.getFreeStartTime()));
                reservationListApiResp.setReservationEndTime(this.returnTimeStamp(orderInfoExtTemp.getReservationEndTime()));
                reservationListApiResp.setReservationStartTime(this.returnTimeStamp(orderInfoExtTemp.getReservationStartTime()));
                reservationListApiResp.setRewardTxt(list.getRewardTxt());
                reservationListApiResp.setCountDownNumber(list.getCountDownNumber());
                result.put(list.getOrderId().toString(), reservationListApiResp);
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("请求平台接口出现异常");
            return result;
        }

    }

    /**
     * 师傅奖励列表
     */
    @Override
    @ExceptionHandle(note = "师傅奖励列表接口异常")
    public SimplePageInfo<MasterRewardListApiResp> rewardList(TocMasterRewardListApiRqt masterRewardListApiRqt) {
        try {

            SimplePageInfo<MasterRewardListApiResp> masterRewardListApiRespSimplePageInfo = new SimplePageInfo<>();

            List<MasterRewardListApiResp> masterRewardListApiRespList = new ArrayList<>();

            MasterRewardListRqt masterRewardListRqt = new MasterRewardListRqt();
            BeanUtils.copyProperties(masterRewardListApiRqt, masterRewardListRqt);
            masterRewardListRqt.setMasterId(masterRewardListApiRqt.getUserId());
            SimplePageInfo<MasterRewardListResp> masterRewardListRespList = masterActivityBusinessServiceApi.rewardList(masterRewardListRqt);

            //移除抽奖机会已经到上限的库存
            List<Long> pIds = masterRewardListRespList.getList().stream().
                    filter(f -> (RewardSymbol.LOTTERY_TURNTABLE.type.equals(f.getRewardType())) && f.getRewardExtraId() != null && f.getRewardExtraId() > 0).map(
                            MasterRewardListResp::getRewardExtraId).collect(
                            Collectors.toList());
            List<ListRemainNumResp> resultRespList = super.getListRemainNumResp(pIds);

            if (masterRewardListRespList.getList() != null) {
                for (MasterRewardListResp masterRewardListResp : masterRewardListRespList.getList()) {
                    //移除库存上限的奖励
                    if (RewardSymbol.LOTTERY_TURNTABLE.type.equals(masterRewardListResp.getRewardType())) {
                        Optional<ListRemainNumResp> listRemainNumRespOptional = resultRespList.stream().filter(f -> f.getPrizeWheelId().equals(masterRewardListResp.getRewardExtraId())).findFirst();
                        if (!listRemainNumRespOptional.isPresent()) {
                            continue;
                        }
                    }

                    MasterRewardListApiResp masterRewardListApiResp = new MasterRewardListApiResp();
                    BeanUtils.copyProperties(masterRewardListResp, masterRewardListApiResp);
                    if (masterRewardListResp.getIsLanding() == 1) {
                        masterRewardListApiResp.setUrl(masterRewardListResp.getActivityUrl() + "&t=" + masterRewardListApiRqt.getIsWeb());
                    } else {
                        masterRewardListApiResp.setUrl("");
                    }
                    masterRewardListApiRespList.add(masterRewardListApiResp);
                    masterRewardListApiResp.setIsLanding(masterRewardListResp.getIsLanding());
                }
            }

            masterRewardListApiRespSimplePageInfo.setList(masterRewardListApiRespList);
            masterRewardListApiRespSimplePageInfo.setPageNum(masterRewardListRespList.getPageNum());
            masterRewardListApiRespSimplePageInfo.setPageSize(masterRewardListRespList.getPageSize());
            masterRewardListApiRespSimplePageInfo.setTotal(masterRewardListRespList.getTotal());
            masterRewardListApiRespSimplePageInfo.setPages(masterRewardListRespList.getPages());

            return masterRewardListApiRespSimplePageInfo;
        } catch (BusinessException e) {
            SimplePageInfo<MasterRewardListApiResp> masterRewardListApiRespSimplePageInfo = new SimplePageInfo<>();

            List<MasterRewardListApiResp> masterRewardListApiRespList = new ArrayList<>();
            masterRewardListApiRespSimplePageInfo.setList(masterRewardListApiRespList);

            return masterRewardListApiRespSimplePageInfo;
        } catch (ApiAccessException e) {
            throw new BusException("get_reward_list_fail", e.getMessage());
        }
    }


    /**
     * 返回落地页地址
     */
    @Override
    public String returnLandingPageUrl(String activityModel, Long activityId) {
        if (activityModel.equals(ActivityTypeEnum.ORDER_TASK.getActivityType())) {
            return orderActivityUrl + activityId;
        } else {
            return awardTaskUrl + activityId;
        }
    }

    /**
     * 抢单详情
     */
    @Deprecated
    @Override
    public GrabOrderButtonApiResp grabInfoBt(TocOfferInfoActivityListApiRqt offerInfoActivityListApiRqt) {

        GrabOrderButtonApiResp grabOrderButtonApiResp = new GrabOrderButtonApiResp();

        OfferInfoActivityListRqt offerInfoActivityListRqt = new OfferInfoActivityListRqt();

        BeanUtils.copyProperties(offerInfoActivityListApiRqt, offerInfoActivityListRqt);
        offerInfoActivityListRqt.setServeLevel1Id(Long.valueOf(offerInfoActivityListApiRqt.getServeLevel1Ids()));

        if (!StringUtils.isEmpty(offerInfoActivityListApiRqt.getServeIds())) {
            String[] strArr = offerInfoActivityListApiRqt.getServeIds().split(",");
            List<String> serverIdStrList = Arrays.asList(strArr);

            List<Long> serveIdList = new ArrayList<>();
            for (String s : serverIdStrList) {
                serveIdList.add(Long.valueOf(s));
            }
            offerInfoActivityListRqt.setServeIds(serveIdList);
        }


        offerInfoActivityListRqt.setMasterId(offerInfoActivityListApiRqt.getUserId());
        offerInfoActivityListRqt.setUserId(offerInfoActivityListApiRqt.getCreateOrderUserId());

        //下单用户人群信息
        offerInfoActivityListRqt.setUserCrowIds(super.getUserGroupIds(offerInfoActivityListApiRqt.getCreateOrderUserId().toString(), "1"));

        //师傅画像接口 TODO


        try {
            GrabOrderButtonResp grabOrderButtonResp = masterActivityBusinessServiceApi.grabInfoBt(offerInfoActivityListRqt);
            BeanUtils.copyProperties(grabOrderButtonResp, grabOrderButtonApiResp);
            return grabOrderButtonApiResp;
        } catch (Exception e) {
            throw new BusException("grab_info_button_fail", e.getMessage());
        }
    }

    /**
     * 抢单列表  批量人群标签 批量查询
     */
    @Override
    @ExceptionHandle(note = "师傅接单列表活动标签接口异常")
    public MasterOrderListResp orderListTag(TocOrderListApiRqt orderListApiRqt) {

        MasterOrderListResp masterOrderListResp = new MasterOrderListResp();
        if (true) {
            return masterOrderListResp;
        }

        List<TocOrderListApiRqt.OrderInfoItem> orderLists = orderListApiRqt.getOrderLists();

        OrderListRqt orderListRqt = new OrderListRqt();
        orderListRqt.setMasterId(orderListApiRqt.getUserId());

        Map<String, MasterOrderListResp.MasterOrderListItem> masterOrderListItemMap = new HashMap<>();

        List<MasterOrderInfoBo> orderInfoItemList = new ArrayList<>();

        List<Long> userIdList = orderLists.stream().map(TocOrderListApiRqt.OrderInfoItem::getCreateOrderUserId).collect(Collectors.toList());
        //批量查询用户人群id
        Map<Long, List<Long>> userCrowIdListMap = super.batchSelectUserIdList(userIdList);

        for (TocOrderListApiRqt.OrderInfoItem orderList : orderLists) {
            MasterOrderInfoBo orderInfoItem = new MasterOrderInfoBo();
            BeanUtils.copyProperties(orderList, orderInfoItem);
            orderInfoItem.setServeLevel1Id(Long.valueOf(orderList.getServeLevel1Ids()));

            if (!StringUtils.isEmpty(orderList.getServeIds())) {
                String[] strArr = orderList.getServeIds().split(",");
                List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
                orderInfoItem.setServeIds(serveIdList);
            }
            orderInfoItem.setAppointMethod(AppointMethodEnum.getTypeTxt(orderList.getAppointType()));

            orderInfoItem.setUserCrowIds(userCrowIdListMap.getOrDefault(orderList.getCreateOrderUserId(), new ArrayList<>()));

            orderInfoItemList.add(orderInfoItem);
        }
        try {
            orderListRqt.setOrderInfoItemList(orderInfoItemList);

            List<OrderListResp> orderListRespList = masterActivityBusinessServiceApi.orderListTag(orderListRqt);


            for (OrderListResp orderListResp : orderListRespList) {

                MasterOrderListResp.MasterOrderListItem masterOrderListItem = new MasterOrderListResp.MasterOrderListItem();
                BeanUtils.copyProperties(orderListResp, masterOrderListItem);
                if (orderListResp.getCountTime() != null) {
                    masterOrderListItem.setCountTime(orderListResp.getCountTime() < 0 ? 0 : orderListResp.getCountTime());
                }
                if (orderListResp.getNum() != null) {
                    masterOrderListItem.setNum(new BigDecimal(MathUtils.convertByPattern(orderListResp.getNum().toString())));
                    if (orderListResp.getReturnPointSource() != null) {
                        if (orderListResp.getReturnPointSource().equals(ReturnPointSourceEnum.COMMISSION.type)) {
                            masterOrderListItem.setNumStr("");
                        } else {
                            masterOrderListItem.setNumStr(MathUtils.convertByPattern(orderListResp.getNum().toString()));
                        }
                    } else {
                        masterOrderListItem.setNumStr(MathUtils.convertByPattern(orderListResp.getNum().toString()));
                    }
                }

                masterOrderListItem.setNumType(orderListResp.getNumType());
                masterOrderListItem.setReturnPointSource(orderListResp.getReturnPointSource());

                //文案信息
                if (masterOrderListItem.getTagType() != null && masterOrderListItem.getTagType() != 0) {
                    MasterOrderListResp.MasterOrderListItem masterOrderListItem1 = this.returnMasterOrderListItem(masterOrderListItem.getTagType(), masterOrderListItem.getNumType(), masterOrderListItem.getReturnPointSource());
                    masterOrderListItem.setTagUnit(masterOrderListItem1.getTagUnit());
                    masterOrderListItem.setTagWord(masterOrderListItem1.getTagWord());
                }

                masterOrderListItemMap.put(orderListResp.getOrderNo(), masterOrderListItem);
            }

            masterOrderListResp.setMasterOrderListItemMap(masterOrderListItemMap);

            return masterOrderListResp;
        } catch (Exception e) {
            throw new BusException("order_list_tag_fail", e.getMessage());
        }
    }

    /**
     * 返回奖励文案
     */
    private MasterOrderListResp.MasterOrderListItem returnMasterOrderListItem(Integer tagType
            , String numType, String returnPointSource) {
        MasterOrderListResp.MasterOrderListItem masterOrderListItem = new MasterOrderListResp.MasterOrderListItem();
        if (tagType == 2) {
            masterOrderListItem.setTagWord("本单有奖");
            return masterOrderListItem;
        }

        if (returnPointSource.equals(MasterReturnPointSourceEnum.COMMISSION.getType())) {
            masterOrderListItem.setTagWord("佣金全返");
            return masterOrderListItem;
        }

        if (returnPointSource.equals(MasterReturnPointSourceEnum.TRADE_AMOUNT.getType())) {
            if (numType.equals(MasterRewardGiveTypeEnum.RETURN_POINT.getType())) {
                masterOrderListItem.setTagWord("奖励");
                masterOrderListItem.setTagUnit("%现金");
                return masterOrderListItem;
            } else {
                masterOrderListItem.setTagWord("奖励");
                masterOrderListItem.setTagUnit("元");
                return masterOrderListItem;
            }
        }

        if (StringUtils.isEmpty(returnPointSource)) {
            masterOrderListItem.setTagWord("奖励");
            masterOrderListItem.setTagUnit("元");
            return masterOrderListItem;
        }
        return masterOrderListItem;
    }


    /**
     * 完工详情页奖励提示
     */
    @Override
    public CompleteOrderApiResp completeReward(TocCompleteOrderApiRqt completeOrderApiRqt) {
        CompleteOrderApiResp completeOrderApiResp = new CompleteOrderApiResp();

        CompleteOrderRqt completeOrderRqt = new CompleteOrderRqt();
        completeOrderRqt.setMasterId(completeOrderApiRqt.getUserId());
        completeOrderRqt.setOrderNo(completeOrderApiRqt.getOrderNo());

        try {
            CompleteOrderResp completeOrderResp = masterActivityBusinessServiceApi.completeReward(completeOrderRqt);

            ActivityTask activityTask = completeOrderResp.getActivityTask();
            ActivityOrderRelation activityOrderRelation = completeOrderResp.getActivityOrderRelation();
            ActivityBase activityBase = completeOrderResp.getActivityBase();
            UserActivityTaskExecuteBatch userActivityTaskExecuteBatch = completeOrderResp.getUserActivityTaskExecuteBatch();

            //活动订单关联表没有数据，说明未完成任务，不展示
            if (activityOrderRelation == null) {

                return completeOrderApiResp;
            }

            //节点错误的过滤掉
            if (activityTask != null && !activityTask.getRewardGiveNode().equals(MasterActivityRewardNodeEnum.TASK_ORDER_FINISH_GIVE.getType())) {

                return completeOrderApiResp;
            }

            Date nowDate = new Date();


            //领奖时间内
            if (activityBase.getRewardGiveStartTime().before(nowDate) && activityBase.getRewardGiveEndTime().after(nowDate)
                    && userActivityTaskExecuteBatch.getRewardGiveState() == 1) {
                completeOrderApiResp.setState(1);
                completeOrderApiResp.setRewardInfo("您已获得订单活动奖励");
                completeOrderApiResp.setRewardDesc("请稍后到活动中心领取");
                return completeOrderApiResp;
            }

            //月 周 日上限
            IsReachRewardUpperResp isReachRewardUpperResp = completeOrderResp.getIsReachRewardUpperResp();
            if (isReachRewardUpperResp.isReachUpper()) {
                RewardUpperEnum rewardUpperEnum = RewardUpperEnum.fromType(isReachRewardUpperResp.getRewardUpperEnum().code);
                switch (rewardUpperEnum) {
                    case USER_MONTH_UPPER_LIMIT_OVER:
                        completeOrderApiResp.setState(0);
                        completeOrderApiResp.setRewardInfo("已达本月领奖上限");
                        completeOrderApiResp.setRewardDesc("次月可前往活动中心领取");
                        return completeOrderApiResp;
                    case USER_WEEK_UPPER_LIMIT_OVER:
                        completeOrderApiResp.setState(0);
                        completeOrderApiResp.setRewardInfo("已达本周领奖上限");
                        completeOrderApiResp.setRewardDesc("下周可前往活动中心领取");
                        return completeOrderApiResp;

                    case USER_DAY_UPPER_LIMIT_OVER:
                        completeOrderApiResp.setState(0);
                        completeOrderApiResp.setRewardInfo("已达今日领奖上限");
                        completeOrderApiResp.setRewardDesc("明日可前往活动中心领取");
                        return completeOrderApiResp;
                    case USER_UPPER_LIMIT_OVER:
                        completeOrderApiResp.setState(0);
                        completeOrderApiResp.setRewardInfo("已达活动期间领奖上限");
                        completeOrderApiResp.setRewardDesc("下次继续努力~");
                        return completeOrderApiResp;
                    default:
                        completeOrderApiResp.setState(0);
                        completeOrderApiResp.setRewardInfo("奖励已领完");
                        completeOrderApiResp.setRewardDesc("下次继续努力~");
                        return completeOrderApiResp;
                }
            }

            //领奖时间范围内，手动领奖，未发放，未到奖励上限
            if (activityBase.getRewardGiveStartTime().before(nowDate) && activityBase.getRewardGiveEndTime().after(nowDate)
                    && userActivityTaskExecuteBatch.getRewardGiveState() == 0 && activityBase.getRewardGiveMethod().equals(RewardGiveMethodEnum.MANUAL.type)) {
                completeOrderApiResp.setState(1);
                completeOrderApiResp.setRewardInfo("您已获得订单活动奖励");
                completeOrderApiResp.setRewardDesc("请稍后到活动中心领取");
                return completeOrderApiResp;
            }


            //未到领奖时间
            if (activityBase.getRewardGiveStartTime().after(nowDate)) {
                completeOrderApiResp.setState(1);
                completeOrderApiResp.setRewardInfo("您已获得订单活动奖励");
                completeOrderApiResp.setRewardDesc("后奖励开始发放");
                long count = (activityBase.getRewardGiveStartTime().getTime() - nowDate.getTime()) / 1000L;
                completeOrderApiResp.setCount(count);
                return completeOrderApiResp;
            }


            //奖励校验失败
            if (RewardGiveQualificationStateEnum.NOT_PASS_CHECK.getCode()
                    .equals(userActivityTaskExecuteBatch.getRewardGiveQualificationState())) {
                completeOrderApiResp.setState(0);
                completeOrderApiResp.setRewardInfo("本单未获得奖励");
                return completeOrderApiResp;
            }


            //奖励已过期
            if (activityBase.getRewardGiveEndTime().before(nowDate)) {
                completeOrderApiResp.setState(0);
                completeOrderApiResp.setRewardInfo("奖励已过期");
                return completeOrderApiResp;
            }
            return completeOrderApiResp;

        } catch (ApiAccessException e) {
            throw new BusException("complete_info_reward", e.getMessage());
        }
    }

    /**
     * 订单详情奖励提醒
     *
     * @param orderDetailRewardRemindApiRqt
     * @return
     */
    @Override
    public OrderDetailRewardRemindApiResp orderDetailRewardRemind(TocOrderDetailRewardRemindApiRqt orderDetailRewardRemindApiRqt) {
        OrderDetailRewardRemindApiResp resp = new OrderDetailRewardRemindApiResp();
        if (true) {
            return resp;
        }

        OrderDetailRewardRemindRqt orderDetailRewardRemindRqt = new OrderDetailRewardRemindRqt();
        orderDetailRewardRemindRqt.setOrderNo(orderDetailRewardRemindApiRqt.getOrderNo());
        orderDetailRewardRemindRqt.setUserClass(orderDetailRewardRemindApiRqt.getUserClass());
        orderDetailRewardRemindRqt.setUserId(orderDetailRewardRemindApiRqt.getUserId());
        orderDetailRewardRemindRqt.setTaskSymbol(TaskTypeEnum.QUOTED.symbol);
        OrderDetailRewardRemindResp orderDetailRewardRemind = null;
        try {
            orderDetailRewardRemind = masterActivityBusinessServiceApi.orderDetailRewardRemind(orderDetailRewardRemindRqt);
        } catch (Exception e) {
            log.error("调用平台订单详情奖励提醒接口异常：" + e.getMessage());
            return null;
        }
        /**
         * 解析出参
         */
        if (ObjectUtils.isEmpty(orderDetailRewardRemind) || orderDetailRewardRemind.getActivityId() == null) {
            return null;
        }

        resp.setRewardGiveNode(orderDetailRewardRemind.getRewardGiveNode());
        //判断是否过了领奖时间
        String taskSymbolCn = MasterActivityTypeEnum.getActivityValue("quoted");
//        if (orderDetailRewardRemind.getRewardGiveEndTime().before(new Date())) {
//            resp.setShowTitle(taskSymbolCn + "奖励已过期");
//            resp.setIconType(IconTypeEnum.EXCEPTIONS.code);
//            return resp;
//        }
        //领奖校验失败
        Integer rgState = orderDetailRewardRemind.getRewardGiveQualificationState();
        if (rgState == ActivityConstant.REWARD_CHECK_THREE) {
            resp.setShowTitle("很遗憾，本单未获得" + taskSymbolCn + "奖励");
            resp.setIconType(IconTypeEnum.EXCEPTIONS.code);
            return resp;
        }
        //待校验
        if (rgState == ActivityConstant.REWARD_CHECK_ONE) {
            resp.setShowTitle(this.getRewardUncheckTitle(orderDetailRewardRemind, taskSymbolCn, null));
            //奖励过期特殊处理
            if (orderDetailRewardRemind.getRewardGiveEndTime().before(new Date())) {
                resp.setShowTitle(taskSymbolCn + "奖励已过期");
                resp.setIconType(IconTypeEnum.EXCEPTIONS.code);
                return resp;
            }
            resp.setIconType(IconTypeEnum.QUALIFY.code);
            return resp;
        }
        //达到领奖条件，奖励发放校验
        if (rgState == ActivityConstant.REWARD_CHECK_ZERO || rgState == ActivityConstant.REWARD_CHECK_TWO) {
            resp.setIconType(IconTypeEnum.GET_REWARDS.code);
            //判断是否到领奖时间
            if (orderDetailRewardRemind.getRewardGiveStartTime().after(new Date())) {
                String countDownTimeStr = MathUtils.getDistanceOfTwoDateStr(new Date(), orderDetailRewardRemind.getRewardGiveStartTime());
                resp.setShowTitle(countDownTimeStr + "后奖励开始发放");
                resp.setCountDownNumber((orderDetailRewardRemind.getRewardGiveStartTime().getTime() - System.currentTimeMillis()) / 1000);
                resp.setIconType(IconTypeEnum.GET_REWARDS.code);
                return resp;
            }
            GiveRewardCheckTitleResp showTitleResp = this.getGiveRewardCheckTitle(orderDetailRewardRemind, taskSymbolCn);
            resp.setShowTitle(showTitleResp.getShowTitle());
            resp.setIconType(showTitleResp.getIconType());
            return resp;
        }
        return null;
    }

    /**
     * 获取待领取奖励和待解锁奖励数据
     *
     * @param masterAwardingNumApiRqt
     * @return
     */
    @Override
    @ExceptionHandle(note = "师傅端红包界面获取奖励记录接口异常")
    public AwardingNumAndUnLockNumResp getAwardingNumAndUnLockNum(TocMasterAwardingNumApiRqt masterAwardingNumApiRqt) {
        //是否登陆
        Long userId = null;
        if (org.apache.commons.lang.StringUtils.isNotEmpty(masterAwardingNumApiRqt.getSignature())) {
            userId = this.getMasterIdBySignature(masterAwardingNumApiRqt.getSignature());
        }
        boolean isLogin = (userId != null);
        MasterAwardingNumRqt masterRewardRemindNumRqt = new MasterAwardingNumRqt();
        masterRewardRemindNumRqt.setUserId(userId);
        masterRewardRemindNumRqt.setUserClass(masterAwardingNumApiRqt.getUserClass());
        try {
            com.wanshifu.iop.activity.domain.api.response.master.AwardingNumAndUnLockNumResp resultResp = masterActivityBusinessServiceApi.getAwardingNumAndUnLockNum(masterRewardRemindNumRqt);
            int resultCode = 0;
            if (resultResp != null) {
                resultCode = resultResp.getAwardingNum();
            }
            //待领取的奖励数量（红包+积分）
//            CommonMasterIdRqt commonMasterIdRqt = new CommonMasterIdRqt();
//            commonMasterIdRqt.setMasterId(userId);
//            WaitReceiveCountResp masterInteger = rewardApi.waitReceiveCount(commonMasterIdRqt);
            //调整成下沉service提供接口
            Integer wrappedCount = bonusOperateApi.getWrappedCounts(userId, "master");
            AwardingNumAndUnLockNumResp resp = new AwardingNumAndUnLockNumResp();

            resp.setAwardingNum(resultCode + wrappedCount);
            resp.setUnLockAmount(resultResp.getUnLockAmount());
            resp.setUnLockNum(resultResp.getUnLockNum());
            return resp;
        } catch (Exception e) {
            log.error("调用平台活动服务异常：" + e.getMessage());
            return new AwardingNumAndUnLockNumResp();
        }

    }

    /**
     * 获取未领奖校验文案
     *
     * @param orderDetailRewardRemind
     * @return
     */
    private String getRewardUncheckTitle(OrderDetailRewardRemindResp orderDetailRewardRemind, String taskSymbolCn, OrderDetailRewardBatchRemindResp.NodeTaskInfo nodeTaskInfo) {
        String title = "";
        //节点
        String rewardGiveNode = orderDetailRewardRemind.getRewardGiveNode();
        //奖励模式
        RewardSymbol rewardSymbolEnum = RewardSymbol.fromType(orderDetailRewardRemind.getRewardSymbol());
        //返点基数来源
        String returnPointSource = orderDetailRewardRemind.getReturnPointSource();
        //奖励发放类型
        String rewardGiveType = orderDetailRewardRemind.getRewardGiveType();
        //奖励发放值
        BigDecimal rewardGiveValue = new BigDecimal(this.returnGiveRewardValue(nodeTaskInfo));
        //是否隐藏奖励数值
        Integer isShowGiveValue = orderDetailRewardRemind.getIsShowGiveValue();
        //规则标志
        String ruleConfigSign = orderDetailRewardRemind.getRuleConfigSign();
        //奖励发放节点
        String nodeName = "";
        if (RewardGiveNodeEnum.TASK_ORDER_FINISH_GIVE.getRewardGiveNode().equals(rewardGiveNode)) {
            nodeName = "完工";
        } else if (RewardGiveNodeEnum.TASK_ORDER_DONE_GIVE.getRewardGiveNode().equals(rewardGiveNode)) {
            nodeName = "订单成交";
        } else if (RewardGiveNodeEnum.TASK_ORDER_APPOINT.getRewardGiveNode().equals(rewardGiveNode)) {
            nodeName = "被指派";
        } else {
            nodeName = "系统校验完成";
        }
        //去掉多余的0
        String rewardGiveValueSub = MathUtils.decimalCutZero(rewardGiveValue);
        switch (rewardSymbolEnum) {
            case BONUS:
                //展示
                if (isShowGiveValue == 1) {
                    String rewardGiveValueStr = RewardGiveTypeEnum.FIXED_VALUE.type.equals(rewardGiveType) ? rewardGiveValueSub + "元" : rewardGiveValueSub + nodeTaskInfo.getRuleConfigUnit() + "现金";
                    title = String.format(ActivityConstant.showTitle1, taskSymbolCn, nodeName, rewardGiveValueStr);
                    if (ActivityConstant.commissionAmountRate.equals(ruleConfigSign)) {
                        title = String.format(ActivityConstant.showTitle2, taskSymbolCn, nodeName);
                    }
                } else {
                    title = String.format(ActivityConstant.showTitle3, taskSymbolCn, nodeName);
                }
                break;
            case INTEGRAL:
                String rewardGiveValueStr = "";
                if (isShowGiveValue == 1) {
                    rewardGiveValueStr = RewardGiveTypeEnum.FIXED_VALUE.type.equals(rewardGiveType) ? rewardGiveValueSub + "" : rewardGiveValueSub + nodeTaskInfo.getRuleConfigUnit();
                }
                title = String.format(ActivityConstant.showTitle4, taskSymbolCn, nodeName, rewardGiveValueStr);
        }
        StringBuilder stringBuilder = new StringBuilder(title);
        //增加期望上门时间额外奖励
        TradeAmountRateBo tradeAmountRateBo = JSONObject.parseObject(nodeTaskInfo.getFrontRuleIndicatorObject(), TradeAmountRateBo.class);
        if (tradeAmountRateBo != null && tradeAmountRateBo.getExpectedAppointmentRate() != null) {
            //展示
            if (isShowGiveValue == 1) {
                stringBuilder.append(String.format(ActivityConstant.RESERVATION_INFO_4_LIST_TXT, tradeAmountRateBo.getExpectedAppointmentRate()));
                //如果是固定值
                if (nodeTaskInfo.getRewardGiveType().equals(RewardGiveTypeEnum.FIXED_VALUE.type)) {
                    if (nodeTaskInfo.getRewardSymbol().equals(RewardSymbol.BONUS.type)) {
                        stringBuilder.append("元");
                    } else {
                        stringBuilder.append(RewardSymbol.fromType(nodeTaskInfo.getRewardSymbol()).cn);
                    }
                }
                if (nodeTaskInfo.getRewardGiveType().equals(RewardGiveTypeEnum.REWARD_GIVE_TYPE_ENUM.type)) {
                    if (nodeTaskInfo.getRewardSymbol().equals(RewardSymbol.BONUS.type)) {
                        stringBuilder.append("%").append(RewardSymbol.BONUS.simpleCn);
                    } else {
                        stringBuilder.append("%").append(RewardSymbol.fromType(nodeTaskInfo.getRewardSymbol()).cn);
                    }
                }
            } else {
                stringBuilder.append(String.format(ActivityConstant.RESERVATION_INFO_5_LIST_TXT, ""));
            }

        }
        return stringBuilder.toString().endsWith("</>") ? stringBuilder.toString() : stringBuilder + "</>";
    }

    /**
     * 达到领奖条件，发奖励校验
     *
     * @param orderDetailRewardRemind
     * @return
     */
    private GiveRewardCheckTitleResp getGiveRewardCheckTitle(OrderDetailRewardRemindResp orderDetailRewardRemind, String taskSymbolCn) {
        GiveRewardCheckTitleResp resp = new GiveRewardCheckTitleResp();
        String title = "";
        //是否已到领奖上限
        StringBuilder builder = new StringBuilder();
        if (!ObjectUtils.isEmpty(orderDetailRewardRemind.getRewardUpperEnum())) {
            //达到上限，但是已领奖
            if (orderDetailRewardRemind.getRewardGiveSuccess() != null && orderDetailRewardRemind.getRewardGiveSuccess() == 1) {
                resp.setShowTitle(builder.append(taskSymbolCn).append("奖励已到账").toString());
                resp.setIconType(IconTypeEnum.GET_REWARDS.code);
                return resp;
            }
            switch (orderDetailRewardRemind.getRewardUpperEnum()) {
                case MAX_UPPER_LIMIT_OVER:
                    title = builder.append(taskSymbolCn).append("奖励已领完，下次继续努力~").toString();
                    break;
                case USER_UPPER_LIMIT_OVER:
                    title = builder.append("已达活动期间").append(taskSymbolCn).append("奖励上限，下次继续努力~").toString();
                    break;
                case USER_MONTH_UPPER_LIMIT_OVER:
                    title = builder.append("已达本月").append(taskSymbolCn).append("奖励上限，次月可前往活动中心领取").toString();
                    break;
                case USER_WEEK_UPPER_LIMIT_OVER:
                    title = builder.append("已达本周").append(taskSymbolCn).append("奖励上限，下周可前往活动中心领取").toString();
                    break;
                case USER_DAY_UPPER_LIMIT_OVER:
                    title = builder.append("已达今日").append(taskSymbolCn).append("奖励上限，明日可前往活动中心领取").toString();
                    break;
            }
            resp.setShowTitle(title);
            resp.setIconType(IconTypeEnum.EXCEPTIONS.code);
            return resp;
        }
        //没领，但是领奖时间已过期
        if (orderDetailRewardRemind.getRewardGiveEndTime().before(new Date())) {
            resp.setShowTitle(builder.append(taskSymbolCn).append("奖励已过期").toString());
            resp.setIconType(IconTypeEnum.EXCEPTIONS.code);
            return resp;
        }
        //自动发放，但是未领取的提示
        if (OperateMethodEnum.AUTO.code.equals(orderDetailRewardRemind.getRewardGiveMethod())
                && orderDetailRewardRemind.getRewardGiveState() == 0) {
            resp.setShowTitle("请稍后到活动中心查询");
            resp.setIconType(IconTypeEnum.EXCEPTIONS.code);
            return resp;
        }
        //奖励已发放，但是没有发放成功
        if (orderDetailRewardRemind.getRewardGiveState() == 1 && orderDetailRewardRemind.getRewardGiveSuccess() != null
                && orderDetailRewardRemind.getRewardGiveSuccess() == 0) {
            resp.setShowTitle("请稍后到活动中心查询");
            resp.setIconType(IconTypeEnum.EXCEPTIONS.code);
            return resp;
        }
        //奖励已领取
        if (orderDetailRewardRemind.getRewardGiveState() == 1 && orderDetailRewardRemind.getRewardGiveSuccess() == 1) {
            resp.setShowTitle(builder.append(taskSymbolCn).append("奖励已到账").toString());
            resp.setIconType(IconTypeEnum.GET_REWARDS.code);
            return resp;
        }
        //排除以上情况，则为待领奖
        resp.setShowTitle(builder.append(taskSymbolCn).append("奖励已发放，数量有限，领完即止").toString());
        resp.setIconType(IconTypeEnum.GET_REWARDS.code);
        return resp;
    }

    /**
     * 抢单列表
     *
     * @param req
     */
    @Override
    public Map<String, OfferOrderListResp> orderListSomeTag(TocOrderListIdListReq req) {

        Map<String, OfferOrderListResp> result = new HashMap<>();

        //获取订单信息
        List<OrderInfoTemp> orderInfoTemps = super.returnOrderBaseInfo(req.getOrderIdList(), req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return result;
        }
//        List<Long> merchantCreateUserIdList = orderInfoTemps.stream()
//                .filter(f -> "site".equals(f.getOrderFrom())).map(OrderInfoTemp::getCreateOrderUserId).collect(Collectors.toList());

        //只有商家订单才需要获取下单用户人群信息
//        Map<Long, List<Long>> merchantGroupIdMap = this.getMerchantGroupIdMap(merchantCreateUserIdList);

        OrderListRqt orderListRqt = new OrderListRqt();
        orderListRqt.setMasterId(req.getUserId());

        //获取订单师傅距离
//        List<BatchGetPushDistanceResp> batchGetPushDistanceResps = this.getPushDistance(req.getUserId(), req.getOrderIdList());

        //获取技能是否相关
//        Map<String, Integer> skillRelatedStateMap = this.getSkillRelatedState(req.getUserId(), req.getOrderIdList());

        List<MasterOrderInfoBo> orderInfoItemList = new ArrayList<>();
        for (OrderInfoTemp orderInfoTemp : orderInfoTemps) {
            MasterOrderInfoBo masterOrderInfoBo = new MasterOrderInfoBo();
            BeanUtils.copyProperties(orderInfoTemp, masterOrderInfoBo);
            masterOrderInfoBo.setServeLevel1Id(Long.valueOf(orderInfoTemp.getServeLevel1Ids()));
            masterOrderInfoBo.setUserId(orderInfoTemp.getCreateOrderUserId());
            if (!StringUtils.isEmpty(orderInfoTemp.getServeIds())) {
                String[] strArr = orderInfoTemp.getServeIds().split(",");
                List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
                masterOrderInfoBo.setServeIds(serveIdList);
            }
            masterOrderInfoBo.setAppointMethod(AppointMethodEnum.getTypeTxt(orderInfoTemp.getAppointType()));

//            masterOrderInfoBo.setUserCrowIds(merchantGroupIdMap.getOrDefault(orderInfoTemp.getCreateOrderUserId(), new ArrayList<>()));

            masterOrderInfoBo.setOtherOrderFilterCondition(orderInfoTemp.getPropertyList());
//            List<String> otherFilterCondition = new ArrayList<>();
//            //团队师傅
//            if (orderInfoTemp.getIsOrderContract() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_CONTRACT);
//            }
//
//            //订单包
//            if (orderInfoTemp.getIsOrderPackage() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_PACKAGE);
//            }
//
//            //师傅店铺
//            if(orderInfoTemp.getIsOrderShop() == 1){
//                otherFilterCondition.add((MasterOrderInfoActivityListConstant.ORDER_SHOP));
//            }

            //订单距离
//            Optional<BatchGetPushDistanceResp> orderInfoExtTempOp = batchGetPushDistanceResps.stream().filter(f -> f.getOrderId().equals(masterOrderInfoBo.getOrderId())).findFirst();
//            orderInfoExtTempOp.ifPresent(batchGetPushDistanceResp -> masterOrderInfoBo.setDistanceValue(batchGetPushDistanceResp.getPushDistance() != null ? batchGetPushDistanceResp.getPushDistance().intValue() : 0));
//            masterOrderInfoBo.setOtherOrderFilterCondition(otherFilterCondition);


//            //技能是否相关
//            masterOrderInfoBo.setSkillRelatedState(skillRelatedStateMap.get(String.valueOf(orderInfoTemp.getOrderId())));
            masterOrderInfoBo.setSinkQueryFlag(1);
            orderInfoItemList.add(masterOrderInfoBo);
        }
        orderListRqt.setOrderInfoItemList(orderInfoItemList);
        try {
            log.info("masterOrderListTag::" + JSON.toJSONString(orderListRqt));
            OrderListTagResp orderListTagResp = masterActivityBusinessServiceApi.masterOrderListTag(orderListRqt);
            if (orderListTagResp == null) {
                return result;
            }
            Map<String, OfferOrderListServiceResp> offerOrderListTagMap = orderListTagResp.getOfferOrderListTagMap();
            if (offerOrderListTagMap == null) {
                return result;
            }
            offerOrderListTagMap.forEach((k, v) -> {
                OfferOrderListResp offerOrderListResp = new OfferOrderListResp();
                offerOrderListResp.setOrderNo(v.getOrderNo());
                offerOrderListResp.setGoodsTag(v.getGoodsTag());
                List<OfferOrderListServiceResp.OfferOrderListItem> tags = v.getTags();
                List<OfferOrderListResp.OfferOrderListItem> offerOrderListItemList = new ArrayList<>();
                for (OfferOrderListServiceResp.OfferOrderListItem tag : tags) {
                    OfferOrderListResp.OfferOrderListItem offerOrderListItem = new OfferOrderListResp.OfferOrderListItem();
                    offerOrderListItem.setTagText(tag.getTagText());
                    if (TaskTypeEnum.MANUAL_RATE.symbol.equals(tag.getTaskSymbol())) {
                        offerOrderListItem.setTagText("服务获得" + tag.getTagText());
                    }
                    offerOrderListItem.setActivityId(tag.getActivityId());
                    offerOrderListItem.setTaskSymbol(tag.getTaskSymbol());
                    offerOrderListItem.setCountTime(tag.getCountTime() == null ? -1 : tag.getCountTime());
                    offerOrderListItemList.add(offerOrderListItem);
                }

                offerOrderListResp.setTags(offerOrderListItemList);
                result.put(k, offerOrderListResp);
            });
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("出现异常，请求参数={}", JSON.toJSONString(req));
        }
        return result;
    }

    /**
     * 订单详情奖励信息 已抢单（待指派）页面
     *
     * @param req
     */
    @Override
    public OrderDetailRewardRemindBatchApiResp orderDetailRewardNotice(TocOrderDetailRewardRemindBatchApiRqt req) {
        OrderDetailRewardRemindBatchApiResp result = new OrderDetailRewardRemindBatchApiResp();

        List<Long> orderIdList = new ArrayList<>();
        orderIdList.add(req.getOrderId());
        List<OrderInfoExtTemp> orderInfoTemps = super.returnOrderBaseExtInfo(orderIdList, req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return result;
        }
        OrderDetailRewardRemindBatchRqt beanOrderReminBatch = this.createBeanOrderReminBatch(req, orderInfoTemps);
        OrderDetailRewardBatchRemindResp orderDetailRewardBatchRemindResp = null;
        try {
            log.info(JSON.toJSONString(beanOrderReminBatch));
            orderDetailRewardBatchRemindResp = masterActivityBusinessServiceApi.orderDetailRewardRemindBatch(beanOrderReminBatch);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusException("获取奖励信息失败");
        }
        if (orderDetailRewardBatchRemindResp == null) {
            return result;
        }

        List<OrderDetailRewardRemindBatchApiResp.OrderDetailRewardItem> resultList = new ArrayList<>();

        List<OrderDetailRewardBatchRemindResp.NodeTaskInfo> nodeTaskInfoList = orderDetailRewardBatchRemindResp.getNodeTaskInfoList();

        if (CollectionUtils.isEmpty(nodeTaskInfoList)) {
            return result;
        }

        for (OrderDetailRewardBatchRemindResp.NodeTaskInfo nodeTaskInfo : nodeTaskInfoList) {
            OrderDetailRewardRemindBatchApiResp.OrderDetailRewardItem orderDetailRewardItem = new OrderDetailRewardRemindBatchApiResp.OrderDetailRewardItem();

            orderDetailRewardItem.setActivityId(nodeTaskInfo.getActivityId());
            orderDetailRewardItem.setRewardGiveNode(nodeTaskInfo.getRewardGiveNode());
            //判断是否过了领奖时间 报价、预约、签到
            String taskSymbolCn = MasterActivityTypeEnum.getActivityValue(nodeTaskInfo.getTaskSymbol());

            //任务是否完成
            orderDetailRewardItem.setTaskStatus(nodeTaskInfo.getCompleted() == 0 ? ActivityConstant.INCOMPLETE : ActivityConstant.COMPLETED);

            //奖励是否发放
            orderDetailRewardItem.setRewardStatus((nodeTaskInfo.getRewardGiveSuccess() == 0) ? ActivityConstant.UNCLAIMED : ActivityConstant.RECEIVED);
            if ((nodeTaskInfo.getRewardGiveQualificationState() != 0 && nodeTaskInfo.getRewardGiveQualificationState() != 2)
                    || !StringUtils.isEmpty(nodeTaskInfo.getRewardUpperEnum())) {
                orderDetailRewardItem.setRewardStatus(ActivityConstant.RECEIVED);
            }

            //节点信息
            orderDetailRewardItem.setTaskSymbol(nodeTaskInfo.getTaskSymbol());
            orderDetailRewardItem.setTaskSymbolExt(taskSymbolCn + "奖励");

            //判断当前任务节点是否完成
            orderDetailRewardItem.setIsReachedNode(this.returnNodeServeInfo(nodeTaskInfo, orderInfoTemps));

            //距离限制
            if (nodeTaskInfo.getOrderLimitDistance() != null && nodeTaskInfo.getOrderLimitDistance() > 0) {
                orderDetailRewardItem.setShowTitle(orderDetailRewardItem.getShowTitle() + String.format(ActivityConstant.DISTANCE_REWARD_TXT, (nodeTaskInfo.getOrderLimitDistance()), taskSymbolCn));
            }

            //如果是没有完成，并且是预约和签到节点
            if (nodeTaskInfo.getCompleted() == 0 && (
                    nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.RESERVE_CUSTOMER.getType()) ||
                            nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.SERVE_SIGN.getType()) ||
                            nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.MANUAL_RATE.getType())
            )) {
                orderDetailRewardItem.setShowTitle(this.returnShowTitleSignCheck(nodeTaskInfo));
            }


            //领奖校验失败
            Integer rgState = nodeTaskInfo.getRewardGiveQualificationState();
            if (rgState == ActivityConstant.REWARD_CHECK_THREE) {
                orderDetailRewardItem.setShowTitle("很遗憾，本单未获得" + taskSymbolCn + "奖励");
                orderDetailRewardItem.setIconType(IconTypeEnum.EXCEPTIONS.code);
                resultList.add(orderDetailRewardItem);
                continue;
            }

            OrderDetailRewardRemindResp orderDetailRewardRemindResp = new OrderDetailRewardRemindResp();
            BeanUtils.copyProperties(nodeTaskInfo, orderDetailRewardRemindResp);

            //待校验
            if (rgState == ActivityConstant.REWARD_CHECK_ONE) {
                orderDetailRewardItem.setShowTitle(this.getRewardUncheckTitle(orderDetailRewardRemindResp, taskSymbolCn, nodeTaskInfo));
                //奖励过期特殊处理
                if (nodeTaskInfo.getRewardGiveEndTime().before(new Date())) {
                    orderDetailRewardItem.setShowTitle(taskSymbolCn + "奖励已过期");
                    orderDetailRewardItem.setIconType(IconTypeEnum.EXCEPTIONS.code);
                    resultList.add(orderDetailRewardItem);
                    continue;
                }
                orderDetailRewardItem.setIconType(IconTypeEnum.QUALIFY.code);
                resultList.add(orderDetailRewardItem);
                continue;
            }
            //达到领奖条件，奖励发放校验
            if ((rgState == ActivityConstant.REWARD_CHECK_ZERO || rgState == ActivityConstant.REWARD_CHECK_TWO) && nodeTaskInfo.getCompleted() == 1) {
                orderDetailRewardItem.setIconType(IconTypeEnum.GET_REWARDS.code);
                //判断是否到领奖时间
                if (nodeTaskInfo.getRewardGiveStartTime().after(new Date())) {
                    String countDownTimeStr = MathUtils.getDistanceOfTwoDateStr(new Date(), nodeTaskInfo.getRewardGiveStartTime());
                    orderDetailRewardItem.setShowTitle(countDownTimeStr + "后奖励开始发放");
                    orderDetailRewardItem.setCountDownNumber((nodeTaskInfo.getRewardGiveStartTime().getTime() - System.currentTimeMillis()) / 1000);
                    orderDetailRewardItem.setIconType(IconTypeEnum.GET_REWARDS.code);
                    resultList.add(orderDetailRewardItem);
                    continue;
                }
                GiveRewardCheckTitleResp showTitleResp = this.getGiveRewardCheckTitle(orderDetailRewardRemindResp, taskSymbolCn);
                orderDetailRewardItem.setShowTitle(showTitleResp.getShowTitle());
                orderDetailRewardItem.setIconType(showTitleResp.getIconType());
                resultList.add(orderDetailRewardItem);
                continue;
            }

            if (orderDetailRewardItem.getShowTitle() == null
                    || (orderDetailRewardItem.getIsReachedNode() == 1 && orderDetailRewardItem.getTaskStatus().equals(ActivityConstant.INCOMPLETE))) {
                continue;
            }


            resultList.add(orderDetailRewardItem);
        }

        result.setLists(resultList);
        return result;
    }

    //判断当前节点是否完成，订单节点
    private Integer returnNodeServeInfo(OrderDetailRewardBatchRemindResp.NodeTaskInfo nodeTaskInfo, List<OrderInfoExtTemp> orderInfoTemps) {
        OrderInfoExtTemp orderInfoExtTemp = orderInfoTemps.get(0);
        if (nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.QUOTED.getType())) {
            //报价，预约截至时间不为空，标识已经报价
            if (orderInfoExtTemp.getAssignTime() != null) {
                return 1;
            }
        }

        if (nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.RESERVE_CUSTOMER.getType())) {
            //预约客户节点
            if (orderInfoExtTemp.getReservationEndTime() != null && orderInfoExtTemp.getReservationStartTime() != null) {
                return 1;
            }
        }

        if (nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.SERVE_SIGN.getType())) {
            //签到
            if (orderInfoExtTemp.getIsSignIn() != null && orderInfoExtTemp.getIsSignIn() == 1) {
                return 1;
            }
        }
        if (nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.MANUAL_RATE.getType())) {
            //如果已经评价，节点就算完成。
            if (null != nodeTaskInfo.getRateCompleted() && nodeTaskInfo.getRateCompleted() == 1) {
                return 1;
            }
            //好评
            if (orderInfoExtTemp.getOrderFinishTime() != null) {
                boolean flag = new Date().before(DateHelper.addSeconds(orderInfoExtTemp.getOrderFinishTime(), DateHelper.SEVEN_DAY_SECOND));
                if (!flag) {
                    return 1;
                }
            }
        }
        return 0;
    }

    private Integer returnRewardNodeInfo(String taskSymbol, Integer rateCompleted, OrderInfoExtTemp orderInfoExtTemp, String orderDetailType) {
        if (MasterActivityTypeEnum.QUOTED.getType().equals(taskSymbol)) {
            //报价，订单详情页查询则一定已报价
            if (OrderDetailTypeEnum.ORDER_DETAIL.type.equals(orderDetailType)) {
                return 1;
            }
        }
        if (MasterActivityTypeEnum.RESERVE_CUSTOMER.getType().equals(taskSymbol)) {
            //预约客户节点
            if (orderInfoExtTemp.getReservationEndTime() != null && orderInfoExtTemp.getReservationStartTime() != null) {
                return 1;
            }
        }
        if (MasterActivityTypeEnum.SERVE_SIGN.getType().equals(taskSymbol)) {
            //签到
            if (orderInfoExtTemp.getIsSignIn() != null && orderInfoExtTemp.getIsSignIn() == 1) {
                return 1;
            }
        }
        if (MasterActivityTypeEnum.MANUAL_RATE.getType().equals(taskSymbol)) {
            //如果已经评价，节点就算完成。
            if (null != rateCompleted && rateCompleted == 1) {
                return 1;
            }
            //好评
            if (orderInfoExtTemp.getOrderFinishTime() != null) {
                boolean flag = new Date().before(DateHelper.addSeconds(orderInfoExtTemp.getOrderFinishTime(), DateHelper.SEVEN_DAY_SECOND));
                if (!flag) {
                    return 1;
                }
            }
        }
        return 0;
    }

    /**
     * 上门操作详情，预约操作详情
     *
     * @param req
     */
    @Override
    public ReservationInfoApiResp reservationDetail(TocReservationInfoReq req) {
        ReservationInfoApiResp result = new ReservationInfoApiResp();

        List<Long> orderIdList = new ArrayList<>();
        orderIdList.add(req.getOrderId());
        //获取订单信息
        List<OrderInfoExtTemp> orderInfoTemps = super.returnOrderBaseExtInfo(orderIdList, req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return result;
        }

        Date nowDate = new Date();
        //过滤出不在免打扰时间段内的订单
        List<OrderInfoExtTemp> notFreeTimeRangeOrders = orderInfoTemps.stream().filter(f ->
                        (f.getFreeEndTime() != null && f.getFreeStartTime() != null && !(nowDate.after(f.getFreeStartTime()) && nowDate.before(f.getFreeEndTime()))))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notFreeTimeRangeOrders)) {
            return result;
        }
        OrderInfoExtTemp notFreeTimeRangeOrder = notFreeTimeRangeOrders.get(0);

        //获取人群信息
        List<Long> merchantCreateUserIdList = orderInfoTemps.stream().map(OrderInfoExtTemp::getCreateOrderUserId).collect(Collectors.toList());
        //获取商家人群信息
        Map<Long, List<Long>> merchantGroupIdMap = this.getMerchantGroupIdMap(merchantCreateUserIdList);

        com.wanshifu.iop.activity.domain.api.request.masterSignCheck.ReservationInfoReq reservationInfoReq
                = new com.wanshifu.iop.activity.domain.api.request.masterSignCheck.ReservationInfoReq();
        BeanUtils.copyProperties(notFreeTimeRangeOrder, reservationInfoReq);
        reservationInfoReq.setServeLevel1Id(Long.valueOf(notFreeTimeRangeOrder.getServeLevel1Ids()));

        if (!StringUtils.isEmpty(notFreeTimeRangeOrder.getServeIds())) {
            String[] strArr = notFreeTimeRangeOrder.getServeIds().split(",");
            List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
            reservationInfoReq.setServeIds(serveIdList);
        }
        reservationInfoReq.setAppointMethod(AppointMethodEnum.getTypeTxt(notFreeTimeRangeOrder.getAppointType()));

        reservationInfoReq.setUserCrowIds(merchantGroupIdMap.getOrDefault(notFreeTimeRangeOrder.getCreateOrderUserId(), new ArrayList<>()));

        reservationInfoReq.setOtherOrderFilterCondition(notFreeTimeRangeOrder.getPropertyList());
//        List<String> otherFilterCondition = new ArrayList<>();
//        //团队师傅
//        if (notFreeTimeRangeOrder.getIsOrderContract() == 1) {
//            otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_CONTRACT);
//        }
//
//        //订单包
//        if (notFreeTimeRangeOrder.getIsOrderPackage() == 1) {
//            otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_PACKAGE);
//        }
//
//        //师傅店铺
//        if (notFreeTimeRangeOrder.getIsOrderShop() == 1) {
//            otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_SHOP);
//        }
//        reservationInfoReq.setOtherOrderFilterCondition(otherFilterCondition);
        reservationInfoReq.setTaskSymbol(req.getTaskSymbol());
        reservationInfoReq.setMasterId(req.getUserId());
        try {
            log.info(JSON.toJSONString(reservationInfoReq));
            ReservationInfoResp reservationInfoResp = masterActivityBusinessServiceApi.reservationDetail(reservationInfoReq);
            if (reservationInfoResp == null) {
                return result;
            }
            result.setRewardTxt(ActivityUtil.filterTag(reservationInfoResp.getRewardTxt()));
            result.setFreeEndTime(this.returnTimeStamp(notFreeTimeRangeOrder.getFreeEndTime()));
            result.setFreeStartTime(this.returnTimeStamp(notFreeTimeRangeOrder.getFreeStartTime()));
            result.setReservationStartTime(this.returnTimeStamp(notFreeTimeRangeOrder.getReservationStartTime()));
            result.setReservationEndTime(this.returnTimeStamp(notFreeTimeRangeOrder.getReservationEndTime()));
            result.setCountDownNumber(reservationInfoResp.getCountDownNumber() == null ? -1 : reservationInfoResp.getCountDownNumber());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取操作详情标签异常");
            return result;
        }

    }

    /**
     * 验收列表标签展示
     *
     * @param req
     * @return
     */
    @Override
    public Map<String, OrderCheckListSomeTagApiResp> orderCheckListSomeTag(TocOrderCheckListSomeTagApiRqt req) {
        Map<String, OrderCheckListSomeTagApiResp> result = new HashMap<>();

        List<Long> orderIdList = req.getOrderIdList();
        if(CollectionUtils.isEmpty(orderIdList)){
            return result;
        }
        if(orderIdList.size() > 6){
            throw new BusException("订单不能超过6个");
        }

        List<OrderInfoExtTemp> orderInfoTemps = super.returnOrderBaseExtInfo(req.getOrderIdList(), req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return result;
        }
        OrderCheckListSomeTagRqt orderListRqt = new OrderCheckListSomeTagRqt();
        orderListRqt.setMasterId(req.getUserId());
        orderListRqt.setTaskSymbol(TaskTypeEnum.MANUAL_RATE.symbol);

        List<OrderCheckListSomeTagRqt.OrderIntoExtItem> orderInfoItemList = new ArrayList<>();
        for (OrderInfoExtTemp orderInfoTemp : orderInfoTemps) {
            OrderCheckListSomeTagRqt.OrderIntoExtItem masterOrderInfoBo = new OrderCheckListSomeTagRqt.OrderIntoExtItem();
            BeanUtils.copyProperties(orderInfoTemp, masterOrderInfoBo);
            masterOrderInfoBo.setServeLevel1Id(Long.valueOf(orderInfoTemp.getServeLevel1Ids()));

            //
            if (!StringUtils.isEmpty(orderInfoTemp.getServeIds())) {
                String[] strArr = orderInfoTemp.getServeIds().split(",");
                List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
                masterOrderInfoBo.setServeIds(serveIdList);
            }
            masterOrderInfoBo.setAppointMethod(AppointMethodEnum.getTypeTxt(orderInfoTemp.getAppointType()));

            masterOrderInfoBo.setOtherOrderFilterCondition(orderInfoTemp.getPropertyList());
//            List<String> otherFilterCondition = new ArrayList<>();
//            //团队师傅
//            if (orderInfoTemp.getIsOrderContract() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_CONTRACT);
//            }
//
//            //订单包
//            if (orderInfoTemp.getIsOrderPackage() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_PACKAGE);
//            }
//
//            //师傅店铺
//            if (orderInfoTemp.getIsOrderShop() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_SHOP);
//            }
//            masterOrderInfoBo.setOtherOrderFilterCondition(otherFilterCondition);
            orderInfoItemList.add(masterOrderInfoBo);

        }

        orderListRqt.setOrderList(orderInfoItemList);
        try {
            log.info("masterOrderListTag::" + JSON.toJSONString(orderListRqt));
            List<OrderCheckListSomeTagResp> orderCheckListSomeTag = masterActivityBusinessServiceApi.orderCheckListSomeTag(orderListRqt);
            if (CollectionUtils.isEmpty(orderCheckListSomeTag)) {
                return result;
            }
            orderCheckListSomeTag.forEach(orderCheckListSomeTagResp -> {
                OrderCheckListSomeTagApiResp offerOrderListResp = new OrderCheckListSomeTagApiResp();
                offerOrderListResp.setRewardTxt(orderCheckListSomeTagResp.getRewardTxt());
                result.put(orderCheckListSomeTagResp.getOrderId().toString(), offerOrderListResp);
            });
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("出现异常，请求参数={}", JSON.toJSONString(req));
        }
        return result;
    }

    /**
     * 验收详情标签展示
     *
     * @param req
     * @return
     */
    @Override
    public OrderCheckInfoDetailApiResp orderCheckInfoDetail(TocOrderCheckInfoDetailApiRqt req) {
        OrderCheckInfoDetailApiResp result = new OrderCheckInfoDetailApiResp();

        List<OrderInfoExtTemp> orderInfoTemps = super.returnOrderBaseExtInfo(Arrays.asList(req.getOrderId()), req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return result;
        }
        OrderCheckInfoDetailRqt orderCheckInfoDetailRqt = new OrderCheckInfoDetailRqt();
        orderCheckInfoDetailRqt.setMasterId(req.getUserId());

        OrderInfoExtTemp orderInfoTemp = orderInfoTemps.get(0);
        BeanUtils.copyProperties(orderInfoTemps.get(0), orderCheckInfoDetailRqt);
        orderCheckInfoDetailRqt.setServeLevel1Id(Long.valueOf(orderInfoTemp.getServeLevel1Ids()));

        //
        if (!StringUtils.isEmpty(orderInfoTemp.getServeIds())) {
            String[] strArr = orderInfoTemp.getServeIds().split(",");
            List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
            orderCheckInfoDetailRqt.setServeIds(serveIdList);
        }
        orderCheckInfoDetailRqt.setAppointMethod(AppointMethodEnum.getTypeTxt(orderInfoTemp.getAppointType()));

        orderCheckInfoDetailRqt.setOtherOrderFilterCondition(orderInfoTemp.getPropertyList());

//        List<String> otherFilterCondition = new ArrayList<>();
//        //团队师傅
//        if (orderInfoTemp.getIsOrderContract() == 1) {
//            otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_CONTRACT);
//        }
//
//        //订单包
//        if (orderInfoTemp.getIsOrderPackage() == 1) {
//            otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_PACKAGE);
//        }
//
//        //师傅店铺
//        if (orderInfoTemp.getIsOrderShop() == 1) {
//            otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_SHOP);
//        }
//        orderCheckInfoDetailRqt.setOtherOrderFilterCondition(otherFilterCondition);
        orderCheckInfoDetailRqt.setTaskSymbol(TaskTypeEnum.MANUAL_RATE.symbol);
        try {
            log.info("masterOrderListTag::" + JSON.toJSONString(orderCheckInfoDetailRqt));
            OrderCheckInfoDetailResp orderCheckInfoDetailResp = masterActivityBusinessServiceApi.orderCheckInfoDetail(orderCheckInfoDetailRqt);
            if (orderCheckInfoDetailResp == null) {
                return result;
            }
            result.setRewardTxt(orderCheckInfoDetailResp.getRewardTxt());

            return result;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("出现异常，请求参数={}", JSON.toJSONString(req));
        }
        return result;
    }

    private String getTaskSymbolExt(String taskSymbol, Integer countDownNumber, String taskStatus) {
        String taskSymbolExt = "";
        //如果已完成任务，则直接展示最简单的节点名称:已获得报价奖励资格，成交后可领奖,由前端拼接；xx:xx:xx内报价，也是由前端拼接
        Boolean simpleShowFlag = countDownNumber > 0 || BottomButtonEnum.COMPLETED.code.equals(taskStatus) ? true : false;
        if (TaskTypeEnum.MANUAL_RATE.symbol.equals(taskSymbol)) {
            taskSymbolExt = "被好评";
        } else if (TaskTypeEnum.RESERVE_CUSTOMER.symbol.equals(taskSymbol)) {
            taskSymbolExt = simpleShowFlag ? "预约" : "及时预约";
        } else if (TaskTypeEnum.SERVE_SIGN.symbol.equals(taskSymbol)) {
            taskSymbolExt = simpleShowFlag ? "签到" : "按时签到";
        } else if (TaskTypeEnum.QUOTED.symbol.equals(taskSymbol)) {
            taskSymbolExt = simpleShowFlag ? "报价" : "限时报价";
        } else {
            taskSymbolExt = TaskTypeEnum.getTaskTypeCn(taskSymbol);
        }
        return taskSymbolExt;
    }

    /**
     * 单个节点，除倒计时外统一取默认值
     *
     * @param taskSymbol
     * @param countDownNumber
     * @return
     */
    private String getSingleTaskSymbolExt(String taskSymbolExt, String taskSymbol, Integer countDownNumber) {
        if (TaskTypeEnum.RESERVE_CUSTOMER.symbol.equals(taskSymbol)) {
            taskSymbolExt = countDownNumber > 0 ? "预约" : "及时预约";
        } else if (TaskTypeEnum.SERVE_SIGN.symbol.equals(taskSymbol)) {
            taskSymbolExt = countDownNumber > 0 ? "签到" : "按时签到";
        } else if (TaskTypeEnum.QUOTED.symbol.equals(taskSymbol)) {
            taskSymbolExt = countDownNumber > 0 ? "报价" : "限时报价";
        }
        return taskSymbolExt;
    }

    @Override
    public Map<String, OfferOrderListV2Resp> orderListSomeTagV2(TocOrderListInfoListReq req) {
        Map<String, OfferOrderListV2Resp> result = new HashMap<>();
        List<TocOrderListInfoListReq.OrderInfo> orderInfoList = req.getOrderInfoList();
        if(CollectionUtils.isEmpty(orderInfoList)){
            return result;
        }
        if(orderInfoList.size() > 6){
            throw new BusException("订单不能超过6个");
        }
        List<Long> orderIdList = orderInfoList.stream().map(m -> m.getOrderId()).collect(Collectors.toList());
        //获取订单信息
        List<OrderInfoTemp> orderInfoTemps = super.returnOrderBaseInfo(orderIdList, req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return result;
        }
//        List<Long> merchantCreateUserIdList = orderInfoTemps.stream()
//                .filter(f -> "site".equals(f.getOrderFrom())).map(OrderInfoTemp::getCreateOrderUserId).collect(Collectors.toList());

        //只有商家订单才需要获取下单用户人群信息
//        Map<Long, List<Long>> merchantGroupIdMap = this.getMerchantGroupIdMap(merchantCreateUserIdList);

        OrderListRqt orderListRqt = new OrderListRqt();
        orderListRqt.setMasterId(req.getUserId());

        //获取订单师傅距离
//        List<BatchGetPushDistanceResp> batchGetPushDistanceResps = this.getPushDistance(req.getUserId(), orderIdList);

        //获取技能是否相关
//        Map<String, Integer> skillRelatedStateMap = this.getSkillRelatedState(req.getUserId(), orderIdList);

        List<MasterOrderInfoBo> orderInfoItemList = new ArrayList<>();
        for (OrderInfoTemp orderInfoTemp : orderInfoTemps) {
            MasterOrderInfoBo masterOrderInfoBo = new MasterOrderInfoBo();
            BeanUtils.copyProperties(orderInfoTemp, masterOrderInfoBo);
            masterOrderInfoBo.setServeLevel1Id(Long.valueOf(orderInfoTemp.getServeLevel1Ids()));
            masterOrderInfoBo.setUserId(orderInfoTemp.getCreateOrderUserId());
            if (!StringUtils.isEmpty(orderInfoTemp.getServeIds())) {
                String[] strArr = orderInfoTemp.getServeIds().split(",");
                List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
                masterOrderInfoBo.setServeIds(serveIdList);
            }
            masterOrderInfoBo.setAppointMethod(AppointMethodEnum.getTypeTxt(orderInfoTemp.getAppointType()));

//            masterOrderInfoBo.setUserCrowIds(merchantGroupIdMap.getOrDefault(orderInfoTemp.getCreateOrderUserId(), new ArrayList<>()));
            masterOrderInfoBo.setOtherOrderFilterCondition(orderInfoTemp.getPropertyList());

//            List<String> otherFilterCondition = new ArrayList<>();
//            //团队师傅
//            if (orderInfoTemp.getIsOrderContract() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_CONTRACT);
//            }
//
//            //订单包
//            if (orderInfoTemp.getIsOrderPackage() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_PACKAGE);
//            }
//
//            //师傅店铺
//            if (orderInfoTemp.getIsOrderShop() == 1) {
//                otherFilterCondition.add((MasterOrderInfoActivityListConstant.ORDER_SHOP));
//            }

            //订单距离
//            Optional<BatchGetPushDistanceResp> orderInfoExtTempOp = batchGetPushDistanceResps.stream().filter(f -> f.getOrderId().equals(masterOrderInfoBo.getOrderId())).findFirst();
//            orderInfoExtTempOp.ifPresent(batchGetPushDistanceResp -> masterOrderInfoBo.setDistanceValue(
//                    batchGetPushDistanceResp.getPushDistance() != null ? batchGetPushDistanceResp.getPushDistance().intValue() : 0));
//            masterOrderInfoBo.setOtherOrderFilterCondition(otherFilterCondition);

            //技能是否相关
//            masterOrderInfoBo.setSkillRelatedState(skillRelatedStateMap.get(String.valueOf(orderInfoTemp.getOrderId())));
            TocOrderListInfoListReq.OrderInfo orderInfo = orderInfoList.stream().filter(f -> f.getOrderId().equals(orderInfoTemp.getOrderId())).findFirst().orElseThrow(() -> new BusException("匹配订单信息异常：orderId=" + orderInfoTemp.getOrderId()));
            masterOrderInfoBo.setQueryRewardTotalFlag(Objects.isNull(orderInfo.getQueryRewardTotalFlag()) ? 0 : orderInfo.getQueryRewardTotalFlag());
            masterOrderInfoBo.setOrderAmount(Objects.isNull(orderInfo.getOrderAmount()) ? BigDecimal.ZERO : orderInfo.getOrderAmount());
            masterOrderInfoBo.setSinkQueryFlag(1);
            orderInfoItemList.add(masterOrderInfoBo);
        }
        orderListRqt.setOrderInfoItemList(orderInfoItemList);
        try {
            log.info("masterOrderListTag::" + JSON.toJSONString(orderListRqt));
            OrderListTagResp orderListTagResp = masterActivityBusinessServiceApi.masterOrderListTag(orderListRqt);
            if (orderListTagResp == null) {
                return result;
            }
            Map<String, OfferOrderListServiceResp> offerOrderListTagMap = orderListTagResp.getOfferOrderListTagMap();
            if (offerOrderListTagMap == null) {
                return result;
            }
            offerOrderListTagMap.forEach((k, v) -> {
                OfferOrderListV2Resp offerOrderListResp = new OfferOrderListV2Resp();
                offerOrderListResp.setOrderNo(v.getOrderNo());
                offerOrderListResp.setGoodsTag(v.getGoodsTag());
                offerOrderListResp.setActivityThemeLabel(v.getActivityThemeLabel());
                offerOrderListResp.setGoodsImgTagUrl(v.getGoodsImgTagUrl());
                offerOrderListResp.setGoodsTagType(v.getGoodsTagType());
                offerOrderListResp.setRewardTotalAmount(v.getRewardTotalAmount());
                offerOrderListResp.setTopTagType(v.getTopTagType());
                offerOrderListResp.setTopTagCountTime(v.getTopTagCountTime());
                offerOrderListResp.setLocalGetTime(v.getLocalGetTime());
                List<OfferOrderListServiceResp.OfferOrderListItem> tags = v.getTags();
                List<OfferOrderListV2Resp.OfferOrderListItem> offerOrderListItemList = new ArrayList<>();
                for (OfferOrderListServiceResp.OfferOrderListItem tag : tags) {
                    OfferOrderListV2Resp.OfferOrderListItem offerOrderListItem = new OfferOrderListV2Resp.OfferOrderListItem();
                    offerOrderListItem.setTagText(tag.getTagText());
                    if (TaskTypeEnum.MANUAL_RATE.symbol.equals(tag.getTaskSymbol())) {
                        offerOrderListItem.setTagText("服务获得" + tag.getTagText());
                    }
                    offerOrderListItem.setActivityId(tag.getActivityId());
                    offerOrderListItem.setTaskSymbol(tag.getTaskSymbol());
                    offerOrderListItem.setCountTime(tag.getCountTime() == null ? -1 : tag.getCountTime());
                    offerOrderListItemList.add(offerOrderListItem);
                }

                offerOrderListResp.setTags(offerOrderListItemList);
                result.put(k, offerOrderListResp);
            });
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("出现异常，请求参数={}", JSON.toJSONString(req));
        }
        return result;
    }

    /**
     * 报价/订单详情页-活动奖励进度模块接口
     */
    @Override
        public OrderDetailRewardProgressResp orderDetailRewardProgressModel(TocOrderRewardProgressReq req) {
        OrderDetailRewardProgressResp result = new OrderDetailRewardProgressResp();
        if (!OrderDetailTypeEnum.include(req.getOrderDetailType())) {
            log.error("详情页类型不符合要求，请联系管理员处理:orderDetailType=" + req.getOrderDetailType());
            return result;
        }
        List<Long> orderIdList = Arrays.asList(req.getOrderId());
        //获取订单信息
        OrderInfoExtTemp orderInfoTemp = new OrderInfoExtTemp();
        if (OrderDetailTypeEnum.ORDER_DETAIL.type.equals(req.getOrderDetailType())) {
            List<OrderInfoExtTemp> orderInfoTemps = super.returnOrderBaseExtInfo(orderIdList, req.getUserId());
            if (CollectionUtils.isEmpty(orderInfoTemps)) {
                return result;
            }
            orderInfoTemp = orderInfoTemps.get(0);
        } else {
            //报价
            List<OrderInfoTemp> orderInfoTemps = super.returnOrderBaseInfo(orderIdList, req.getUserId());
            if (CollectionUtils.isEmpty(orderInfoTemps)) {
                return result;
            }
            BeanUtils.copyProperties(orderInfoTemps.get(0), orderInfoTemp);
        }
//        List<Long> merchantCreateUserIdList = new ArrayList<>();
//        if ("site".equals(orderInfoTemp.getOrderFrom())) {
//            merchantCreateUserIdList.add(orderInfoTemp.getCreateOrderUserId());
//        }
        //只有商家订单才需要获取下单用户人群信息
//        Map<Long, List<Long>> merchantGroupIdMap = this.getMerchantGroupIdMap(merchantCreateUserIdList);
        OrderRewardProgressDetailRqt orderListRqt = new OrderRewardProgressDetailRqt();
//        //获取订单师傅距离
//        List<BatchGetPushDistanceResp> batchGetPushDistanceResps = this.getPushDistance(req.getUserId(), orderIdList);
//        //获取技能是否相关
//        Map<String, Integer> skillRelatedStateMap = this.getSkillRelatedState(req.getUserId(), orderIdList);
        BeanUtils.copyProperties(orderInfoTemp, orderListRqt);
        orderListRqt.setMasterId(req.getUserId());
        orderListRqt.setUserId(orderInfoTemp.getCreateOrderUserId());
        orderListRqt.setServeLevel1Id(com.wanshifu.framework.utils.StringUtils.isNotEmpty(orderInfoTemp.getServeLevel1Ids()) ? Long.valueOf(orderInfoTemp.getServeLevel1Ids()) : null);
        if (!StringUtils.isEmpty(orderInfoTemp.getServeIds())) {
            String[] strArr = orderInfoTemp.getServeIds().split(",");
            List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
            orderListRqt.setServeIds(serveIdList);
        }
        orderListRqt.setAppointMethod(AppointMethodEnum.getTypeTxt(orderInfoTemp.getAppointType()));
//        orderListRqt.setUserCrowIds(merchantGroupIdMap.getOrDefault(orderInfoTemp.getCreateOrderUserId(), new ArrayList<>()));
        orderListRqt.setOtherOrderFilterCondition(orderInfoTemp.getPropertyList());
        //订单距离
//        Optional<BatchGetPushDistanceResp> orderInfoExtTempOp = batchGetPushDistanceResps.stream().filter(f -> f.getOrderId().equals(orderListRqt.getOrderId())).findFirst();
//        orderInfoExtTempOp.ifPresent(batchGetPushDistanceResp -> orderListRqt.setDistanceValue(
//                batchGetPushDistanceResp.getPushDistance() != null ? batchGetPushDistanceResp.getPushDistance().intValue() : 0));
//        //技能是否相关
//        orderListRqt.setSkillRelatedState(skillRelatedStateMap.get(String.valueOf(orderInfoTemp.getOrderId())));
        //只有报价节点需要去查总额
        if (OrderDetailTypeEnum.QUOTED_DETAIL.type.equals(req.getOrderDetailType())) {
            orderListRqt.setQueryRewardTotalFlag(Objects.isNull(req.getQueryRewardTotalFlag()) ? 0 : req.getQueryRewardTotalFlag());
            orderListRqt.setOrderAmount(Objects.isNull(req.getOrderAmount()) ? BigDecimal.ZERO : req.getOrderAmount());
        }
        orderListRqt.setQuotedFlag(OrderDetailTypeEnum.QUOTED_DETAIL.type.equals(req.getOrderDetailType()) ? 1 : 0);
        orderListRqt.setSinkQueryFlag(1);
        try {
            log.info("masterOrderListTag::" + JSON.toJSONString(orderListRqt));
            OrderRewardProgressDetailResp orderProgressResp = masterActivityBusinessServiceApi.orderRewardProgressDetail(orderListRqt);
            if (orderProgressResp == null) {
                return result;
            }
            BeanUtils.copyProperties(orderProgressResp, result);
            List<OrderRewardProgressDetailResp.TaskInfoVo> taskList = orderProgressResp.getTaskList();
            if (CollectionUtils.isEmpty(taskList)) {
                return result;
            }
            List<OrderDetailRewardProgressResp.TaskInfoVo> resultTaskList = new ArrayList<>();
            OrderInfoExtTemp finalOrderInfoTemp = orderInfoTemp;
            for (int i = 0; i < taskList.size(); i++) {
                OrderRewardProgressDetailResp.TaskInfoVo taskInfoVo = taskList.get(i);
                OrderDetailRewardProgressResp.TaskInfoVo taskInfoVo1 = new OrderDetailRewardProgressResp.TaskInfoVo();
                BeanUtils.copyProperties(taskInfoVo, taskInfoVo1);
                taskInfoVo1.setIsReachedNode(this.returnRewardNodeInfo(taskInfoVo.getTaskSymbol(), taskInfoVo.getRateCompleted(), finalOrderInfoTemp, req.getOrderDetailType()));
                //去除已到达，但是任务未完成的节点：可能会有延迟，任务执行
                if (taskInfoVo1.getIsReachedNode() == 1 && BottomButtonEnum.INCOMPLETE.code.equals(taskInfoVo.getTaskStatus())) {
                    log.info("节点已到达，但是未完成：activityId:{},activityTaskId:{},taskSymbol:{}", taskInfoVo.getActivityId(), taskInfoVo.getActivityTaskId(), taskInfoVo.getTaskSymbol());
                    continue;
                }
                taskInfoVo1.setRewardGiveValue(taskInfoVo.getRewardValue());
                taskInfoVo1.setCountDownNumber(this.setCountDownNumber(req.getOrderDetailType(), taskInfoVo.getTaskSymbol(), taskInfoVo.getCountDownNumber()));
                taskInfoVo1.setTaskSymbolExt(this.getTaskSymbolExt(taskInfoVo.getTaskSymbol(), taskInfoVo1.getCountDownNumber(), taskInfoVo1.getTaskStatus()));
                //已领奖/待领奖状态，不管设置隐藏还是展示奖励数值，统一改为展示
                if (MasterRewardGiveButtonEnum.RECEIVED.code.equals(taskInfoVo.getRewardStatus()) || MasterRewardGiveButtonEnum.UNCLAIMED.code.equals(taskInfoVo.getRewardStatus())) {
                    taskInfoVo1.setIsShowGiveValue(1);
                }
                //此处奖励假如是2位小数截取成一位,结尾是0去掉0
                taskInfoVo1.setRewardGiveValue(MathUtils.keepOneDecimal(taskInfoVo1.getRewardGiveValue()));
                resultTaskList.add(taskInfoVo1);
            }
            //任务>=2，去掉被评价任务
            if (resultTaskList.size() >= 2) {
                resultTaskList = resultTaskList.stream().filter(f -> !TaskTypeEnum.MANUAL_RATE.symbol.equals(f.getTaskSymbol())).collect(Collectors.toList());
            }
            //只有一个节点情况，报价等节点默认展示：限时报价
            if (resultTaskList.size() == 1) {
                resultTaskList.forEach(taskVo -> taskVo.setTaskSymbolExt(this.getSingleTaskSymbolExt(taskVo.getTaskSymbolExt(), taskVo.getTaskSymbol(), taskVo.getCountDownNumber())));
            }

            result.setTaskList(resultTaskList);
            return result;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("出现异常，请求参数={}", JSON.toJSONString(req));
        }
        return result;
    }

    private Integer setCountDownNumber(String orderDetailType, String taskSymbol, Integer countDownNumber) {
        if (countDownNumber == null || countDownNumber <= 0) {
            return 0;
        }
        //报价页面除了报价节点其他都不展示倒计时
        if (OrderDetailTypeEnum.QUOTED_DETAIL.type.equals(orderDetailType)) {
            if (!TaskTypeEnum.QUOTED.symbol.equals(taskSymbol)) {
                return 0;
            }
        }
        return countDownNumber;
    }

    /**
     * 获取单个订单活动奖励总金额接口:只获取红包金额
     */
    @Override
    public OrderRewardTotalResp orderRewardTotalAmount(TocOrderRewardTotalReq req) {
        OrderRewardTotalResp resp = new OrderRewardTotalResp();
        resp.setRewardTotalAmount(BigDecimal.ZERO);
        if (req.getOrderAmount() == null && req.getOrderAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return resp;
        }
        //调用报价订单详情接口
        List<Long> orderIdList = Arrays.asList(req.getOrderId());
        List<OrderInfoTemp> orderInfoTemps = super.returnOrderBaseInfo(orderIdList, req.getUserId());
        if (CollectionUtils.isEmpty(orderInfoTemps)) {
            return resp;
        }
        OrderInfoTemp orderInfoTemp = orderInfoTemps.get(0);
//        List<Long> merchantCreateUserIdList = new ArrayList<>();
//        if ("site".equals(orderInfoTemp.getOrderFrom())) {
//        merchantCreateUserIdList.add(orderInfoTemp.getCreateOrderUserId());
//        }
        //报抢结合-报价时默认加标签
        if(Objects.nonNull(req.getIsDefiniteOffer()) && req.getIsDefiniteOffer() == 1){
            List<String> propertyList = orderInfoTemp.getPropertyList();
            if(Objects.isNull(propertyList)){
                propertyList = new ArrayList<>();
            }
            propertyList.add(OrderPropertyEnum.IS_ORDER_QUOTED_GRAB_QUOTED.code);
        }

        //只有商家订单才需要获取下单用户人群信息
//        Map<Long, List<Long>> merchantGroupIdMap = this.getMerchantGroupIdMap(merchantCreateUserIdList);
        OrderRewardTotalAmountRqt orderRewardTotalAmountRqt = new OrderRewardTotalAmountRqt();
        //获取订单师傅距离
//        List<BatchGetPushDistanceResp> batchGetPushDistanceResps = this.getPushDistance(req.getUserId(), orderIdList);
        //获取技能是否相关
//        Map<String, Integer> skillRelatedStateMap = this.getSkillRelatedState(req.getUserId(), orderIdList);
        BeanUtils.copyProperties(orderInfoTemp, orderRewardTotalAmountRqt);
        orderRewardTotalAmountRqt.setMasterId(req.getUserId());
        orderRewardTotalAmountRqt.setServeLevel1Id(Long.valueOf(orderInfoTemp.getServeLevel1Ids()));
        if (!StringUtils.isEmpty(orderInfoTemp.getServeIds())) {
            String[] strArr = orderInfoTemp.getServeIds().split(",");
            List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
            orderRewardTotalAmountRqt.setServeIds(serveIdList);
        }
        orderRewardTotalAmountRqt.setAppointMethod(AppointMethodEnum.getTypeTxt(orderInfoTemp.getAppointType()));

//        orderRewardTotalAmountRqt.setUserCrowIds(merchantGroupIdMap.getOrDefault(orderInfoTemp.getCreateOrderUserId(), new ArrayList<>()));
        orderRewardTotalAmountRqt.setOtherOrderFilterCondition(orderInfoTemp.getPropertyList());
        //订单距离
//        Optional<BatchGetPushDistanceResp> orderInfoExtTempOp = batchGetPushDistanceResps.stream().filter(f -> f.getOrderId().equals(orderRewardTotalAmountRqt.getOrderId())).findFirst();
//        orderInfoExtTempOp.ifPresent(batchGetPushDistanceResp -> orderRewardTotalAmountRqt.setDistanceValue(
//                batchGetPushDistanceResp.getPushDistance() != null ? batchGetPushDistanceResp.getPushDistance().intValue() : 0));
        //技能是否相关
//        orderRewardTotalAmountRqt.setSkillRelatedState(skillRelatedStateMap.get(String.valueOf(orderInfoTemp.getOrderId())));
        orderRewardTotalAmountRqt.setOrderAmount(Objects.isNull(req.getOrderAmount()) ? BigDecimal.ZERO : req.getOrderAmount());
        orderRewardTotalAmountRqt.setSinkQueryFlag(1);
        try {
            log.info("orderRewardTotalAmountRqt::" + JSON.toJSONString(orderRewardTotalAmountRqt));
            OrderRewardTotalAmountDetailResp orderDetailResp = masterActivityBusinessServiceApi.orderRewardTotalAmount(orderRewardTotalAmountRqt);
            if (orderDetailResp == null) {
                return resp;
            }
            resp.setRewardTotalAmount(orderDetailResp.getRewardTotalAmount());
            return resp;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("出现异常，请求参数={}", JSON.toJSONString(req));
        }
        return resp;
    }

    private Long returnTimeStamp(Date s) {
        if (s == null) {
            return 0L;
        }
        return s.getTime() / 1000;
    }

    /**
     * 返回预约指派节点title
     */
    private String returnShowTitleSignCheck(OrderDetailRewardBatchRemindResp.NodeTaskInfo nodeTaskInfo) {
        StringBuilder stringBuilder = new StringBuilder();
        if (nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.RESERVE_CUSTOMER.getType()) && nodeTaskInfo.getOfferLimitTime() != null) {
            //预约节点，并且预约限时不等于null
            stringBuilder.append(String.format(ActivityConstant.showTitle5, (nodeTaskInfo.getOfferLimitTime()) / 60));

        }

        if (nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.SERVE_SIGN.getType())) {
            //预约节点，并且预约限时不等于null
            stringBuilder.append(ActivityConstant.showTitle6);
        }
        //增加奖励发放节点
        //好评返现特殊处理
        if (nodeTaskInfo.getTaskSymbol().equals(MasterActivityTypeEnum.MANUAL_RATE.getType())) {
            //固定格式
            stringBuilder.append(String.format(ActivityConstant.showTitle8, MasterActivityTypeEnum.MANUAL_RATE.getTxt(), this.returnRewardExtInfo(nodeTaskInfo)));
            return stringBuilder.toString();
        }
        stringBuilder.append(MasterActivityRewardNodeEnum.getActivityValue(nodeTaskInfo.getRewardGiveNode()));
        stringBuilder.append("奖励<>").append(this.returnRewardExtInfo(nodeTaskInfo)).append("</>");
        //增加奖励金额信息
        if (nodeTaskInfo.getOrderLimitDistance() != null && nodeTaskInfo.getOrderLimitDistance() > 0) {
            stringBuilder.append(String.format(ActivityConstant.RESERVATION_INFO_3_LIST_TXT, nodeTaskInfo.getOrderLimitDistance()));
        }
        if (!StringUtils.isEmpty(nodeTaskInfo.getExpectReward())) {
            stringBuilder.append("，").append(nodeTaskInfo.getExpectReward()).append("。");
        }

        return stringBuilder.toString();
    }

    /**
     * 返回奖励值
     */
    private String returnGiveRewardValue(OrderDetailRewardBatchRemindResp.NodeTaskInfo nodeTaskInfo) {
        String frontRuleIndicatorObject = nodeTaskInfo.getFrontRuleIndicatorObject();
        BigDecimal rewardGiveValue = nodeTaskInfo.getRewardGiveValue();
        String rewardGiveType = nodeTaskInfo.getRewardGiveType();
        String rewardSymbol = nodeTaskInfo.getRewardSymbol();
        String result = null;
        if (StringUtils.isEmpty(frontRuleIndicatorObject)) {
            if (rewardSymbol.equals(RewardSymbol.INTEGRAL.type)) {
                result = rewardGiveValue.setScale(0, RoundingMode.DOWN).toString();
            } else {
                result = rewardGiveValue.toPlainString();
            }

        } else {
            TradeAmountRateBo tradeAmountRateBo = JSONObject.parseObject(frontRuleIndicatorObject, TradeAmountRateBo.class);
            if (tradeAmountRateBo != null) {
                if (tradeAmountRateBo.getTradeAmountRate() != null) {
                    result = tradeAmountRateBo.getTradeAmountRate().toString();
                } else {
                    if (tradeAmountRateBo.getTimelyAppointmentRate() != null) {
                        result = tradeAmountRateBo.getTimelyAppointmentRate().toString();
                    }
                }
            }
            if (rewardGiveType.equals(MasterRewardGiveTypeEnum.FIXED_VALUE.getType())) {
                result = nodeTaskInfo.getRewardGiveValue().toString();
            } else if (rewardGiveType.equals(MasterRewardGiveTypeEnum.RETURN_POINT.getType())) {
                //按照成交金额比例返积分
                result = nodeTaskInfo.getRewardGiveValue().toString();

            } else {
                result = nodeTaskInfo.getRuleResultValue().toString();

            }
        }
        return result == null ? "0" : result;
    }

    /**
     * 返回元  积分 %现金 %积分
     */
    private String returnRewardExtInfo(OrderDetailRewardBatchRemindResp.NodeTaskInfo nodeTaskInfo) {

        String rewardGiveValue = this.returnGiveRewardValue(nodeTaskInfo);

        if (nodeTaskInfo.getIsShowGiveValue() == 0) {
            if (nodeTaskInfo.getRewardSymbol().equals(RewardSymbol.BONUS.type)) {
                return RewardSymbol.BONUS.simpleCn;
            } else {
                return RewardSymbol.fromType(nodeTaskInfo.getRewardSymbol()).cn;
            }
        }

        //如果是固定值
        if (nodeTaskInfo.getRewardGiveType().equals(RewardGiveTypeEnum.FIXED_VALUE.type)) {
            if (nodeTaskInfo.getRewardSymbol().equals(RewardSymbol.BONUS.type)) {
                return rewardGiveValue + RewardSymbol.BONUS.simpleAlias;
            }
            return rewardGiveValue + RewardSymbol.fromType(nodeTaskInfo.getRewardSymbol()).cn;
        }
        if (nodeTaskInfo.getRewardGiveType().equals(RewardGiveTypeEnum.REWARD_GIVE_TYPE_ENUM.type)) {
            if (nodeTaskInfo.getRewardSymbol().equals(RewardSymbol.BONUS.type)) {
                return rewardGiveValue + nodeTaskInfo.getRuleConfigUnit() + RewardSymbol.BONUS.simpleCn;
            } else {
                return rewardGiveValue + nodeTaskInfo.getRuleConfigUnit() + RewardSymbol.fromType(nodeTaskInfo.getRewardSymbol()).cn;
            }
        }
        return "";
    }

    /**
     * 包装请求参数
     */
    private OrderDetailRewardRemindBatchRqt createBeanOrderReminBatch(TocOrderDetailRewardRemindBatchApiRqt req, List<OrderInfoExtTemp> orderInfoTemps) {

        OrderDetailRewardRemindBatchRqt orderDetailRewardRemindBatchRqt = new OrderDetailRewardRemindBatchRqt();
        OrderInfoItem orderInfoItem = new OrderInfoItem();

        List<Long> merchantCreateUserIdList = orderInfoTemps.stream().map(OrderInfoExtTemp::getCreateOrderUserId).collect(Collectors.toList());
        //获取商家人群信息
        Map<Long, List<Long>> merchantGroupIdMap = this.getMerchantGroupIdMap(merchantCreateUserIdList);

        for (OrderInfoExtTemp orderInfoTemp : orderInfoTemps) {
            BeanUtils.copyProperties(orderInfoTemp, orderInfoItem);
            orderInfoItem.setServeLevel1Id(com.wanshifu.framework.utils.StringUtils.isNotEmpty(orderInfoTemp.getServeLevel1Ids()) ? Long.valueOf(orderInfoTemp.getServeLevel1Ids()) : null);

            if (!StringUtils.isEmpty(orderInfoTemp.getServeIds())) {
                String[] strArr = orderInfoTemp.getServeIds().split(",");
                List<Long> serveIdList = Arrays.stream(strArr).map(Long::valueOf).collect(Collectors.toList());
                orderInfoItem.setServeIds(serveIdList);
            }
            orderInfoItem.setAppointMethod(AppointMethodEnum.getTypeTxt(orderInfoTemp.getAppointType()));

            orderInfoItem.setUserCrowIds(merchantGroupIdMap.getOrDefault(orderInfoTemp.getCreateOrderUserId(), new ArrayList<>()));

            orderInfoItem.setOtherOrderFilterCondition(orderInfoTemp.getPropertyList());

//            List<String> otherFilterCondition = new ArrayList<>();

            //团队师傅
//            if (orderInfoTemp.getIsOrderContract() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_CONTRACT);
//            }
//
//            //订单包
//            if (orderInfoTemp.getIsOrderPackage() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_PACKAGE);
//            }
//
//            //师傅店铺
//            if (orderInfoTemp.getIsOrderShop() == 1) {
//                otherFilterCondition.add(MasterOrderInfoActivityListConstant.ORDER_SHOP);
//            }
//            orderInfoItem.setOtherOrderFilterCondition(otherFilterCondition);
            orderDetailRewardRemindBatchRqt.setOrderNo(orderInfoItem.getOrderNo());
            orderDetailRewardRemindBatchRqt.setUserId(req.getUserId());
            //获取订单师傅距离
            List<BatchGetPushDistanceResp> batchGetPushDistanceResps = this.getPushDistance(req.getUserId(), Collections.singletonList(req.getOrderId()));
            if (!org.springframework.util.CollectionUtils.isEmpty(batchGetPushDistanceResps)) {
                orderInfoItem.setDistanceValue(batchGetPushDistanceResps.get(0).getPushDistance().intValue());
            } else {
                orderInfoItem.setDistanceValue(0);
            }
        }
        orderDetailRewardRemindBatchRqt.setOrderInfo(orderInfoItem);
        return orderDetailRewardRemindBatchRqt;
    }

    /**
     * 只有商家订单才需要获取下单用户人群信息
     */
    private Map<Long, List<Long>> getMerchantGroupIdMap(List<Long> merchantCreateUserIdList) {

        Map<Long, List<Long>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(merchantCreateUserIdList)) {
            return result;
        }

        return super.batchBigCrowdMap(merchantCreateUserIdList);
    }


}
