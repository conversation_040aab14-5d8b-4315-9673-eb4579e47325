package com.wanshifu.service;

import com.wanshifu.iop.activity.domain.api.response.GetActivityDetailResp;
import com.wanshifu.iop.inner.api.domains.request.GetActivityDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.response.GetActivityDetailApiResp;

/**
 * Service for handling activity detail operations
 * Extracted from ActivityServiceImpl to improve maintainability
 * 
 * <AUTHOR>
 */
public interface ActivityDetailService {

    /**
     * Build complete activity detail response
     * 
     * @param request the original request
     * @param activityDetail the fetched activity detail
     * @param isLogin whether user is logged in
     * @return complete activity detail response
     */
    GetActivityDetailApiResp buildActivityDetailResponse(
        GetActivityDetailApiRqt request, 
        GetActivityDetailResp activityDetail, 
        boolean isLogin
    );

    /**
     * Process activity base information
     * 
     * @param activityDetail source activity detail
     * @param request original request
     * @return processed base info
     */
    GetActivityDetailApiResp.ActivityBaseInfo processActivityBaseInfo(
        GetActivityDetailResp activityDetail, 
        GetActivityDetailApiRqt request
    );

    /**
     * Process landing page information including image URL conversion
     * 
     * @param activityDetail source activity detail
     * @param request original request
     * @return processed landing page info
     */
    GetActivityDetailApiResp.LandingPageInfo processLandingPageInfo(
        GetActivityDetailResp activityDetail, 
        GetActivityDetailApiRqt request
    );

    /**
     * Process activity task lists with reward calculations
     * 
     * @param activityDetail source activity detail
     * @param request original request
     * @param isLogin whether user is logged in
     * @return processed task lists
     */
    java.util.List<GetActivityDetailApiResp.ActivityTaskList> processActivityTaskLists(
        GetActivityDetailResp activityDetail, 
        GetActivityDetailApiRqt request, 
        boolean isLogin
    );
}
