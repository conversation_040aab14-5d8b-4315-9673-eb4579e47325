package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.request.master.*;
import com.wanshifu.iop.inner.api.domains.request.tocMaster.*;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.master.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/6/27 9:58
 */
public interface TocMasterActivityService {

    /**
     * 活动中心列表
     */
    MasterActivityListApiResp list(TocMasterActivityListApiRqt masterActivityListApiRqt);

    /**
     * 待预约列表
     */
    Map<String, ReservationListApiResp> reservationList(TocReservationListApiReq req);

    /**
     * 师傅奖励列表
     */
    SimplePageInfo<MasterRewardListApiResp> rewardList(TocMasterRewardListApiRqt masterRewardListApiRqt);

    /**
     * 抢单详情
     */
    GrabOrderButtonApiResp grabInfoBt(TocOfferInfoActivityListApiRqt offerInfoActivityListApiRqt);

    /**
     * 抢单列表
     */
    MasterOrderListResp orderListTag(TocOrderListApiRqt orderListApiRqt);

    /**
     * 完工详情页奖励提示
     */
    CompleteOrderApiResp completeReward(TocCompleteOrderApiRqt completeOrderApiRqt);

    /**
     * 订单详情奖励提醒
     *
     * @param orderDetailRewardRemindApiRqt
     * @return
     */
    OrderDetailRewardRemindApiResp orderDetailRewardRemind(TocOrderDetailRewardRemindApiRqt orderDetailRewardRemindApiRqt);

    /**
     * getAwardingNumAndUnLockNum
     *
     * @param masterAwardingNumApiRqt
     * @return
     */
    AwardingNumAndUnLockNumResp getAwardingNumAndUnLockNum(TocMasterAwardingNumApiRqt masterAwardingNumApiRqt);

    /**
     * 抢单列表
     */
    Map<String, OfferOrderListResp> orderListSomeTag(TocOrderListIdListReq req);


    /**
     * 订单详情奖励信息
     */
    OrderDetailRewardRemindBatchApiResp orderDetailRewardNotice(TocOrderDetailRewardRemindBatchApiRqt req);

    /**
     * 上门操作详情，预约操作详情
     */
    ReservationInfoApiResp reservationDetail(TocReservationInfoReq req);

    Map<String, OrderCheckListSomeTagApiResp> orderCheckListSomeTag(TocOrderCheckListSomeTagApiRqt rqt);

    OrderCheckInfoDetailApiResp orderCheckInfoDetail(TocOrderCheckInfoDetailApiRqt req);

    Map<String, OfferOrderListV2Resp> orderListSomeTagV2(TocOrderListInfoListReq req);

    OrderDetailRewardProgressResp orderDetailRewardProgressModel(TocOrderRewardProgressReq req);

    OrderRewardTotalResp orderRewardTotalAmount(TocOrderRewardTotalReq req);
}
