package com.wanshifu.service;

import com.wanshifu.base.address.domain.po.Address;
import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.request.landingPage.*;
import com.wanshifu.iop.inner.api.domains.request.master.GetDetailAggregationBannerApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryBarrageListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryTurntableLandingPageDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.response.CustomOrderListTagApiResp;
import com.wanshifu.iop.inner.api.domains.response.landingpage.*;

import com.wanshifu.iop.inner.api.domains.response.master.GetDetailAggregationBannerApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryBarrageListApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryTurntableLandingPageDetailApiResp;
import java.util.List;

public interface LandingPageService {

    /***
     * @Description：获取师傅有奖任务活动落地页详情
     * @param: landingPageRqt
     * @return：
     */
    MasterPrizeTaskApiResp getPrizeTasklandingPage(LandingPageRqt landingPageRqt);

    /***
     * @Description：获取匹配订单信息列表
     * @param: customOrderTagRqt
     * @return：
     */
    CustomOrderListTagApiResp customOrderListTag(CustomOrderTagRqt customOrderTagRqt);


    PrizeTaskPreResp getPrizeTaskPre(PrizeTaskPreRqt prizeTaskPreRqt);

    /***
     * @Description：根据活动id返回活动模型
     * @param: activityDetailApiRqt
     * @return：
     */
    String getActivityModelById(ActivityDetailApiRqt activityDetailApiRqt);

    /**
     * 获取师傅订单活动落地页详情
     */
    MasterOrderTaskApiResp getMasterOrderTaskLandingPage(MasterOrderLandingApiPageRqt masterOrderLandingPageRqt);

    /**
     * 获取商家有奖活动落地页详情
     */
    MetchantPrizeTaskApiResp getMerchantPrizeTaskLandingPage(MetchantPrizeLandingPageRqt metchantPrizeLandingPageRqt);

    /**
     * 获取商家充值活动专用落地页详情
     */
    MerchantRechargeTaskApiResp getMerchantRechargeTaskLandingPage(MerchantRechargeTaskLandingPageRqt merchantRechargeTaskLandingPageRqt);

    /**
     * 获取商家多状态广告位
     */
    MerchantActivityDetailAdApiResp getMerchantActivityDetailAd(GetActivityDetailApiRqt getActivityDetailRqt);

    /**
     * 获取邀请人详情落地页
     */
    MasterInviterTaskDetailApiResp getInviterTaskLandingPageDetail(MasterInviterTaskDetailApiRqt masterInviterTaskDetailApiRqt);

    /**
     * 获取邀请人详情落地页--我的奖励
     */
    MasterInviterTaskMyRewardApiResp getInviterRewardInfoTaskPage(MasterInviterTaskDetailMyRewardApiRqt masterInviterTaskDetailApiRqt);

    /**
     * 获取被邀请人落地页详情（应用外）
     */
    MasterInviteeTaskOutDetailApiResp getInviteeTaskOutLandingPageDetail(MasterInviteeTaskOutDetailApiRqt masterInviteeTaskOutDetailApiRqt);

    /**
     * 获取被邀请人落地页详情（应用内）
     */
    MasterInviteeTaskInDetailApiResp getInviteeTaskInLandingPageDetail(MasterInviteeTaskInDetailApiRqt masterInviteeTaskInDetailApiRqt);

    /**
     * 师傅邀请活动  集合页
     */
    MasterInviteTaskCollectPageDetailApiResp getInviteTaskCollectPageDetail(MasterInviteTaskCollectPageDetailApiRqt masterInviteTaskCollectPageDetailApiRqt);

    /**
     * 师傅邀请活动  集合页--我的奖励
     */
    MasterInviteTaskCollectPageMyRwardInfoApiResp getInviteTaskCollectPageMyRewardInfo(MasterInviteTaskCollectPageMyRwardInfoApiRqt masterInviteTaskCollectPageMyRwardInfoApiRqt);

    /**
     * 发送短信验证码
     */
    boolean sendMessageCode(SendMessageCodeRqt sendMessageCodeRqt);

    /**
     * 保存手机并入库
     */
    Integer checkPhoneAndSave(CheckPhoneAndSaveRqt checkPhoneAndSaveRqt);

    /**
     * 获取隐私政策
     *
     * @return
     */
    GetCurrentAgreementInfoApiResp getPrivacyPolicy();

    /**
     * 获取入驻进度
     *
     * @return
     */
    GetAccountStatusApiResp getSettledState(GetSettledStateRqt getSettledStateRqt);

    /**
     * 接口邀请并注册生成绑定关系
     *
     * @return
     */
    boolean setPhoneInsert(SetPhoneInsertApiRqt setPhoneInsertRqt);

    /**
     * 获取三四级地址缓存
     *
     * @param addressIdList
     * @return
     */
    List<Address> getThreeAndFourAddressListFromRedis(List<Long> addressIdList);

//    /**
//     * 获取活动单个任务订单限制
//     * @param getActivityTaskOrderLimitApiRqt
//     * @return
//     */
//    GetActivityTaskOrderLimitApiResp getActivityTaskOrderLimitInfo(GetActivityTaskOrderLimitApiRqt getActivityTaskOrderLimitApiRqt);

    /**
     * 抽奖专用落地页详情
     * @param rqt
     * @return
     */
    LotteryTurntableLandingPageDetailApiResp getLotteryTurntableLandingPageDetail(LotteryTurntableLandingPageDetailApiRqt rqt);

    /**
     * 抽奖专用弹幕
     * @param rqt
     * @return
     */
    List<LotteryBarrageListApiResp>  getLotteryBarrageList(LotteryBarrageListApiRqt rqt);

    MerchantActivityDetailBannerAdApiResp getMerchantActivityDetailBannerAd(GetActivityDetailBannerApiRqt getActivityDetailRqt);

    GetDetailAggregationBannerApiResp getDetailAggregationBanner(GetDetailAggregationBannerApiRqt rqt);

    MerchantActivityDetailMultiStatusAdApiResp getMerchantActivityDetailMultiStatusAd(MerchantActivityDetailMultiStatusAdApiRqt getActivityDetailRqt);
}
