package com.wanshifu.service;

import com.wanshifu.iop.inner.api.domains.request.GetAdSwitchRqt;
import com.wanshifu.iop.inner.api.domains.request.UpdateAdSwitchRqt;
import com.wanshifu.iop.inner.api.domains.request.ad.SaveUnInterestAdRqt;
import com.wanshifu.iop.inner.api.domains.response.GetAdSwitchResp;

public interface AdCommonService {
    Integer saveUnInterestAd(SaveUnInterestAdRqt rqt);

    Integer updatePersonalizationAdSwitch(UpdateAdSwitchRqt rqt);

    GetAdSwitchResp getPersonalizationAdSwitch(GetAdSwitchRqt rqt);
}
