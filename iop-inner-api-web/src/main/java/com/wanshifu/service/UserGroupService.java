package com.wanshifu.service;

import java.util.List;

/**
 * Service for handling user group and persona operations
 * Extracted from ActivityServiceImpl to improve separation of concerns
 * 
 * <AUTHOR>
 */
public interface UserGroupService {

    /**
     * Get user group IDs for a specific user and persona
     * 
     * @param userId user ID
     * @param personaId persona ID
     * @return list of group IDs
     */
    List<Long> getUserGroupIds(String userId, String personaId);

    /**
     * Get tag list from big data for a user
     * 
     * @param userClass user class type
     * @param userId user ID
     * @return list of tags
     */
    List<String> getTagListFromBigData(String userClass, Long userId);

    /**
     * Get persona ID based on user class and launch port
     * 
     * @param userClass user class type
     * @param launchPort launch port
     * @return persona ID
     */
    String getPersonaId(String userClass, String launchPort);

    /**
     * Convert user class to persona ID
     * 
     * @param userClass user class type
     * @return persona ID
     */
    String convertUserClassToPersonaId(String userClass);
}
