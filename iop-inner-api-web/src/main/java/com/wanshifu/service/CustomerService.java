package com.wanshifu.service;

import com.wanshifu.framework.core.page.SimplePageInfo;
import com.wanshifu.iop.activity.domain.api.response.out.GetSeckillTagResp;
import com.wanshifu.iop.inner.api.domains.request.ReceiveVoucherV2Rqt;
import com.wanshifu.iop.inner.api.domains.request.customer.*;
import com.wanshifu.iop.inner.api.domains.response.ReceiveVoucherResp;
import com.wanshifu.iop.inner.api.domains.response.customer.*;

/**
 * @author: kexianyun
 * @time:2023/7/22 0022 11:33
 */
public interface CustomerService {

    OrderRewardInviteeLandingPageApiResp inviteeLandingPage(OrderRewardInviteeLandingPageApiReq req, String token);

    ShareLinkApiResp shareLink(ShareLinkApiReq req);

    OrderRewardInviterLandingPageApiResp inviterLandingPage(OrderRewardInviterLandingPageApiReq req, String token);

    SimplePageInfo<OrderRewardInviterOrderListApiResp> orderList(OrderRewardInviterOrderListApiReq req);

    OrderRewardNumResp rewardNum(RewardNumReq req);

    /**
     * 获取秒杀活动标签信息
     * @param rqt
     * @return
     */
    GetSeckillTagResp getSeckillTag(GetSeckillTagInnerRqt rqt);

    /**
     * 会员权益领券
     * @param rqt
     * @return
     */
    ReceiveVoucherResp receiveVipVoucher(ReceiveVoucherV2Rqt rqt);

    GetCustomerActivityTagApiResp getCustomerActivityTag(GetCustomerActivityTagInnerRqt rqt);

    CreateTeamBuyCheckOutApiResp createTeamBuyCheckForOut(CreateTeamBuyCheckOutApiRqt rqt);
}
