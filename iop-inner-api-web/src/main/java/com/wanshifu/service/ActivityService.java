package com.wanshifu.service;

import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.merchant.GetPayPageVoucherListApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MatchOrderActivityDetailApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MatchUserOrderListTagApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MerchantPublishOrderTagApiResp;

import java.util.List;
import java.util.Map;

public interface ActivityService {

    /***
     * @Description：获取活动落地页详情
     * @param: getActivityDetailApiRqt
     * @param: token
     * @param: tokenApp
     * @return：
     */
    GetActivityDetailApiResp getActivityDetail(GetActivityDetailApiRqt getActivityDetailApiRqt);


    /***
     * @Description：优惠活动列表
     * @param: getActivityListApiRqt
     * @param: token
     * @param: tokenApp
     * @return：
     */
    List<GetActivityListApiResp> getList(GetActivityListApiRqt getActivityListApiRqt);

    /***
     * @Description：弹窗结果回传
     * @param: backDialogAdRqt
     * @param: token
     * @param: tokenApp
     * @return：
     */
    Integer backDialogAd(BackDialogAdRqt backDialogAdRqt);

    /**
     * 用户活动报名
     */
    Integer apply(ApplyActivityApiRqt applyActivityApiRqt);

    /**
     * 用户活动下发
     */
    Integer issue(IssueApiRqt issueApiRqt);

    /**
     * 用户浏览落地页详情
     */
    Integer actiLandingclickCollect(EnterActivityDetailRqt enterActivityDetailRqt);

    /**
     * 获取用户弹窗广告
     */
    GetUserAdResp getDialogAd(GetUserPopAdRqt getUserPopAdRqt);

    /**
     * 获取用户广告（非弹窗）
     */
    GetUserAdResp getAdPosition(GetUserAdRqt getUserAdRqt, String token, String tokenApp, String signature);

    /**
     * 批量获取广告位广告，非弹窗、非开屏广告
     */
    Map<String, GetUserAdResp> getAdPositionBatch(GetUserAdBatchRqt getUserAdBatchRqt);

    /**
     * 用户活动任务奖励领取
     *
     * @return
     */
    List<RewardGiveApiResp> taskGiveReward(ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt);

    List<GetUserActivityTaskShortApiResp> getUserActivityTaskShort(GetActivityDetailApiRqt getActivityDetailApiRqt);

    /**
     * 商家充值页面优惠活动信息
     *
     * @param getRechargeDiscountActivityApiRqt
     * @return
     */
    GetRechargeDiscountActivityApiResp getRechargeDiscountInfo(GetRechargeDiscountActivityApiRqt getRechargeDiscountActivityApiRqt);

    /**
     * 商家充值页面优惠活动信息
     *
     * @param matchUserOrderListTagRqt
     * @return
     */
    MatchUserOrderListTagApiResp matchUserOrderListTag(MatchUserOrderListTagRqt matchUserOrderListTagRqt);

    /**
     * 商家订单详情
     *
     * @param matchOrderActivityDetailRqt
     * @return
     */
    MatchOrderActivityDetailApiResp matchOrderActivityDetail(MatchOrderActivityDetailApiRqt matchOrderActivityDetailRqt);

    /**
     * 单个活动领奖励取
     *
     * @param activityTaskGiveRewardApiRqt
     * @return
     */
    RewardTypeApiResp singleTaskGiveReward(ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt);

    /**
     * 商家发布订单活动标签接口
     *
     * @param merchantPublishOrderTagApiRqt
     * @return
     */
    MerchantPublishOrderTagApiResp getPublishOrderTag(MerchantPublishOrderTagApiRqt merchantPublishOrderTagApiRqt);

    /**
     * 根据实验id查询是否在使用
     */
    List<IsUsedByTryIdResp> isUsedByTryId(IsUsedByTryIdRqt isUsedByTryIdRqt);

    /**
     * 用户多选一活动报名
     * @param activityApplyApiChoiceRqt
     * @return
     */
    Integer applyChoice(ActivityApplyApiChoiceRqt activityApplyApiChoiceRqt);

    Integer applyChoiceNew(ActivityApplyApiChoiceOfRqt activityApplyApiChoiceRqt);

    /**
     * 家庭用户 活动报名接口--广告使用
     * @param activityApplyCustomerRqt
     * @return
     */
    Integer applyCustomer(ActivityApplyCustomerApiRqt activityApplyCustomerRqt);

    List<RewardGiveApiResp> taskGiveRewardOf(ActivityTaskGiveRewardApiOfRqt activityTaskGiveRewardApiRqt);

    /**
     * 列表广告
     * @param getUserAdRqt
     * @param token
     * @param tokenApp
     * @param signature
     * @return
     */
    GetUserAdResp getListAd(GetUserAdRqt getUserAdRqt, String token, String tokenApp, String signature);

    List<RewardGiveBannerAdApiResp> taskGiveRewardForMultiBannerAd(RewardGiveBannerAdApiRqt rewardGiveBannerAdApiRqt);

    /**
     * 获取商家充值活动的优惠券列表
     * @param getPayPageVoucherListApiRqt
     * @return
     */
    GetPayPageVoucherListApiResp getPayPageVoucherList(GetPayPageVoucherListApiRqt getPayPageVoucherListApiRqt);

    /**
     * 获取家庭弹窗广告
     * @param customerAdRqt
     * @return
     */
    CustomerAdResp listCustomerPopAd(CustomerAdRqt customerAdRqt, String token);

    /**
     * 获取家庭广告位广告(非弹窗)
     * @param customerAdRqt
     * @return
     */
    CustomerAdResp listCustomerAdPositionAd(CustomerAdRqt customerAdRqt, String token);

    /**
     * 家庭多状态广告点击记录接口
     * @param customerAdClickCollectRqt
     * @return
     */
    Integer customerAdClickCollect(CustomerAdClickCollectRqt customerAdClickCollectRqt);

    /**
     * 获取是否有用户已报名的进行中且未达任何上限的充值活动
     * @param rqt
     * @return
     */
    GetUserRechargeActivityResp getUserRechargeActivity(GetUserRechargeActivityRqt rqt);

    /**
     * 充值成功页获取用户的充值任务的奖励
     * @param rqt
     * @return
     */
    GetRechargeRewardResp getRechargeRewardList(GetRechargeRewardRqt rqt);

    /**
     * 钱包页面获取用户待领取的充值任务的多选一奖励
     * @param rqt
     * @return
     */
    RechargeChooseCombineResp getRechargeChooseCombineRewardInfo(RechargeChooseCombineRqt rqt);

    /**
     * 执行浏览指定页面任务
     * @param rqt
     * @return
     */
    BrowsePageResp runBrowsePageTask(BrowsePageRqt rqt);

    CustomerAdResp batchGetCustomerAdPosition(BatchGetCustomerAdPositionApiRqt customerAdRqt, String token);
    /**
     * 免费领券 - 领取优惠券
     */
    ReceiveVoucherResp receiveVoucher(ReceiveVoucherRqt rqt, String token);

    /**
     * 领取优惠券V2(免费领券,会员权益领券)
     */
    ReceiveVoucherResp receiveVoucherV2(ReceiveVoucherV2Rqt rqt, String token);

    /**
     * 获取家庭服务详情标签优惠券列表接口
     * @param rqt
     * @param token
     * @return
     */
    List<GetCustomerServeTagVoucherListApiResp> getCustomerServeTagVoucherList(GetCustomerServeTagVoucherListApiRqt rqt);
}
