package com.wanshifu.service;

import com.wanshifu.bo.VoucherRewardBo;
import com.wanshifu.iop.activity.domain.api.response.GetActivityDetailResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Service for handling voucher and reward-related operations
 * Extracted from ActivityServiceImpl to improve separation of concerns
 * 
 * <AUTHOR>
 */
public interface VoucherRewardService {

    /**
     * Batch query voucher reward values
     * 
     * @param activityTaskList list of activity tasks
     * @param userClass user class type
     * @return map of reward extra ID to voucher reward info
     */
    Map<Long, VoucherRewardBo> batchGetVoucherRewardValue(
        List<GetActivityDetailResp.ActivityTaskBo> activityTaskList, 
        String userClass
    );

    /**
     * Get reward value for a specific reward
     * 
     * @param rewardExtraId reward extra ID
     * @param rewardGiveValue default reward value
     * @param rewardSymbol reward symbol type
     * @return calculated reward value
     */
    BigDecimal getRewardValue(Long rewardExtraId, BigDecimal rewardGiveValue, String rewardSymbol);

    /**
     * Calculate voucher reward value based on type
     * 
     * @param rewardExtraId voucher ID
     * @param rewardSymbol voucher type symbol
     * @return calculated reward value
     */
    BigDecimal calculateVoucherRewardValue(Long rewardExtraId, String rewardSymbol);

    /**
     * Calculate voucher pack total amount
     * 
     * @param voucherPackId voucher pack ID
     * @return total amount of voucher pack
     */
    BigDecimal calculateVoucherPackTotalAmount(Long voucherPackId);
}
