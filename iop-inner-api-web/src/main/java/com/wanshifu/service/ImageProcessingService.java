package com.wanshifu.service;

import java.util.List;
import java.util.Map;

/**
 * Service for handling image processing operations
 * Extracted from ActivityServiceImpl to improve separation of concerns
 * 
 * <AUTHOR>
 */
public interface ImageProcessingService {

    /**
     * Convert image AIDs to URLs in batch
     * 
     * @param imageAids list of image AIDs
     * @return map of AID to URL
     */
    Map<Long, String> convertAidsToUrls(List<String> imageAids);

    /**
     * Filter valid image AIDs (non-null and non-zero)
     * 
     * @param imageAidList list of image AIDs
     * @return filtered list of valid AIDs as strings
     */
    List<String> filterValidImageAids(List<Long> imageAidList);

    /**
     * Collect all image AIDs from various sources
     * 
     * @param sources various objects containing image AIDs
     * @return consolidated list of image AIDs
     */
    List<Long> collectImageAids(Object... sources);
}
