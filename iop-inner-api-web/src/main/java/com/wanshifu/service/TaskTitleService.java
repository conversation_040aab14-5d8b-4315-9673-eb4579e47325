package com.wanshifu.service;

import com.wanshifu.iop.activity.domain.bo.ActivityTaskBeanBo;
import com.wanshifu.iop.activity.domain.po.UserActivityTask;

/**
 * Service for handling task title and description generation
 * Extracted from ActivityServiceImpl to improve maintainability
 * 
 * <AUTHOR>
 */
public interface TaskTitleService {

    /**
     * Generate task title for logged-in users
     * 
     * @param taskSymbol the task symbol
     * @param userActivityTask the user activity task
     * @return formatted task title
     */
    String generateTaskTitle(String taskSymbol, UserActivityTask userActivityTask);

    /**
     * Generate task title for non-logged-in users
     * 
     * @param taskSymbol the task symbol
     * @return formatted task title
     */
    String generateTaskTitleForAnonymous(String taskSymbol);

    /**
     * Generate task description based on execution type
     * 
     * @param activityTask the activity task configuration
     * @return formatted task description
     */
    String generateTaskDescription(ActivityTaskBeanBo activityTask);

    /**
     * Calculate remaining seconds for task completion
     * 
     * @param activityEndTime activity end time
     * @param timeLimit task time limit
     * @param applyTime task apply time
     * @return remaining seconds
     */
    Long calculateRemainingSeconds(java.util.Date activityEndTime, Long timeLimit, java.util.Date applyTime);
}
