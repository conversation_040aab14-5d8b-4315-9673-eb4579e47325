package com.wanshifu.configuration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.wanshifu.bo.BaseDateBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Arrays;

/**
 * 已废弃，跨域配置
 * @see WsfCrossOriginFilter
 * <AUTHOR>
 */
@Component
@Slf4j
public class WsfCrossOriginFilter extends CorsFilter {

    @Value("${wanshifu.ios.enable}")
    private Boolean iosEnable;
    String WSF_DEVICE_ID = "wsf-device-id";
    public WsfCrossOriginFilter() {
        super(configurationSource());
    }

    private static UrlBasedCorsConfigurationSource configurationSource() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.setMaxAge(86400L);
        corsConfiguration.setAllowCredentials(true);
        UrlBasedCorsConfigurationSource urlBasedCorsConfigurationSource = new UrlBasedCorsConfigurationSource();
        urlBasedCorsConfigurationSource.registerCorsConfiguration("/**", corsConfiguration);
        return urlBasedCorsConfigurationSource;
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String origin = request.getHeader("Origin");
        response.addHeader("Access-Control-Allow-Origin", StringUtils.isNoneBlank(origin) ? origin : "*");
        response.addHeader("Access-Control-Allow-Credentials", "true");
        response.addHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
        response.addHeader("Access-Control-Max-Age", "3600");
        // 浏览器是会先发一次options请求，如果请求通过，则继续发送正式的post请求
        response.setHeader("Access-Control-Allow-Headers",
                "Content-Type,XFILENAME,XFILECATEGORY,XFILESIZE,x-requested-with," +
                        "sessionId,sig,token,scene,appToken,sec-ch-ua,sec-ch-ua-mobile," +
                        "sec-ch-ua-platform,appClient,appVersion,appsystem,authorization," +
                        "Authorization,Origin,Accept,X-Requested-With,Access-Control-Request-Method," +
                        "Access-Control-Request-Headers,credentials,phoneType,versionCode," +
                        "signature,apkChannel,type,cache-control,deviceid,distinct-id,end-from," +
                        "sec-fetch-dest,sec-fetch-mode,sec-fetch-site,wsf-device-id,user-agent," +
                        "phone_model,referer,system_version,version");

        //        response.setHeader("Access-Control-Allow-Headers", "*");
        // 配置options的请求返回
        if (request.getMethod().equals("OPTIONS")) {
            response.setStatus(HttpStatus.SC_OK);
            response.getWriter().write("OPTIONS returns OK");
            return;
        }

        if(!iosEnable){
            if(!org.springframework.util.StringUtils.isEmpty(request.getHeader("appClient")) && "ios".equals(request.getHeader("appClient"))){
                response.setStatus(HttpStatus.SC_OK);
                BaseDateBo baseDateBo = new BaseDateBo();
                baseDateBo.setCode("success");
                baseDateBo.setMsg("");
                baseDateBo.setStatus("1");
                response.getWriter().write(JSON.toJSONString(baseDateBo, SerializerFeature.WriteMapNullValue));
                response.setContentType("application/json;charset=utf-8");

                return;
            }
        }

        filterChain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
