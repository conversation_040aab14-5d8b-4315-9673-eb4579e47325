package com.wanshifu.service.impl;

import com.wanshifu.iop.activity.domain.api.response.GetActivityDetailResp;
import com.wanshifu.iop.inner.api.domains.request.GetActivityDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.response.GetActivityDetailApiResp;
import com.wanshifu.service.ActivityDetailService;
import com.wanshifu.spring.cloud.fegin.component.ApiAccessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ActivityServiceImpl
 * Tests the refactored methods to ensure functionality is preserved
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ActivityServiceImplTest {

    @Mock
    private ActivityDetailService activityDetailService;

    @InjectMocks
    private ActivityServiceImpl activityService;

    private GetActivityDetailApiRqt request;
    private GetActivityDetailResp activityDetail;
    private GetActivityDetailApiResp expectedResponse;

    @BeforeEach
    void setUp() {
        // Set up test data
        request = new GetActivityDetailApiRqt();
        request.setActivityId(1L);
        request.setUserId("123");
        request.setUserClass("merchant");
        request.setIsWeb(1);

        activityDetail = new GetActivityDetailResp();
        GetActivityDetailResp.ActivityBaseInfo baseInfo = new GetActivityDetailResp.ActivityBaseInfo();
        baseInfo.setActivityState("launching");
        activityDetail.setActivityBaseInfo(baseInfo);

        expectedResponse = new GetActivityDetailApiResp();
    }

    @Test
    void testGetActivityDetail_Success() {
        // Given
        when(activityDetailService.buildActivityDetailResponse(any(), any(), anyBoolean()))
                .thenReturn(expectedResponse);

        // When
        GetActivityDetailApiResp result = activityService.getActivityDetail(request);

        // Then
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        verify(activityDetailService).buildActivityDetailResponse(eq(request), any(), eq(true));
    }

    @Test
    void testGetActivityDetail_AppNotEnabled() {
        // Given
        request.setTokenApp("test-token");
        // Assuming appEnable is false by default

        // When
        GetActivityDetailApiResp result = activityService.getActivityDetail(request);

        // Then
        assertNull(result);
        verify(activityDetailService, never()).buildActivityDetailResponse(any(), any(), anyBoolean());
    }

    @Test
    void testIsAppAccessAllowed_WithAppToken() {
        // Given
        String tokenApp = "test-token";

        // When
        boolean result = activityService.isAppAccessAllowed(tokenApp);

        // Then
        // This will depend on the appEnable configuration
        // For this test, we assume appEnable is true
        assertTrue(result);
    }

    @Test
    void testIsAppAccessAllowed_WithoutAppToken() {
        // Given
        String tokenApp = null;

        // When
        boolean result = activityService.isAppAccessAllowed(tokenApp);

        // Then
        assertTrue(result); // Should always be true when no app token
    }

    @Test
    void testFetchActivityDetail_LoggedInUser() {
        // This test would require mocking the activityBusinessServiceApi
        // and testing the fetchActivityDetailForLoggedInUser method
        // Implementation depends on the actual service setup
    }

    @Test
    void testFetchActivityDetail_AnonymousUser() {
        // This test would require mocking the activityBusinessServiceApi
        // and testing the fetchActivityDetailForAnonymousUser method
        // Implementation depends on the actual service setup
    }

    @Test
    void testValidateActivityState_ValidState() {
        // Given
        GetActivityDetailResp.ActivityBaseInfo baseInfo = new GetActivityDetailResp.ActivityBaseInfo();
        baseInfo.setActivityState("launching");
        activityDetail.setActivityBaseInfo(baseInfo);

        // When & Then
        assertDoesNotThrow(() -> activityService.validateActivityState(activityDetail));
    }

    @Test
    void testValidateActivityState_CanceledState() {
        // Given
        GetActivityDetailResp.ActivityBaseInfo baseInfo = new GetActivityDetailResp.ActivityBaseInfo();
        baseInfo.setActivityState("canceled");
        activityDetail.setActivityBaseInfo(baseInfo);

        // When & Then
        assertThrows(Exception.class, () -> activityService.validateActivityState(activityDetail));
    }

    @Test
    void testValidateActivityState_ExpiredState() {
        // Given
        GetActivityDetailResp.ActivityBaseInfo baseInfo = new GetActivityDetailResp.ActivityBaseInfo();
        baseInfo.setActivityState("expiry");
        activityDetail.setActivityBaseInfo(baseInfo);

        // When & Then
        assertThrows(Exception.class, () -> activityService.validateActivityState(activityDetail));
    }

    @Test
    void testParseAndThrowDetailedException_BusinessException() {
        // Given
        ApiAccessException exception = new ApiAccessException("【业务异常】Test error message]");

        // When & Then
        Exception result = assertThrows(Exception.class, 
            () -> activityService.parseAndThrowDetailedException(exception));
        
        assertTrue(result.getMessage().contains("Test error message"));
    }

    @Test
    void testParseAndThrowDetailedException_MessageException() {
        // Given
        ApiAccessException exception = new ApiAccessException("message: Test error message]");

        // When & Then
        Exception result = assertThrows(Exception.class, 
            () -> activityService.parseAndThrowDetailedException(exception));
        
        assertTrue(result.getMessage().contains("Test error message"));
    }

    @Test
    void testParseAndThrowDetailedException_GenericException() {
        // Given
        ApiAccessException exception = new ApiAccessException("Generic error message");

        // When & Then
        Exception result = assertThrows(Exception.class, 
            () -> activityService.parseAndThrowDetailedException(exception));
        
        assertTrue(result.getMessage().contains("Generic error message"));
    }
}
