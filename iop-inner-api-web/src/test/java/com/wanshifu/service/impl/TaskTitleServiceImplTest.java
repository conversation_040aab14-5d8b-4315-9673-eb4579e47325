package com.wanshifu.service.impl;

import com.wanshifu.iop.activity.domain.bo.ActivityTaskBeanBo;
import com.wanshifu.iop.activity.domain.po.UserActivityTask;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for TaskTitleServiceImpl
 * Tests the task title and description generation logic
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class TaskTitleServiceImplTest {

    @InjectMocks
    private TaskTitleServiceImpl taskTitleService;

    private UserActivityTask userActivityTask;
    private ActivityTaskBeanBo activityTaskBean;

    @BeforeEach
    void setUp() {
        userActivityTask = new UserActivityTask();
        userActivityTask.setTargetQuantityValue(3);
        userActivityTask.setTargetAmountValue(new BigDecimal("100"));
        userActivityTask.setTimeLimit(24);

        activityTaskBean = new ActivityTaskBeanBo();
        activityTaskBean.setExecuteType("single");
        activityTaskBean.setRewardUserUpperLimit(5);
        activityTaskBean.setRewardUserDayUpperLimit(2);
        activityTaskBean.setExecuteTimeRangeStart("09:00");
        activityTaskBean.setExecuteTimeRangeEnd("18:00");
    }

    @Test
    void testGenerateTaskTitle_Visit() {
        // When
        String result = taskTitleService.generateTaskTitle("visit", userActivityTask);

        // Then
        assertEquals("完成3次登录", result);
    }

    @Test
    void testGenerateTaskTitle_CreateOrderWithTimeLimit() {
        // When
        String result = taskTitleService.generateTaskTitle("create_order", userActivityTask);

        // Then
        assertEquals("内完成3次下单", result);
    }

    @Test
    void testGenerateTaskTitle_CreateOrderWithoutTimeLimit() {
        // Given
        userActivityTask.setTimeLimit(0);

        // When
        String result = taskTitleService.generateTaskTitle("create_order", userActivityTask);

        // Then
        assertEquals("完成3次下单", result);
    }

    @Test
    void testGenerateTaskTitle_AppointMaster() {
        // When
        String result = taskTitleService.generateTaskTitle("appoint_master", userActivityTask);

        // Then
        assertEquals("内完成3次指派", result);
    }

    @Test
    void testGenerateTaskTitle_OrderPayMultipleWithAmount() {
        // Given
        userActivityTask.setTargetQuantityValue(2);
        userActivityTask.setTargetAmountValue(new BigDecimal("50"));
        userActivityTask.setTimeLimit(24);

        // When
        String result = taskTitleService.generateTaskTitle("order_pay", userActivityTask);

        // Then
        assertEquals("内完成2次费用托管，且金额满50元", result);
    }

    @Test
    void testGenerateTaskTitle_OrderPaySingleWithAmount() {
        // Given
        userActivityTask.setTargetQuantityValue(1);
        userActivityTask.setTargetAmountValue(new BigDecimal("100"));
        userActivityTask.setTimeLimit(24);

        // When
        String result = taskTitleService.generateTaskTitle("order_pay", userActivityTask);

        // Then
        assertEquals("内累计托管费用满100元", result);
    }

    @Test
    void testGenerateTaskTitle_ManualRate() {
        // When
        String result = taskTitleService.generateTaskTitle("manual_rate", userActivityTask);

        // Then
        assertEquals("内完成3次评价", result);
    }

    @Test
    void testGenerateTaskTitle_NullUserActivityTask() {
        // When
        String result = taskTitleService.generateTaskTitle("visit", null);

        // Then
        assertEquals("", result);
    }

    @Test
    void testGenerateTaskTitle_UnknownTaskSymbol() {
        // When
        String result = taskTitleService.generateTaskTitle("unknown_task", userActivityTask);

        // Then
        assertEquals("", result);
    }

    @Test
    void testGenerateTaskTitleForAnonymous_Visit() {
        // When
        String result = taskTitleService.generateTaskTitleForAnonymous("visit");

        // Then
        assertEquals("完成指定次数登录", result);
    }

    @Test
    void testGenerateTaskTitleForAnonymous_CreateOrder() {
        // When
        String result = taskTitleService.generateTaskTitleForAnonymous("create_order");

        // Then
        assertEquals("完成指定次数下单", result);
    }

    @Test
    void testGenerateTaskTitleForAnonymous_UnknownTask() {
        // When
        String result = taskTitleService.generateTaskTitleForAnonymous("unknown_task");

        // Then
        assertEquals("", result);
    }

    @Test
    void testGenerateTaskDescription_Single() {
        // When
        String result = taskTitleService.generateTaskDescription(activityTaskBean);

        // Then
        assertEquals("活动期间最多可领取1次", result);
    }

    @Test
    void testGenerateTaskDescription_Cycle() {
        // Given
        activityTaskBean.setExecuteType("cycle");

        // When
        String result = taskTitleService.generateTaskDescription(activityTaskBean);

        // Then
        assertEquals("活动期间最多可领取5次", result);
    }

    @Test
    void testGenerateTaskDescription_Day() {
        // Given
        activityTaskBean.setExecuteType("day");

        // When
        String result = taskTitleService.generateTaskDescription(activityTaskBean);

        // Then
        assertEquals("每天09:00到18:00可领取2次", result);
    }

    @Test
    void testCalculateRemainingSeconds_WithTimeLimit() {
        // Given
        Date activityEndTime = new Date(System.currentTimeMillis() + 3600000); // 1 hour from now
        Long timeLimit = 1800L; // 30 minutes
        Date applyTime = new Date(System.currentTimeMillis() - 600000); // 10 minutes ago

        // When
        Long result = taskTitleService.calculateRemainingSeconds(activityEndTime, timeLimit, applyTime);

        // Then
        assertTrue(result > 0);
        assertTrue(result <= timeLimit);
    }

    @Test
    void testCalculateRemainingSeconds_WithoutTimeLimit() {
        // Given
        Date activityEndTime = new Date(System.currentTimeMillis() + 3600000); // 1 hour from now
        Long timeLimit = 0L;
        Date applyTime = new Date(System.currentTimeMillis() - 600000); // 10 minutes ago

        // When
        Long result = taskTitleService.calculateRemainingSeconds(activityEndTime, timeLimit, applyTime);

        // Then
        assertTrue(result > 0);
        // Should be approximately 1 hour (3600 seconds), allowing for test execution time
        assertTrue(result <= 3600);
        assertTrue(result >= 3590);
    }

    @Test
    void testCalculateRemainingSeconds_ActivityEnded() {
        // Given
        Date activityEndTime = new Date(System.currentTimeMillis() - 3600000); // 1 hour ago
        Long timeLimit = 1800L;
        Date applyTime = new Date(System.currentTimeMillis() - 7200000); // 2 hours ago

        // When
        Long result = taskTitleService.calculateRemainingSeconds(activityEndTime, timeLimit, applyTime);

        // Then
        assertEquals(0L, result);
    }
}
