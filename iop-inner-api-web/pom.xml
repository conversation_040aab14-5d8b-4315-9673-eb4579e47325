<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>iop-inner-api</artifactId>
        <groupId>com.wanshifu</groupId>
        <version>1.0.25-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>
    <artifactId>iop-inner-api-web</artifactId>
    <version>1.0.25-SNAPSHOT</version>

    <properties>
        <maven.compiler.plugin.version>3.6.1</maven.compiler.plugin.version>
        <maven.source.plugin.version>3.0.1</maven.source.plugin.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logstash-logback-encoder</artifactId>
                    <groupId>net.logstash.logback</groupId>
                </exclusion>
                <exclusion>
                    <groupId>net.bull.javamelody</groupId>
                    <artifactId>javamelody-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.bull.javamelody</groupId>
                    <artifactId>javamelody-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-config-service-api</artifactId>
            <version>${wshifu-order-config-service-api.version}</version>
        </dependency>
        <!-- 师傅 -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-manage-config-service-api</artifactId>
            <version>${master-manage-config-service-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>image-service-api</artifactId>
            <version>1.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>user-super-service-api</artifactId>
            <version>1.0.30</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-rocketmq-spring-boot-starter</artifactId>
            <version>${wanshfiu-framework-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>cat-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-rocketmq-consume-dispatcher</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-inner-api-base</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>

        <!-- apollo 客户端-->
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>1.4.0</version>
        </dependency>

        <!--日志规范-->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>5.2</version>
        </dependency>

        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- 返回给前端的解码器才用这个-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-web-api-core-spring-boot-starter</artifactId>
            <!-- <exclusions>
                 <exclusion>
                     <artifactId>wshifu-framework-web-api-core</artifactId>
                     <groupId>com.wanshifu</groupId>
                 </exclusion>
             </exclusions>-->
        </dependency>


        <!-- 活动服务api-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-activity-service-api</artifactId>
            <version>${wshifu.iop.activity.service.api}</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-test</artifactId>
            <version>${wanshfiu-framework-version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wshifu-framework-core</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-redis-spring-boot-starter</artifactId>
            <version>${wanshfiu-framework-version}</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-ad-service-api</artifactId>
            <version>${iop-ad-service-api.version}</version>
        </dependency>
        <!--现金券服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>user-voucher-service-api</artifactId>
            <version>1.0.47</version>
        </dependency>
        <!--家庭优惠券服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>toc-user-voucher-service-api</artifactId>
            <version>${toc-user-voucher-service-api-version}</version>
        </dependency>
        <!--师傅inner服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-inner-api-api</artifactId>
            <version>${wshifu-master-inner-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wanshifu</groupId>
                    <artifactId>order-offer-service-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>order-offer-service-api</artifactId>
            <version>${wshifu-order-offer-service-api.version}</version>
        </dependency>


        <!--地址服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-address-service-api</artifactId>
            <version>${base-address-service-api.version}</version>
        </dependency>

        <!--营销配置服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-marketing-config-service-api</artifactId>
            <version>${iop-marketing-config-service.version}</version>
        </dependency>

        <!--用户端服务-->
<!--        <dependency>-->
<!--            <groupId>com.wanshifu</groupId>-->
<!--            <artifactId>user-inner-api-base</artifactId>-->
<!--            <version>${wshifu-user-inner-api.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>user-service-api</artifactId>
            <version>${user-service-api-version}</version>
        </dependency>
        <!--用户订单服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>user-order-service-api</artifactId>
            <version>${user-order-service-api-version}</version>
        </dependency>
        <!--支付服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>payment-service-api</artifactId>
            <version>${payment-service-api-version}</version>
        </dependency>
        <!--基础服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>base-privacy-phone-service-api</artifactId>
            <version>${base-privacy-phone-service.version}</version>
        </dependency>

        <!--权益服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-equity-service-api</artifactId>
            <version>${iop-equity-service-api.version}</version>
        </dependency>

        <!--师傅红包服务-->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>bonus-service-api</artifactId>
            <version>${bonus-service-api-version}</version>
        </dependency>

        <!-- 玩法服务 -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>iop-play-method-service-api</artifactId>
            <version>${iop-play-method-service-api-version}</version>
        </dependency>
        <!-- 师傅信息服务 -->
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>master-information-service-api</artifactId>
            <version>1.0.164</version>
        </dependency>
    </dependencies>

</project>