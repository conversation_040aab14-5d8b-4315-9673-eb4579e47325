<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>iop-inner-api</artifactId>
        <groupId>com.wanshifu</groupId>
        <version>1.0.25-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>iop-inner-api-base</artifactId>
    <version>1.0.25-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-framework-core</artifactId>
            <version>${wanshfiu-framework-version}</version>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>com.wanshifu</groupId>
            <artifactId>wshifu-microservice-cloud-fegin-component</artifactId>
            <version>2.18.Jacoco</version>
            <exclusions>
                <exclusion>
                    <artifactId>wshifu-framework-lang</artifactId>
                    <groupId>com.wanshifu</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>