package com.wanshifu.iop.inner.api.domains.response.landingpage;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.iop.inner.api.domains.response.RewardInfoVo;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 用户活动详情--商家后台多状态广告位专用
 */

@Data
public class MerchantActivityDetailMultiStatusAdApiResp {
    /**
     * 服务器时间
     */
    private Long serverTimeStamp;

    /**
     * 展示的任务状态：0-未完成任务，1-待领取任务，2-没有任务展示
     */
    private Integer showTaskStatus = 0;
    /**
     * 活动获取的所有奖励文案
     */
    private String activityAllGiveRewardText;
    /**
     * 广告是否可以关闭 广告是否可关闭,有专门的关闭按钮:1-可以，0-不可以
     */
    private Integer closable;
    /**
     * 活动基础信息
     */
    private MerchantActivityDetailMultiStatusAdApiResp.ActivityBaseInfo activityBaseInfo;
    /**
     * 单个活动任务
     */
    private ActivityTaskVo activityTaskVo;

    /**
     * 奖励信息
     */
    private List<RewardInfoVo> rewardInfoRespList;

    //活动基本信息
    @Data
    public static class ActivityBaseInfo {
        /**
         * 用户活动任务id列表
         */
        private List<Long> userActivityTaskIdList;
        /**
         * 活动id
         */
        private Long activityId;


        /**
         * 活动是否开始 1:已经开始，0：未开始
         */
        private Integer isActivityStart;

        /**
         * 活动名称
         */
        private String activityName;

        /**
         * 活动落地页地址
         */
        private String activityUrl;
        /**
         * 活动描述
         */
        private String activityDescription;

        /**
         * 活动开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date activityStartTime;

        /**
         * 活动结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date activityEndTime;

        /**
         * 活动投放开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date launchStartTime;

        /**
         * 活动投放结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date launchEndTime;

        /**
         * 报名方式：auto:自动报名，manual：手动报名
         */
        private String applyMethod;

        /**
         * 活动报名开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date applyStartTime;

        /**
         * 活动报名结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date applyEndTime;

        /**
         * 领取方式 auto:自动领取，manual：手动领取
         */
        private String rewardGiveMethod;

        /**
         * 奖励方式 single:完成一个任务奖励一个，multiple：任务全部完成后批量奖励
         */
        private String rewardGiveStrategy;

        /**
         * 奖励领取开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date rewardGiveStartTime;

        /**
         * 奖励领取结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date rewardGiveEndTime;

        /**
         * 是否报名 1:已报名 0：未报名
         */
        private Integer isApply;

        /**
         * 一次性领取奖励的时候 是否需要领奖：1：是，0：否
         */
        private Integer hasReceivedReward;

        /**
         * 活动状态
         */
        private String activityState;

        /**
         * apply_condition  1:付费报名 0免费报名
         */
        private Integer applyCondition;
        /**
         * 任务完成礼盒图片
         */
        private String taskFinishGiftImgUrl;
        /**
         * 任务待完成礼盒图片
         */
        private String taskNotFinishGiftImgUrl;
    }


    /**
     * 活动任务列表
     */
    @Data
    public static class ActivityTaskVo {
        /**
         * 用户任务id列表
         */
        private List<Long> userActivityTaskIdList;
        /**
         * 任务id
         */
        private Long activityTaskId;

//        /**
//         * 是否展示订单限制 1:展示 0：不展示
//         */
//        private Integer isShowOrderLimit = 0;
//
//        /**
//         * 按钮状态：1：未领取（已经完成任务），2：已领奖，3：已失效（期限内未完成任务） 4：未完成任务
//         */
//        private Integer buttonStatus;
//
//        /**
//         * 投放端口, none:不限，merchant_web：web, merchant_app:app
//         */
//        private String launchPort;

        /**
         * 是否有任务级别的倒计时 1:有，0：没有
         */
        private Integer existTaskTimeLimit;

//        /**
//         * 活动任务标题文案
//         */
//        private String title = "";
//
//        /**
//         * 活动任务左边标题下面的描述
//         */
//        private String titleDesc = "";

        /**
         * 时间 返回剩下多少秒，标题任务完成时间
         */
        private Long secondsRemain;


//        /**
//         * 用户对象id，user_id,master_id
//         */
//        private Long userId;

        /**
         * 报名时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date applyTime;
//
//        /**
//         * 任务配置id
//         */
//        private Long taskConfigId;

        /**
         * 任务标识
         */
        private String taskSymbol;

//        /**
//         * 按钮标题
//         */
//        private String buttonTitle;
//
//        /**
//         * 按钮链接map
//         * {
//         * "app": "http://www.sina.com.cn",
//         * "web": "http://www.baidu.com"
//         * }
//         */
//        private String buttonSkipUrl;
//
//        /**
//         * 当前任务状态，0：未完成，1：已完成
//         */
//        private Integer taskExecuteState;
//
//        /**
//         * 奖励发放状态	0:未发放，1：已发放
//         */
//        private Integer rewardGiveState;
        /**
         * 奖励类型
         */
        private String rewardSymbol;
        /**
         * 目标值
         */
        private BigDecimal targetQuantityValue;

        /**
         * 目标值类型 fixed_value:固定值,chain_value:环比增长值,chain_rate:环比增长率
         */
        private BigDecimal targetAmountValue;

        /**
         * 当前完成次数
         */
        private Integer currentQuantityValue;

        /**
         * 当前完成金额
         */
        private BigDecimal currentAmountValue;

//        /**
//         * 用户目标金额范围值（1000,1999）逗号隔开
//         */
//        private String targetAmountValueRange;

//        /**
//         * 奖励类型 rewardType; 1:现金券，2：券包 3：金币，4：服务保障金
//         */
//        private Long rewardType;

        /**
         * 奖励价值
         */
        private BigDecimal rewardValue;

        /**
         * 现金券类型:money_off:满减-默认 、discount: 折扣
         */
        private String voucherType;

        /**
         * 限时时长
         */
        private Integer timeLimit;

//        /**
//         * 奖励发放类型	fixed_value:固定值，return_point:返点(百分比)
//         */
//        private String rewardGiveType;
//
//        /**
//         * decimal(10,2) 	固定值/返点百分比（1-100）
//         */
//        private BigDecimal rewardGiveValue;
//
//        /**
//         * 奖励图片aid
//         */
//        private String rewardImageUrl;
        /**
         * 是否选中
         */
        private Integer isChoice;
        /**
         * 奖励文案
         */
        private String rewardText;
        /**
         * 还差xx文案
         */
        private String needRewardText;
        /**
         * 任务执行类型[single:单次,cycle:持续循环,day:每天,week:每周,month:每月]
         */
        private String executeType;
        /**
         * 已完成文案
         */
        private String finishRewardText;
        /**
         * 当前任务进度：带百分号
         */
        private String currentTaskPercent;
    }
}
