package com.wanshifu.iop.inner.api.domains.bo;

import lombok.Data;

import java.util.List;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2024/4/24 15:32
 * @Description:广告跳转地址bo
 */
@Data
public class ForwardUrlBo {

    /**
     * 跳转地址录入方式[1-手动录入，2-非手动录入,3-活动落地页录入]
     */
    private Integer inputType;

    /**
     * 跳转地址类型[0-无跳转链接,1-H5链接,2-内部小程序,3-外部小程序,4-内部app页面]
     */
    private Integer urlType;

    /**
     * 跳转地址
     */
    private String url;

    /**
     * 小程序信息
     */
    private AppletInfo appletInfo;

    /**
     * 扩展数据
     */
    private List<ExtraData> extraList;

    /**
     * 小程序信息
     */
    @Data
    public static class AppletInfo{

        /**
         * 小程序appId
         */
        private String appId;

        /**
         * 小程序原始id
         */
        private String originalId;
    }
    @Data
    public static class ExtraData{
        /**
         * 扩展类型[点击领取地址:click_to_claim]
         */
        private String extraSymbol;
        /**
         * 跳转地址录入方式[1-手动录入，2-非手动录入,3-活动落地页录入]
         */
        private Integer inputType;

        /**
         * 跳转地址类型[0-无跳转链接,1-H5链接,2-内部小程序,3-外部小程序,4-内部app页面]
         */
        private Integer urlType;

        /**
         * 跳转地址
         */
        private String url;
    }

}
