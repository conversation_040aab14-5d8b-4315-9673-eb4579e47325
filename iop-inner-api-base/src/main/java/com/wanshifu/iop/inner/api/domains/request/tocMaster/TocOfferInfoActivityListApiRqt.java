package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/6/29 15:44
 * @Desc 报价详情活动列表入参
 */
@Data
public class TocOfferInfoActivityListApiRqt extends CommonInfo {

    /**
     * 签名信息
     * */
    private String signature;

    /**
     * 下单用户id
     * userOrderId
     */
    @NotNull(message = "下单用户id不能空")
    private Long createOrderUserId;

    /**
     * 下单人群id，临时
     */
    private List<Long> userCrowIds;

    /**
     * 订单编号
     */
    @NotNull(message = "订单编号不能空")
    private String orderNo;

    /**
     * 关联全局订单id
     */
    @NotNull(message = "订单全局不能空")
    private Long globalOrderTraceId;

    /**
     * 来源(客户端) site,backend,weixin,ikea
     */
    @NotNull(message = "订单来源不能空")
    private String orderFrom;

    /**
     * 订单类目id
     */
    @NotNull(message = "订单类目不能空")
    private Integer categoryId;

    /**
     * 订单服务类型id
     */
    private Integer serveTypeId;

    /**
     * 订单服务类型
     */
    @NotNull(message = "订单服务类型不能空")
    private Integer serveType;

    /**
     * 业务线id
     */
    @NotNull(message = "业务线不能空")
    private Integer businessLineId;

    /**
     * 1级服务id
     */
    @NotNull(message = "一级服务不能空")
    private String serveLevel1Ids;

    /**
     * 服务id 服务id list
     */
    @NotNull
    private String serveIds;

    /**
     * 三级地址
     */
    private Long thirdDivisionId;

    /**
     * 四级地址
     */
    private Long fourthDivisionId;

    /**
     * 下单模式[1:直接指派,2:公开抛单,definite_price:一口价,advance_payment:预付款]
     */
    private Integer appointType;


    /**
     * 订单来源账号类型[user:用户订单,enterprise:总包订单,NULL:不限（单选）]
     */
    private String fromAccountType;

    /**
     * 商品数量
     */
    private Integer groupGoodNum;

    /**
     * 订单报价人数
     */
    private Long offerNum;

    /**
     * 下单时间
     */
    @NotNull(message = "下单时间不能空")
    private Date createOrderTime;

    /**
     * 推单时间
     */
    private Date pushOrderTime;

}
