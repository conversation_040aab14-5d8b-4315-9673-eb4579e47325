package com.wanshifu.iop.inner.api.domains.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.wanshifu.iop.inner.api.domains.bo.ForwardUrlBo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/10 14:41
 * @Description:
 */
@Data
public class CustomerAdResp {
    private List<AdInfo> adInfo;

    //广告信息
    @Data
    public static class AdInfo {
        /**
         * 会员领券资格[0:无,1:有]
         */
        private Integer vipQualificationState;

        //最大展示数量
        private Integer showMaxNumber;

        private String adName;

        private Long adId;

        private String adDescription;

        /**
         * 是否进入保护期：1-进入，0-未进入 默认0
         */
        private Integer isHelpTime;
        /**
         * 是否为关闭状态:1-关闭，0-否
         */
        private Integer isClosed;

        /**
         * 活动id
         */
        private Long activityId;

        /**
         * 广告位标识
         */
        private String adPositionSymbol;

        /**
         * 奖励来源类型[free_voucher_activity:领券活动,vip_equity:会员权益]
         */
        private String rewardSourceType;

        /**
         * 广告开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date adStartTime;

        /**
         * 广告结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date adEndTime;

        /**
         * 弹窗样式标识
         */
        private String popStyleSymbol;

        /**
         * 广告展示形式标识
         */
        private String showTypeSymbol;

        /**
         * 是否领取
         */
        private Integer hasReward;

        /**
         * 素材类型
         */
        private String materialClass;

        /**
         * 素材
         */
        private List<CustomerAdResp.Meterial> meterialList;

        /**
         * 活动信息对象,rewardSourceType=free_voucher_activity时才有值
         */
        private CustomerAdResp.ActivityInfo activityInfo;

        /**
         * 会员权益信息对象,rewardSourceType=vip_equity时才有值
         */
        private CustomerAdResp.VipEquityInfo vipEquityInfo;

        /**
         * 是否已经报名
         */
        private Integer hasSignUp;

    }

    /**
     * 素材列表
     */
    @Data
    public static class Meterial {
        /**
         * 图片url
         */
        private String pictureUrl;

        /**
         * 素材类型
         */
        private String materialClass;

        /**
         * 点击后图片url
         */
        private String clickedPictureUrl;

        /**
         * 标题
         */
        private String meterialTitle;

        /**
         * 跳转地址录入方式:1-手动录入，2-非手动录入,3-活动落地页录入,4-小程序链接,5-H5链接,6-外部小程序
         */
        private Integer forwardUrlType;

        /**
         * 跳转地址
         */
        private String forwardUrl;

        /**
         * 素材描述
         */
        private String materialDesc = "";

        /**
         * 素材类型
         */
        private String materialType;


        /**
         * 图标list
         */
        private List<CustomerAdResp.PopIcon> popIconList;

        /**
         * 是否限时,1:是,0:否
         */
        private Integer isLimitTime;

        /**
         * 展示时长:开屏广告及弹窗广告使用，单位：秒
         */
        private Integer showTime;

        /**
         * 跳转链接信息
         */
        private ForwardUrlBo adForwardUrlInfo;
        /**
         * 素材图片信息
         */
        private MaterialPictureUrlInfo materialPictureUrlInfo;
        /**
         * 商品服务素材
         */
        private GoodsServiceInfo goodsServiceInfo;
        /**
         * 搜索按钮色值
         */
        private String searchButtonHEX;
        /**
         * 定位地区色值
         */
        private String positionRegionHEX;
    }
    @Data
    public static class GoodsServiceInfo {
        /**
         * 素材展示样式:receiveVoucher-领券活动专用切换样式,commonPic-普通图片样式
         */
        private String showType;
        /**
         * 商品服务图片信息列表
         */
        private List<GoodsServicePicVo> goodsServicePicList;
    }
    @Data
    public static class GoodsServicePicVo {
        /**
         * 商品服务图（领券前）url
         */
        private String goodsServiceReceiveBeforeImgUrl;
        /**
         * 商品服务图（领券后）url
         */
        private String goodsServiceReceiveAfterImgUrl;

        /**
         * 普通样式商品服务图片url
         */
        private String goodsServiceCommonImgUrl;
        /**
         * 商品服务图跳转链接地址
         */
        private String goodsServiceForwardUrl;
    }
    @Data
    public static class MaterialPictureUrlInfo {
        /**
         * 标题图url
         */
        private String titleImgUrl;
        /**
         * 更多跳转按钮素材图片url
         */
        private String moreForwardImgUrl;
        /**
         * 一键领取按钮图（领取前）url
         */
        private String oneClickReceiveBeforeImgUrl;
        /**
         * 一键领取按钮图（领取后）url
         */
        private String oneClickReceiveAfterImgUrl;
        /**
         * 大背景图url
         */
        private String bigBackgroundImgUrl;
        /**
         * 中背景图url
         */
        private String middleBackgroundImgUrl;

        /**
         * 领券背景图url
         */
        private String receiveVoucherBackgroundUrl;
        /**
         * 左上角slogan图url
         */
        private String topLeftCornerSloganUrl;
        /**
         * banner图素材
         */
        private String bannerImgUrl;
        private Long bannerImgAid;
    }
    /**
     * 按钮图标
     */
    @Data
    public static class PopIcon {

        /**
         * 按钮功能:领取任务-receive_task、领取奖励-receive_reward,无功能-non
         */
        private String iconFunction;


        /**
         * 跳转地址
         */
        private String forwardUrl;

        /**
         * 按钮跳转code
         * unforward-不跳转,落地页-landing_page，下单页-create_order_page，充值页-recharge_page，添加商品页-add_product_page，添加商品套装页-add_product_suit_page
         */
        private String forwardCode;

        /**
         * 按钮文案
         */
        private String iconTitle;

        /**
         * 是否已经领取 1：已领取，0：未领取
         */
        private Integer isReceive;

        /**
         * forwardUrlType = 2 非手动录入 代表跳转方式 是跳转app 内部界面
         * forwardUrlType = 1 手动录入 代表跳转方式  是加载h5 界面
         */
        private Integer forwardUrlType;


        /**
         * 是否有子按钮 1：是 0否
         */
        private Integer isChildIcon;

        /**
         * 子按钮信息
         * 子按钮信息：{      \"forwardCode\":\"按钮跳转code，默认落地页\",      \"forwardUrl\"跳转地址：默认落地页地址\"      }
         */
        private ChildIconClass childIcon;
        /**
         * 按钮样式：default-默认样式，custom_img-自定义图片
         */
        private String buttonStyle;
        /**
         * 按钮图片
         */
        private Long buttonPictureId;
        /**
         * 按钮图片
         */
        private String buttonPictureUrl;
    }


    /**
     * 活动基本信息
     */
    @Data
    public static class ActivityInfo {
        /**
         * 活动id
         */
        private Long activityId;

        /**
         * 活动名
         */
        private String activityName;

        /**
         * 活动描述
         */
        private String activityDescription;

        /**
         * 活动开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date activityStartTime;

        /**
         * 活动结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date activityEndTime;

        /**
         * 下发时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date applyTime;
        /**
         * 活动标题
         */
        private String activityTitle;

        /**
         * 具体奖励信息
         */
        private List<RewardInfoBo> rewardInfo;
    }

    @Data
    public static class VipEquityInfo{
        /**
         * 会员权益id
         */
        private Long vipEquityBaseId;

        /**
         * 会员权益内容id(序号)
         */
        private Long vipEquityContentId;

        /**
         * 会员权益奖励id
         */
        private Long vipEquityRewardId;

        /**
         * 会员权益类型[specific:特定人群会员权益,formal:正式会员权益,undertake:兜底]
         */
        private String vipEquityType;

        /**
         * 奖励类型[customer_voucher:家庭优惠券,customer_voucher_pack:家庭券包]
         */
        private String extraType;

        /**
         * 奖励扩展id
         */
        private String extraId;

        /**
         * 会员权益开始时间
         */
        private Date equityStartTime;

        /**
         * 会员权益结束时间
         */
        private Date equityEndTime;

        /**
         * 具体奖励信息
         */
        private List<RewardInfoBo> rewardInfo;
    }

    @Data
    public static class RewardInfoBo {
        /**
         * voucher:优惠券
         */
        private String rewardType;
        /**
         * 优惠券类型：money_off-满减,discount-折扣
         */
        private String rewardExtraType;
        /**
         * 优惠券id
         */
        private Long rewardExtraId;
        /**
         * 优惠券名称
         */
        private String rewardExtraName;
        /**
         * 最低门槛，例如 满100元
         */
        private BigDecimal minTradeAmount;
        /**
         * 优惠券最大面额
         */
        private BigDecimal rewardMaxValue;
        /**
         * 折扣系数：9.5折，折扣券才有--暂时用不到，家庭没有折扣券
         */
        private BigDecimal discount;
        /**
         * 奖励数量：优惠券张数
         */
        private Integer rewardNum;
    }
}
