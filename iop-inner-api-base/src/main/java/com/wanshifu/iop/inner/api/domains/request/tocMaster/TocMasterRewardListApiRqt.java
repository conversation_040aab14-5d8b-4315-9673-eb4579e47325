package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2022/7/6 10:40
 * @Desc 师傅奖励列表入参
 */
@Data
public class TocMasterRewardListApiRqt extends CommonInfo {

    /**
     * 师傅登录签名
     * */
    private String signature;

    /**
     * 用户分类；[master:师傅,customer:客户,merchant:商家]
     * */
    @NotNull
    private String userClass;

    /**
     * 活动id，非必填
     * */
    private Long activityId;

    /**
     * 用户活动任务id，非必填
     * */
    private Long userActivityTaskId;

    /**
     * 活动任务id，非必填
     * */
    private Long activityTaskId;



    private Integer pageNum = 1;

    private Integer pageSize = 10;

}
