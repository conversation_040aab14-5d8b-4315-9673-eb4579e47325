package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2022/6/27 11:11
 * @Desc 师傅活动入参
 */
@Data
public class TocMasterActivityListApiRqt extends CommonInfo {
    /**
     * 师傅分类
     */
    @NotNull(message = "师傅分类不能空")
    private String userClass;


    /**
     * 类型
     * all:全部
     * attended：已参加活动
     * unAttended：未参加活动
     * expired：已过期活动
     * */
    @NotNull
    @ValueIn("all,attended,unAttended,expired,recommend")
    private String type;


}
