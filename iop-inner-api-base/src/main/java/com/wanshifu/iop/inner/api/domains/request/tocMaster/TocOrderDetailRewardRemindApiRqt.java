package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date Created in 2022/7/12 18:40
 */
@Data
public class TocOrderDetailRewardRemindApiRqt extends CommonInfo {
    /**
     * 订单全局关联id
     */
    @NotNull
    private String orderNo;
    /**
     * 用户分类
     */
    @NotEmpty
    private String userClass;
//    /**
//     * 订单活动任务标识:quoted-报价
//     */
//    @NotEmpty
//    private String taskSymbol;
}
