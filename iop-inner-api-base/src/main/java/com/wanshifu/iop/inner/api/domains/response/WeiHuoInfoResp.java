package com.wanshifu.iop.inner.api.domains.response;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date Created in 2025/4/9 23:13
 */
@Data
public class WeiHuoInfoResp {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 完整地址带*号
     */
    private String address;
    /**
     * 期望价格
     */
    @NotNull
    private BigDecimal expectedPrice;
    /**
     * 原价
     */
    private BigDecimal originalPrice;
    /**
     * 订单状态(trading-交易进行中,close-订单关闭,finish-交易完成)
     */
    private String orderStatus;
    /**
     * 尾货订单详情页地址
     */
    private String orderDetailUrl;
    /**
     * 商品信息
     */
    private InfoOrderGoodsInfoResp masterInfoOrderGoods;
}
