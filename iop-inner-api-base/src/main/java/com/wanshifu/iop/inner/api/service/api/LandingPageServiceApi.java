package com.wanshifu.iop.inner.api.service.api;

import com.wanshifu.iop.inner.api.domains.request.ActivityDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.request.CustomOrderTagRqt;
import com.wanshifu.iop.inner.api.domains.request.GetActivityDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.request.GetActivityDetailBannerApiRqt;
import com.wanshifu.iop.inner.api.domains.request.MerchantActivityDetailMultiStatusAdApiRqt;
import com.wanshifu.iop.inner.api.domains.request.landingPage.*;
import com.wanshifu.iop.inner.api.domains.request.master.GetDetailAggregationBannerApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryBarrageListApiRqt;
import com.wanshifu.iop.inner.api.domains.request.master.LotteryTurntableLandingPageDetailApiRqt;
import com.wanshifu.iop.inner.api.domains.response.CustomOrderListTagApiResp;
import com.wanshifu.iop.inner.api.domains.response.landingpage.*;
import com.wanshifu.iop.inner.api.domains.response.master.GetDetailAggregationBannerApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryBarrageListApiResp;
import com.wanshifu.iop.inner.api.domains.response.master.LotteryTurntableLandingPageDetailApiResp;
import com.wanshifu.iop.inner.api.service.api.decoder.IopDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import java.text.ParseException;
import java.util.List;
import javax.validation.Valid;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author:<EMAIL>
 * @create:2022-08-23 11:33:06
 * @Description ：落地页服务类api
 **/
@FeignClient(
    value = "iop-inner-api",
    path = "landingPage", configuration = {IopDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
    url = "${wanshifu.iop-inner-api.url}"
)
public interface LandingPageServiceApi {
    /**
     * 获取师傅有奖任务落地页详情
     */
    @PostMapping("/master/getPrizeTasklandingPage")
    MasterPrizeTaskApiResp getPrizeTasklandingPage(@Valid @RequestBody LandingPageRqt landingPageRqt);

    /**
     * 落地页订单列表
     */
    @PostMapping("/master/customOrderListTag")
    CustomOrderListTagApiResp customOrderListTag(@Valid @RequestBody CustomOrderTagRqt customOrderTagRqt);

    /**
     * 落地页订单列表
     */
    @PostMapping("/master/getActivityModelById")
    String getActivityModelById(@Valid @RequestBody ActivityDetailApiRqt activityDetailApiRqt);

    /**
     * 获取师傅订单活动落地页详情
     */
    @PostMapping("/master/getOrderTaskLandingPage")
    MasterOrderTaskApiResp getOrderTaskLandingPage(@Valid @RequestBody MasterOrderLandingApiPageRqt masterOrderLandingPageRqt) throws ParseException;

    /**
     * 获取商家有奖活动落地页详情
     */
    @PostMapping("/merchant/getPrizeTaskLandingPage")
    MetchantPrizeTaskApiResp getPrizeTaskLandingPage(@Valid @RequestBody MetchantPrizeLandingPageRqt metchantPrizeLandingPageRqt) throws ParseException;

    /**
     * 获取商家充值活动专用落地页详情
     */
    @PostMapping("/merchant/getRechargeTaskLandingPage")
    MerchantRechargeTaskApiResp getRechargeTaskLandingPage(@Valid @RequestBody MerchantRechargeTaskLandingPageRqt merchantRechargeTaskLandingPageRqt) throws ParseException;

    /**
     * 商家有奖任务-多状态广告位
     * */
    @PostMapping("merchant/getMerchantActivityDetailAd")
    MerchantActivityDetailAdApiResp getMerchantActivityDetailAd(@Valid @RequestBody GetActivityDetailApiRqt getActivityDetailRqt);
    /**
     * 商家后台多状态广告位数据-order_top_banner专用
     * */
    @PostMapping("merchant/getMerchantActivityDetailBannerAd")
     MerchantActivityDetailBannerAdApiResp getMerchantActivityDetailBannerAd(@Valid @RequestBody GetActivityDetailBannerApiRqt getActivityDetailRqt);
    /**
     * 商家后台多状态广告位数据-通用
     * */
    @PostMapping("merchant/getMerchantActivityDetailMultiStatusAd")
    MerchantActivityDetailMultiStatusAdApiResp getMerchantActivityDetailMultiStatusAd(@Valid @RequestBody MerchantActivityDetailMultiStatusAdApiRqt getActivityDetailRqt);

    /**
     * 发送短信验证码
     */
    @PostMapping("/sendMessageCode")
    boolean sendMessageCode(@Valid @RequestBody SendMessageCodeRqt sendMessageCodeRqt);
    /**
     * 检查手机号并入库
     */
    @PostMapping("/checkPhoneAndSave")
    Integer checkPhoneAndSave(@Valid @RequestBody CheckPhoneAndSaveRqt checkPhoneAndSaveRqt);
    /**
     * 获取隐私政策
     */
    @PostMapping("/master/getPrivacyPolicy")
    GetCurrentAgreementInfoApiResp getPrivacyPolicy();

    /**
     * 手机号生成绑定关系
     * @return
     */
    @PostMapping("/master/setPhoneInsert")
    boolean setPhoneInsert(@Valid @RequestBody SetPhoneInsertApiRqt setPhoneInsertRqt);
    /**
     *
     * 获取邀请人活动落地页详情
     */
    @PostMapping("/master/getInviterTaskLandingPageDetail")
    MasterInviterTaskDetailApiResp getInviterTaskLandingPageDetail(@Valid @RequestBody MasterInviterTaskDetailApiRqt masterInviterTaskDetailApiRqt) throws ParseException;

    /**
     *
     * 获取邀请人活动落地页详情
     */
    @PostMapping("/master/getInviterRewardInfoTaskPage")
    MasterInviterTaskMyRewardApiResp getInviterRewardInfoTaskPage(@Valid @RequestBody MasterInviterTaskDetailMyRewardApiRqt masterInviterTaskDetailApiRqt) throws ParseException;

    /**
     * 获取被邀请人活动落地页详情（应用外）
     */
    @PostMapping("/master/getInviteeTaskOutLandingPageDetail")
    MasterInviteeTaskOutDetailApiResp getInviteeTaskOutLandingPageDetail(@Valid @RequestBody MasterInviteeTaskOutDetailApiRqt masterInviteeTaskOutDetailApiRqt) throws ParseException;

    /**
     * 被邀请人活动落地页详情（应用内）
     */
    @PostMapping("/master/getInviteeTaskInLandingPageDetail")
    MasterInviteeTaskInDetailApiResp getInviteeTaskInLandingPageDetail(@Valid @RequestBody MasterInviteeTaskInDetailApiRqt masterInviteeTaskInDetailApiRqt) throws ParseException;

    /**
     * 师傅邀请活动-集合页
     */
    @PostMapping("/master/getInviteTaskCollectPageDetail")
    MasterInviteTaskCollectPageDetailApiResp getInviteTaskCollectPageDetail(@Valid @RequestBody MasterInviteTaskCollectPageDetailApiRqt masterInviteTaskCollectPageDetailApiRqt) throws ParseException;

    /**
     * 师傅邀请活动-集合页--我的奖励模块
     */
    @PostMapping("/master/getCollectMyRewardData")
    MasterInviteTaskCollectPageMyRwardInfoApiResp getCollectMyRewardData(@Valid @RequestBody MasterInviteTaskCollectPageMyRwardInfoApiRqt masterInviteTaskCollectPageMyRwardInfoApiRqt) throws ParseException;

    /**
     * 师傅邀请活动-获取入驻进度
     */
    @PostMapping("/master/getSettledState")
    GetAccountStatusApiResp getSettledState(@Valid @RequestBody GetSettledStateRqt getSettledStateRqt);

//    /**
//     * 获取活动单个任务订单限制
//     */
//    @PostMapping("/getActivityTaskOrderLimitInfo")
//    GetActivityTaskOrderLimitApiResp getActivityTaskOrderLimitInfo(@Valid @RequestBody GetActivityTaskOrderLimitApiRqt getActivityTaskOrderLimitApiRqt);

    /**
     * 获取师傅有奖任务落地页详情--抽奖专用落地页
     */
    @PostMapping("/master/getLotteryTurntableLandingPageDetail")
    LotteryTurntableLandingPageDetailApiResp getLotteryTurntableLandingPageDetail(@Valid @RequestBody LotteryTurntableLandingPageDetailApiRqt rqt);
    /**
     * 抽奖专用落地页--弹幕获取接口
     */
    @PostMapping("/master/getLotteryBarrageList")
    List<LotteryBarrageListApiResp> getLotteryBarrageList(@Valid @RequestBody LotteryBarrageListApiRqt rqt);

    /**
     * 师傅多状态广告位详情聚合接口
     */
    @PostMapping("/master/getDetailAggregationBanner")
    GetDetailAggregationBannerApiResp getDetailAggregationBanner(@Valid @RequestBody GetDetailAggregationBannerApiRqt rqt);

}