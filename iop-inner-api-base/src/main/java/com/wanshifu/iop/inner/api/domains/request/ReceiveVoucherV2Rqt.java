package com.wanshifu.iop.inner.api.domains.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * 免费领券 领取优惠券
 *
 * <AUTHOR>
 * @date 2025/01/03 9:00
 */
@Data
public class ReceiveVoucherV2Rqt extends CommonInfo{

    /**
     * 奖励来源类型[free_voucher_activity:领券活动,vip_equity:会员权益,free_voucher_get_one_activity:领券活动单张领取]
     */
    @NotBlank
    private String rewardSourceType;

    /**
     * 活动id(rewardSourceType为free_voucher_activity时需要传)
     */
    private Long activityId;

    /**
     * 会员权益奖励id(rewardSourceType为vip_equity时需要传)
     */
    private Long vipEquityRewardId;

    /**
     * 用户分类[master:师傅,customer:家庭,merchant:商家]
     */
    private String userClass;

    /**
     * 奖励类型[customer_voucher:家庭优惠券,customer_voucher_pack:家庭券包](rewardSourceType为vip_equity时需要传)
     */
    private String extraType;

    /**
     * 奖励扩展id(rewardSourceType为vip_equity时需要传)
     */
    private String extraId;

    /**
     * 请求外网ip,内部使用
     */
    public String ip;

    /**
     * 请求客户端,内部使用
     */
    public String client;

    /**
     * 设备指纹,内部使用
     */
    public String deviceId;
}
