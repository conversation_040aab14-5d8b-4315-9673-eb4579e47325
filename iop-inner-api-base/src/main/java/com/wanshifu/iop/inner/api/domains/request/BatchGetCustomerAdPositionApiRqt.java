package com.wanshifu.iop.inner.api.domains.request;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量获取家庭广告位广告
 */
@Data
public class BatchGetCustomerAdPositionApiRqt extends CommonInfo {
    /**
     * 用户分类(master:师傅,customer:客户,merchant:商家)
     */
    @ValueIn("master,customer,merchant")
    private String userClass;

    /**
     * 调用端口端口：customer_applet:C端小程序 customer_app:C端APP
     */
    @NotNull(message = "广告投放端口不能为空")
    private String launchPort;

    /**
     * 广告位标识
     */
    @NotNull(message = "广告位标识不能为空")
    @Size(min = 1)
    private List<String> adPositionSymbolList;

    /**
     * 支持类型[vip_equity:会员权益]
     */
    private List<String> supportTypeList;

    /**
     * 渠道编号
     */
    private String channel;

    /**
     * 1级地区id
     */
    private Long firstDivisionId;

    /**
     * 2级地区id
     */
    private Long secondDivisionId;

    /**
     * 3级地区id
     */
    private Long thirdDivisionId;

    /**
     * 4级地区id
     */
    private Long fourthDivisionId;
}
