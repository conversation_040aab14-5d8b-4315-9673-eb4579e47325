package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date Created in 2024/9/3 20:37
 */
@Data
public class TocOrderRewardTotalReq extends CommonInfo {
    /**
     * 订单id
     */
    @NotNull
    private Long orderId;
    /**
     * 订单服务费
     */
    private BigDecimal orderAmount;

    /**
     * 是否一口价报抢结合-报价[0:否,1:是]
     */
    private Integer isDefiniteOffer;
}
