package com.wanshifu.iop.inner.api.domains.response;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @author:z<PERSON><PERSON>@wanshifu.com
 * @create:2025-04-28 17:09:55
 * @Description ：
 **/
@Data
public class GetCustomerServeTagVoucherListApiResp {
    /**
     * 券id
     */
    private Integer eventId;

    /**
     * 券Name
     */
    private String eventName;

    /**
     * 优惠券类型
     */
    private String mold;
    /**
     * 优惠券面额
     */
    private BigDecimal voucherAmount;
    private BigDecimal thresholdAmount;
    /**
     * 折扣率（0-1 非负数） mold=discount时必传
     */
    private BigDecimal discountRatio;
    /**
     * 券类型
     */
    private String eventType;
    /**
     * 起始日期
     */
    private Long startDate;
    /**
     * 有效期类型:1-日期范围，2-固定天数
     */
    private Integer termStatus;
    /**
     * 领取后有效天数
     */
    private Integer expireTimeNum;
    /**
     * 截止日期
     */
    private Long endDate;
    /**
     * 有效期剩余时间  秒
     */
    private Integer remainTime;

    /**
     * 是否领取，0未领取，1已领取
     */
    private Integer receivedState = 0;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date activityStartTime;
    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndTime;
}