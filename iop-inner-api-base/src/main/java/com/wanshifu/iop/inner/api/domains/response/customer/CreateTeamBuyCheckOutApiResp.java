package com.wanshifu.iop.inner.api.domains.response.customer;

import lombok.Data;

/**
 * <AUTHOR>
 * @date Created in 2025/5/29 10:45
 */
@Data
public class CreateTeamBuyCheckOutApiResp {
    /**
     * 校验结果[0:不通过,1:通过]
     */
    private Integer result;

    /**
     * 失败原因类型：
     * existOpenTeam-该服务下已存在发起的团,
     * existWaitPayOrder-该服务下存在待支付订单,
     * taskUpperLimit-任务上限,
     * budgetUpperLimit-达到预算上限,
     * rewardUpperLimit-达到奖励上限,
     * inventoryUpperLimit-库存上限
     */
    private String failType;
    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 团购编号:错误类型是existOpenTeam及existWaitPayOrder有值
     */
    private Long teamNo;
    /**
     * 订单id：错误类型是existWaitPayOrder有值
     */
    private Long orderId;
    /**
     * 服务名称
     */
    private String serveName;
}
