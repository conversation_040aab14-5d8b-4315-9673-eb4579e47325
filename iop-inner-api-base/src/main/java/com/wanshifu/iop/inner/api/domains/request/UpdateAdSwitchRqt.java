package com.wanshifu.iop.inner.api.domains.request;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2025-05-12 09:37:21
 * @Description:
 **/
@Data
public class UpdateAdSwitchRqt extends CommonInfo {

    /**
     * 修改后广告开关状态[1:开启 0:关闭]
     */
    @NotNull
    private Integer adStatus;

    /**
     * 用户分类(master:师傅,customer:家庭,merchant:商家)
     */
    @NotEmpty
    @ValueIn("master,customer,merchant")
    private String userClass;
}
