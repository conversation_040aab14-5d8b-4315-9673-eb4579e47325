package com.wanshifu.iop.inner.api.domains.response.customer;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2025/5/20 18:42
 */
@Data
public class GetTeamBuyTagApiResp {
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动任务id
     */
    private Long activityTaskId;

    /**
     * 活动结束时间
     */
    private Long activityEndTime;
    /**
     * 投放结束时间
     */
    private Long launchEndTime;
    /**
     * 系统当前时间(时间戳)
     */
    private Long currentTime;

    /**
     * 团购价格信息
     */
    private List<TeamPriceRangeBo> priceRangeInfoList;

    @Data
    public static class TeamPriceRangeBo {

        /**
         * 区间最小值
         */
        private BigDecimal minPrice;

        /**
         * 区间最大值
         */
        private BigDecimal maxPrice;
        /**
         * 团购价:目前只有一个，取第一个即可，后面可能会有扩展成多个
         */
        private List<TeamPriceBo> teamBuyPriceList;
    }

    @Data
    public static class TeamPriceBo {
        /**
         * 成团目标人数
         */
        private Integer teamTargetNumber;
        /**
         * 拼团价
         */
        private BigDecimal teamBuyPrice;

    }
}
