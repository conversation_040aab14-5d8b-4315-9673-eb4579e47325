package com.wanshifu.iop.inner.api.domains.request.customer;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date Created in 2025/5/29 10:49
 */
@Data
public class CreateTeamBuyCheckOutApiRqt extends CommonInfo {
    /**
     * 用户分类(master:师傅,customer:家庭,merchant:商家)
     */
    @NotBlank
    @ValueIn("master,customer,merchant")
    private String userClass;
    /**
     * 服务id
     */
    @NotNull
    private Long serveId;
    /**
     * 活动id
     */
    private Long activityId;
}
