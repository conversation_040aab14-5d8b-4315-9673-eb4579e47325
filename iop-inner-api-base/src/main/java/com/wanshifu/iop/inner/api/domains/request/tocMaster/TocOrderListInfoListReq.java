package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: k<PERSON><PERSON>yun
 * @time:2023/8/29 0029 20:06
 */
@Data
public class TocOrderListInfoListReq extends CommonInfo {

    private List<OrderInfo> orderInfoList;

    @Data
    public static class OrderInfo {
        /**
         * 订单id
         */
        @NotNull
        private Long orderId;
        /**
         * 是否需要查询奖励总额：1-是，0-否
         */
        private Integer queryRewardTotalFlag;
        /**
         * 订单服务费
         */
        private BigDecimal orderAmount;
    }
}
