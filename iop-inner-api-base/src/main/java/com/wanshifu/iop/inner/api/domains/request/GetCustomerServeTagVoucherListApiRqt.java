package com.wanshifu.iop.inner.api.domains.request;

import java.math.BigDecimal;
import lombok.Data;

/**
 * @author:z<PERSON><PERSON>@wanshifu.com
 * @create:2025-04-28 16:56:02
 * @Description ：
 * {
 *     "serveId": 44, //服务id
 *     "serveName": "服务名称", //三级服务名称
 *     "serverPrice": 0, //服务原价
 *     "thirdDivisionId": 333, //三级地址id
 *     "thirdDivisionName": "深圳市", //三级地址名称  城市id
 *     "sceneType": "", //场景类型[serve_detail:服务详情页,receive_pop:领券弹窗]
 * }
 **/
@Data
public class GetCustomerServeTagVoucherListApiRqt {
    /**
     * 服务id
     */
    private Long serveId;
    /**
     * 三级服务名称
     */
    private String serveName;
    /**
     * 服务原价
     */
    private BigDecimal serverPrice;
    /**
     * 三级地址id
     */
    private Long thirdDivisionId;
    /**
     * 三级地址名称  城市id
     */
    private String thirdDivisionName;
    /**
     * 场景类型[serve_detail:服务详情页,receive_pop:领券弹窗]
     */
    private String sceneType;
    /**
     * showOrderTag
     */
    private String showOrderTag;

}