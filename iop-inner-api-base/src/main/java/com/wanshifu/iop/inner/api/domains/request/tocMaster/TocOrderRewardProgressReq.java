package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date Created in 2024/9/3 20:37
 */
@Data
public class TocOrderRewardProgressReq extends CommonInfo {
    /**
     * 订单id
     */
    @NotNull
    private Long orderId;
    /**
     * 详情页类型：quoted-报价详情，order-订单详情
     */
    @NotEmpty
    private String orderDetailType;
    /**
     * 是否需要查询奖励总额：1-是，0-否
     */
    private Integer queryRewardTotalFlag;
    /**
     * 订单服务费
     */
    private BigDecimal orderAmount;
}
