package com.wanshifu.iop.inner.api.domains.request.ad;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date Created in 2025/4/9 10:50
 */
@Data
public class SaveUnInterestAdRqt extends CommonInfo {
    /**
     * 用户分类(master:师傅,customer:客户,merchant:商家)
     */
    @NotEmpty
    private String userClass;
    /**
     * 端口：merchant_app:商家APP merchant_web:商家web master_app:师傅APP customer_applet:C端小程序 customer_app:C端APP
     */
    @NotEmpty
    private String launchPort;
    /**
     * 广告id
     */
    @NotNull
    @Min(1)
    private Long adId;
    /**
     * 广告位标识
     */
    @NotEmpty
    private String adPositionSymbol;
}
