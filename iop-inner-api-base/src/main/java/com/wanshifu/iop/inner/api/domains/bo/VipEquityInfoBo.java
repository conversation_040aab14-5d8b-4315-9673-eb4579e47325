package com.wanshifu.iop.inner.api.domains.bo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2025-04-22 21:17:46
 * @Description:
 **/

@Data
public class VipEquityInfoBo {

    /**
     * 会员领券资格[0:无,1:有]
     */
    private Integer vipQualificationState;

    /**
     * 会员权益id
     */
    private Long vipEquityBaseId;

    /**
     * 会员权益内容id(序号)
     */
    private Long vipEquityContentId;

    /**
     * 会员权益奖励id
     */
    private Long vipEquityRewardId;

    /**
     * 会员权益类型[specific:特定人群会员权益,formal:正式会员权益,undertake:兜底]
     */
    private String vipEquityType;

    /**
     * 奖励类型[customer_voucher:家庭优惠券,customer_voucher_pack:家庭券包]
     */
    private String extraType;

    /**
     * 奖励扩展id
     */
    private String extraId;

    /**
     * 奖励领取状态[0:未领取,1:已领取]
     */
    private Integer couponReceivedState;

    /**
     * 当月券包的领取时间
     */
    private Date receiveTime;

    /**
     * 会员权益开始时间
     */
    private Date equityStartTime;

    /**
     * 会员权益结束时间
     */
    private Date equityEndTime;

    /**
     * 具体奖励信息
     */
    private List<RewardDetailInfoBo> rewardInfo;
}
