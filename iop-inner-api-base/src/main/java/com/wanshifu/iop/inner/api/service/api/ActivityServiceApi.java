package com.wanshifu.iop.inner.api.service.api;

import com.wanshifu.iop.inner.api.domains.request.*;
import com.wanshifu.iop.inner.api.domains.response.*;
import com.wanshifu.iop.inner.api.domains.response.merchant.GetPayPageVoucherListApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MatchOrderActivityDetailApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MatchUserOrderListTagApiResp;
import com.wanshifu.iop.inner.api.domains.response.merchant.MerchantPublishOrderTagApiResp;
import com.wanshifu.iop.inner.api.service.api.decoder.IopDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

@FeignClient(
        value = "iop-inner-api",
        path = "activity/user", configuration = {IopDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-inner-api.url}"
)
public interface ActivityServiceApi {

    /**
     * 获取活动详情
     */
    @PostMapping("/getActivityDetail")
    GetActivityDetailApiResp getActivityDetail(@Valid @RequestBody GetActivityDetailApiRqt getActivityDetailApiRqt) throws ParseException;

    /**
     * 获取优惠活动列表
     */
    @PostMapping("/getList")
    List<GetActivityListApiResp> getList(@Valid @RequestBody GetActivityListApiRqt getActivityListApiR);

    /**
     * 弹窗广告弹出结果回传
     */
    @PostMapping("/backDialogAd")
    Integer backDialogAd(@Valid @RequestBody BackDialogAdRqt backDialogAdRqt);

    /**
     * 用户活动报名
     */
    @PostMapping("/apply")
    Integer apply(@Valid @RequestBody ApplyActivityApiRqt applyActivityApiRqt);
    /**
     * 用户活动报名--多选一
     */
    @PostMapping("/applyChoice")
    Integer applyChoice(@Valid @RequestBody ActivityApplyApiChoiceRqt activityApplyApiChoiceRqt);

    /**
     * 用户活动报名--多选一
     */
    @PostMapping("/applyChoiceNew")
    Integer applyChoiceNew(@Valid @RequestBody ActivityApplyApiChoiceOfRqt activityApplyApiChoiceOfRqt);
    /**
     * 家庭用户活动报名-新
     */
    @PostMapping("/applyCustomer")
    Integer applyCustomer(@Valid @RequestBody ActivityApplyCustomerApiRqt activityApplyCustomerApiRqt);
    /**
     * 用户活动下发 用户活动下发
     */
    @PostMapping("/issue")
    Integer issue(@Valid @RequestBody IssueApiRqt issueApiRqt) throws ParseException;

    /**
     * 用户浏览落地页详情记录
     */
    @PostMapping("/actiLandingclickCollect")
    Integer actiLandingclickCollect(@Valid @RequestBody EnterActivityDetailRqt enterActivityDetailRqt);

    /**
     * 家庭多状态广告点击记录接口
     */
    @PostMapping("/customerAdClickCollect")
    Integer customerAdClickCollect(@Valid @RequestBody CustomerAdClickCollectRqt customerAdClickCollectRqt);

    /**
     * 获取用户弹窗广告
     */
//    @PostMapping("/getDialogAd")
//    GetUserAdResp getDialogAd(@Valid @RequestBody GetUserPopAdRqt getUserPopAdRqt) throws ParseException;

    /**
     * 获取广告位广告（非弹窗）
     */
//    @PostMapping("/getAdPosition")
//    GetUserAdResp getAdPosition(@Valid @RequestBody GetUserAdRqt getUserAdRqt,
//                                @CookieValue(value = "wsf_user_token", required = false) String token,
//                                @RequestHeader(value = "appToken", required = false) String tokenApp,
//                                @RequestHeader(value = "signature", required = false) String signature) throws ParseException;

    /**
     * 批量获取广告位广告，非弹窗、非开屏广告
     */
    @PostMapping("/getAdPositionBatch")
    Map<String, GetUserAdResp> getAdPositionBatch(@Valid @RequestBody GetUserAdBatchRqt getUserAdBatchRqt);

    /**
     * 用户活动任务奖励领取
     *
     * @return
     */
    @PostMapping("/taskGiveReward")
    List<RewardGiveApiResp> taskGiveReward(@Valid @RequestBody ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt);

    /**
     * 商家多状态banner广告领奖专用接口
     * @param rewardGiveBannerAdApiRqt
     * @return
     */
    @PostMapping("/taskGiveRewardForMultiBannerAd")
     List<RewardGiveBannerAdApiResp> taskGiveRewardForMultiBannerAd(@Valid @RequestBody RewardGiveBannerAdApiRqt rewardGiveBannerAdApiRqt);
    /**
     * 用户活动任务奖励领取
     *
     * @return
     */
    @PostMapping("/taskGiveRewardOf")
    List<RewardGiveApiResp> taskGiveRewardOf(@Valid @RequestBody ActivityTaskGiveRewardApiOfRqt activityTaskGiveRewardApiRqt);

    /**
     * 用户活动任务奖励领取--活动单个领取专用
     *
     * @return
     */
    @PostMapping("/singleTaskGiveReward")
    RewardTypeApiResp singleTaskGiveReward(@Valid @RequestBody ActivityTaskGiveRewardApiRqt activityTaskGiveRewardApiRqt);

    /**
     * 获取活动目标任务还差次数/金额
     */
    @PostMapping("/getUserActivityTaskShort")
    List<GetUserActivityTaskShortApiResp> getUserActivityTaskShort(@Valid @RequestBody GetActivityDetailApiRqt getActivityDetailApiRqt);

    /**
     * 商家充值页面优惠活动信息
     *
     * @param getRechargeDiscountActivityApiRqt
     * @return
     */
    @PostMapping("/getRechargeDiscountInfo")
    GetRechargeDiscountActivityApiResp getRechargeDiscountInfo(@Valid @RequestBody GetRechargeDiscountActivityApiRqt getRechargeDiscountActivityApiRqt);
    /**
     * 获取商家优符合条件的优惠券id列表
     *
     * @param getPayPageVoucherListApiRqt
     * @return
     */
    @PostMapping("/getPayPageVoucherList")
    GetPayPageVoucherListApiResp getPayPageVoucherList(@Valid @RequestBody GetPayPageVoucherListApiRqt getPayPageVoucherListApiRqt);

    /**
     * 商家订单列表活动匹配标签
     */
    @PostMapping("/matchUserOrderListTag")
    MatchUserOrderListTagApiResp matchUserOrderListTag(@Valid @RequestBody MatchUserOrderListTagRqt matchUserOrderListTagRqt);

    /**
     * 商家订单详情
     */
    @PostMapping("/matchOrderActivityDetail")
    MatchOrderActivityDetailApiResp matchOrderActivityDetail(@Valid @RequestBody MatchOrderActivityDetailApiRqt matchOrderActivityDetailRqt);
    /**
     * 商家订单列表活动匹配标签
     */
    @PostMapping("/getPublishOrderTag")
    MerchantPublishOrderTagApiResp getPublishOrderTag(@Valid @RequestBody MerchantPublishOrderTagApiRqt merchantPublishOrderTagApiRqt);

    /**
     * 给大数据用
     * 判断指定实验是否有进行中的活动
     * */
    @PostMapping("/isUsedByTryId")
    List<IsUsedByTryIdResp> isUsedByTryId(@Valid @RequestBody IsUsedByTryIdRqt isUsedByTryIdRqt);

    /**
     * 获取用户已报名的进行中且未达任何上限的充值活动
     * @param rqt
     * @return
     */
    @PostMapping("/getUserRechargeActivity")
    GetUserRechargeActivityResp getUserRechargeActivity(GetUserRechargeActivityRqt rqt);

    /**
     * 充值成功页获取用户的充值任务的奖励
     * @param rqt
     * @return
     */
    @PostMapping("/getRechargeRewardList")
    GetRechargeRewardResp getRechargeRewardList(@Valid @RequestBody GetRechargeRewardRqt rqt);

    /**
     * 钱包页面获取用户待领取的充值任务的多选一奖励
     * @param rqt
     * @return
     */
    @PostMapping("/getRechargeChooseCombineRewardInfo")
    RechargeChooseCombineResp getRechargeChooseCombineRewardInfo(@Valid @RequestBody RechargeChooseCombineRqt rqt);

    /**
     * 执行浏览指定页面任务
     * @param rqt
     * @return
     */
    @PostMapping("/runBrowsePageTask")
    BrowsePageResp runBrowsePageTask(@Valid @RequestBody BrowsePageRqt rqt);
    /**
     * 免费领券-领取优惠券
     */
    @PostMapping("/receiveVoucher")
    ReceiveVoucherResp receiveVoucher(@Valid @RequestBody ReceiveVoucherRqt rqt, @RequestHeader(value = "token") String token);
    /**
     * 获取家庭服务详情标签优惠券列表接口
     */
    @PostMapping("/getCustomerServeTagVoucherList")
    List<GetCustomerServeTagVoucherListApiResp> getCustomerServeTagVoucherList(@Valid @RequestBody GetCustomerServeTagVoucherListApiRqt rqt);


}
