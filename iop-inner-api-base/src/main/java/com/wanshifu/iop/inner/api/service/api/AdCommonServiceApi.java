package com.wanshifu.iop.inner.api.service.api;

import com.wanshifu.iop.inner.api.domains.request.ad.SaveUnInterestAdRqt;
import com.wanshifu.iop.inner.api.service.api.decoder.IopDecoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultEncoder;
import com.wanshifu.spring.cloud.fegin.component.DefaultErrorDecode;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 广告公共接口
 *
 * <AUTHOR>
 * @date Created in 2025/4/9 10:44
 */
@FeignClient(
        value = "iop-inner-api",
        path = "ad/common", configuration = {IopDecoder.class, DefaultEncoder.class, DefaultErrorDecode.class},
        url = "${wanshifu.iop-inner-api.url}"
)
public interface AdCommonServiceApi {
    @PostMapping("/saveUnInterestAd")
    Integer saveUnInterestAd(@Valid @RequestBody SaveUnInterestAdRqt rqt);
}
