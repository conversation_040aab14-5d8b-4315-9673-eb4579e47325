package com.wanshifu.iop.inner.api.domains.response.customer;

import lombok.Data;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2024/12/20 15:00:00
 * @Description:
 */
@Data
public class GetCustomerActivityTagApiResp {
    /**
     * 查询结果标签类型：teamBuy-拼团，seckill-秒杀，空则一个没有
     */
    private String resultTagType;
    /**
     * 秒杀标签信息
     */
    private GetSeckillTagApiResp seckillTagInfo;
    /**
     * 拼团标签信息
     */
    private GetTeamBuyTagApiResp teamBuyTagInfo;
    /**
     * 无活动提示文案
     */
    private String failMsg;

    /**
     * 额外文案,开发用
     */
    private String extraMsg;
}
