package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/7/7 16:08
 * @Desc 报价列表入参
 */
@Data
public class TocOrderListApiRqt extends CommonInfo {

    /**
     * 签名
     */
    private String signature;

    /**
     * 订单信息
     */
    private List<OrderInfoItem> orderLists;

    @Data
    public static class OrderInfoItem {
        /**
         * 下单用户id
         * userOrderId
         */
        private Long createOrderUserId;

        /**
         * 下单人群id
         */
        private List<Long> userCrowIds;

        /**
         * 订单编号
         */
        private String orderNo;

        /**
         * 关联全局订单id
         */
        private Long globalOrderTraceId;

        /**
         * 来源(客户端) site,backend,weixin,ikea
         */
        private String orderFrom;

        /**
         * 订单类目id
         */
        private Integer categoryId;

        /**
         * 订单服务类型id
         */
        private Integer serveTypeId;

        /**
         * 订单服务类型
         */
        private Integer serveType;

        /**
         * 业务线id
         */
        private Integer businessLineId;

        /**
         * 1级服务id
         */
        private String serveLevel1Ids;

        /**
         * 服务id 服务id list
         */
        private String serveIds;

        /**
         * 三级地址
         */
        private Long thirdDivisionId;

        /**
         * 四级地址
         */
        private Long fourthDivisionId;

        /**
         * 下单模式[normal:直接指派,open:公开抛单,definite_price:一口价,advance_payment:预付款]
         */
        private Integer appointType;


        /**
         * 订单来源账号类型[user:用户订单,enterprise:总包订单,NULL:不限（单选）]
         */
        private String fromAccountType;

        /**
         * 商品数量
         */
        private Integer groupGoodNum;

        /**
         * 订单报价人数
         */
        private Long offerNum;

        /**
         * 下单时间
         */
        private Date createOrderTime;

        /**
         * 推单时间
         */
        private Date pushOrderTime;

        /**
         * 其他订单过滤条件--订单包属性
         * */
        private boolean isOrderPackage;
    }

}
