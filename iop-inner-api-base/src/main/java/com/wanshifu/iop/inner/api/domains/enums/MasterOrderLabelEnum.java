package com.wanshifu.iop.inner.api.domains.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 师傅订单标签值枚举
 *
 * <AUTHOR>
 * @date Created in 2023/5/12 10:27
 */

public enum MasterOrderLabelEnum {
    /**
     * 专属订单
     */
    EXCLUSIVE("exclusive", "专属"),
    /**
     * 直约订单
     */
    DIRECT_APPOINTMENT("direct_appointment", "直约"),
    /**
     * 优选订单
     */
    PREFERRED("preferred", "优选"),
    /**
     * 合约师傅订单
     */
    CONTRACT("contract", "合约"),
    /**
     * 品牌订单
     */
    BRAND("brand", "品牌"),
    /**
     * 师傅店铺订单
     */
    MASTER_SHOP("master_shop", "师傅店铺"),
    /**
     * 协议师傅订单
     */
    AGREEMENT_MASTER("agreement_master", "协议师傅"),
    /**
     * 样板城市
     */
    NEW_MODEL("new_model", "样板城市"),
    /**
     * 订单包订单
     */
    PACKAGE_ORDER("package_order", "订单包订单"),
    /**
     * 招募师傅
     */
    BZ_SHOW_LABEL("bz_show_label", "招募师傅"),
    /**
     * 团购订单
     */
    MONEY_SAVING_CARD("money_saving_card", "团购订单"),
    /**
     * 报抢结合-报价(师傅已报价,不管是否自动指派)
     */
    DEFINITE_OFFER_CONTAIN_MERCHANT_RULE("definite_offer_contain_merchant_rule", "报抢结合-报价"),
    /**
     * 报抢结合-补贴(已自动指派)
     */
    DEFINITE_OFFER_GRAB_SUBSIDIES("definite_offer_grab_subsidies", "报抢结合-补贴"),
    /**
     * 一口价订单-纯报价
     */
    DEFINITE_ONLY_OFFER("definite_only_offer", "一口价订单-纯报价"),
    /**
     * 新合约师傅订单标签
     */
    NEW_CONTRACT("new_contract", "新合约师傅"),

    /**
     * 企服订单
     */
    TOC_CORPORATE_SERVICES("toc_corporate_services", "企服订单"),

    /**
     * 技能验证单
     */
    SKILL_TASK_ORDER_GRAB("skill_task_order_grab", "技能验证单"),
    ;

    public final String label;
    public final String name;

    MasterOrderLabelEnum(String label, String name) {
        this.label = label;
        this.name = name;
    }

    /**
     * 一级标签中可能包含合约师傅的标签
     */
    public static final List<String> CONTRACT_MASTER_TYPE_LIST = Arrays.asList(EXCLUSIVE.label,AGREEMENT_MASTER.label);

    /**
     * 是否包含一级合约师傅标签
     * @return
     */
    public static boolean isContainsContractMasterType(String label){
        return CONTRACT_MASTER_TYPE_LIST.contains(label);
    }
}
