package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TocReservationInfoReq extends CommonInfo {

    @NotNull
    private Long orderId;

    @NotNull
    @ValueIn("reserve_customer,serve_sign")
    private String taskSymbol;
}
