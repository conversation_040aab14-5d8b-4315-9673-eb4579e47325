package com.wanshifu.iop.inner.api.domains.request.customer;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2025/5/20 10:50
 */
@Data
public class GetCustomerActivityTagInnerRqt extends CommonInfo {
    /**
     * 用户分类(master:师傅,customer:家庭,merchant:商家)
     */
    @NotBlank
    @ValueIn("master,customer,merchant")
    private String userClass;

    /**
     * 服务id
     */
    @NotNull
    private Long serveId;

    /**
     * 二级地址id-市
     */
    private String secondDivisionId;
    /**
     * 三级地址id-区
     */
    private String thirdDivisionId;
    /**
     * 下单模式[normal:直接指派,open:公开抛单,definite_price:一口价,advance_payment:预付款]
     */
    @NotNull
    private String appointMethod;

    /**
     * 订单类型集合
     */
    private List<String> orderTypeList;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 活动任务id
     */
    private Long activityTaskId;

    /**
     * 渠道码
     */
    private String channelCode;
    /**
     * 标签类型：teamBuy-拼团，seckill-秒杀
     * 有传标签类型则只查该类型的活动，没传则根据优先级查询：秒杀>拼团
     */
    @ValueIn("teamBuy,seckill")
    private String tagType;
    /**
     * 团购编号-参团时必传
     */
    private Long teamNo;
}
