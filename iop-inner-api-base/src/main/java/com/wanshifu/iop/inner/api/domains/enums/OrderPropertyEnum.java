package com.wanshifu.iop.inner.api.domains.enums;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2024/8/30 15:37
 * @Description:
 */
public enum OrderPropertyEnum {
    /**
     * 师傅店铺订单
     */
    IS_ORDER_SHOP("isOrderShop","师傅店铺订单"),
    /**
     * 订单包内的订单
     */
    IS_ORDER_PACKAGE("isOrderPackage","订单包内的订单"),
    /**
     * 合约师傅订单
     */
    IS_ORDER_CONTRACT("isOrderContract","合约师傅订单"),
    /**
     * 协议师傅(专属)订单
     */
    IS_ORDER_AGREEMENT_EXCLUSIVE("isOrderAgreementExclusive","协议师傅(专属)订单"),
    /**
     * 协议师傅(合约)订单
     */
    IS_ORDER_AGREEMENT_CONTRACT("isOrderAgreementContract","协议师傅(合约)订单"),
    /**
     * 协议师傅(品牌)订单
     */
    IS_ORDER_AGREEMENT_BRAND("isOrderAgreementBrand","协议师傅(品牌)订单"),
    /**
     * 样板城市(样板城市)订单
     */
    IS_ORDER_NEW_MODEL("isOrderNewModel","样板城市(样板城市)订单"),
    /**
     * 招募师傅(招募)订单
     */
    IS_ORDER_RECRUIT("isOrderRecruit","招募师傅(招募)订单"),
    /**
     * 团购订单
     */
    IS_ORDER_GROUP_BUY("isOrderGroupBuy","团购订单"),
    /**
     * 报抢结合-报价订单
     */
    IS_ORDER_QUOTED_GRAB_QUOTED("isOrderQuotedGrabQuoted","报抢结合-报价订单"),
    /**
     * 一口价订单-纯报价
     */
    IS_ORDER_DEFINITE_ONLY_OFFER("isOrderDefiniteOnlyOffer","一口价订单-纯报价"),
    /**
     * 新合约师傅
     */
    IS_NEW_ORDER_CONTRACT("isNewOrderContract","新合约师傅"),

    /**
     * 企服订单
     */
    IS_CORPORATE_SERVICES("isCorporateServices","企服订单"),

    /**
     * 技能验证单
     */
    IS_SKILL_TASK_ORDER_GRAB("isSkillTaskOrderGrab","技能验证单"),
    ;

    public final String code;

    public final String cn;

    OrderPropertyEnum(String code, String cn) {
        this.code = code;
        this.cn = cn;
    }
}
