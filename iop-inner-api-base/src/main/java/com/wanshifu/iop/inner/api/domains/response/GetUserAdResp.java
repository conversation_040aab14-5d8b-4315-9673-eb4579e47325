package com.wanshifu.iop.inner.api.domains.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 非弹窗广告返回实体类
 */

@Data
public class GetUserAdResp {

    /**
     * 广告数据源类型：IOC-IOC广告,MangoSDK:芒果SDK
     */
    private String adSourceType;

    /**
     * 个性化开关状态,1:开启 0:关闭
     */
    private Integer adStatus;

    /**
     * 个性化广告最大拉取限时,单位:秒,adSourceType不为IOC时有值
     */
    private Integer personalizationAdMaxPullTime;
    /**
     * 个性化广告  其他出参
     */
    private String otherParam;

    private List<AdInfo> adInfo;

    //广告信息
    @Data
    public static class AdInfo {

        //最大展示数量
        private Integer showMaxNumber;

        private String adName;

        private Long adId;

        private String adDescription;

        /**
         * 是否进入保护期：1-进入，0-未进入 默认0
         */
        private Integer isHelpTime;
        /**
         * 是否为关闭状态:1-关闭，0-否
         */
        private Integer isClosed;

        /**
         * 活动id
         */
        private Long activityId;

        /**
         * 广告位标识
         */
        private String adPositionSymbol;

        /**
         * 广告开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date adStartTime;

        /**
         * 广告结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date adEndTime;

        /**
         * 弹窗样式标识
         */
        private String popStyleSymbol;
        /**
         * 弹窗关联功能信息
         */
        private FunctionRelationInfoVo functionRelationInfo;
        /**
         * 广告展示形式标识
         */
        private String showTypeSymbol;

        /**
         * 是否领取
         */
        private Integer hasReward;

        /**
         * 素材类型
         */
        private String materialClass;

        /**
         * 素材
         */
        private List<Meterial> meterialList;

        /**
         * 活动信息对象
         */
        private ActivityInfo activityInfo;

        /**
         * 是否已经报名
         */
        private Integer hasSignUp;
        /**
         * 尾货数据列表
         */
        private List<WeiHuoInfoResp> weiHuoList;
        /**
         * 素材数据源类型：weihuo-尾货数据
         */
        private String materialSourceType;
    }

    @Data
    public static class FunctionRelationInfoVo {
        private Long activityId;
        private Long taskExecuteBatchId;
    }

    /**
     * 素材列表
     */
    @Data
    public static class Meterial {
        /**
         * 图片url
         */
        private String pictureUrl;

        /**
         * 素材类型
         */
        private String materialClass;

        /**
         * 点击后图片url
         */
        private String clickedPictureUrl;

        /**
         * 标题
         */
        private String meterialTitle;

        /**
         * forwardUrlType = 2 非手动录入 代表跳转方式 是跳转app 内部界面
         * forwardUrlType = 1 手动录入 代表跳转方式  是加载h5 界面
         */
        private Integer forwardUrlType;

        /**
         * 跳转地址
         */
        private String forwardUrl;

        /**
         * 素材描述
         */
        private String materialDesc = "";

        /**
         * 素材类型
         */
        private String materialType;


        /**
         * 图标list
         */
        private List<PopIcon> popIconList;

        /**
         * 展示时长:开屏广告专用，单位：秒
         */
        private Integer showTime;
        /**
         * 背景图片url,针对商家首页顶部轮播大banner使用
         */
        private String backendPictureUrl;
        /**
         * 去逛逛引导图url
         */
        private String goExploreGuideImgUrl;
        /**
         * 去逛逛跳转地址
         */
        private String goExploreForwardUrl;
        /**
         * 去逛逛跳转地址类型[0-无跳转链接,1-H5链接,2-内部小程序,3-外部小程序,4-内部app页面]
         */
        private Integer goExploreForwardUrlType;
    }

    /**
     * 按钮图标
     */
    @Data
    public static class PopIcon {

        /**
         * 按钮功能:领取任务-receive_task、领取奖励-receive_reward,无功能-non
         */
        private String iconFunction;


        /**
         * 跳转地址
         */
        private String forwardUrl;

        /**
         * 按钮跳转code
         * unforward-不跳转,落地页-landing_page，下单页-create_order_page，充值页-recharge_page，添加商品页-add_product_page，添加商品套装页-add_product_suit_page
         */
        private String forwardCode;

        /**
         * 按钮文案
         */
        private String iconTitle;

        /**
         * 是否已经领取 1：已领取，0：未领取
         */
        private Integer isReceive;

        /**
         * forwardUrlType = 2 非手动录入 代表跳转方式 是跳转app 内部界面
         * forwardUrlType = 1 手动录入 代表跳转方式  是加载h5 界面
         */
        private Integer forwardUrlType;


        /**
         * 是否有子按钮 1：是 0否
         */
        private Integer isChildIcon;

        /**
         * 子按钮信息
         * 子按钮信息：{      \"forwardCode\":\"按钮跳转code，默认落地页\",      \"forwardUrl\"跳转地址：默认落地页地址\"      }
         */
        private ChildIconClass childIcon;
        /**
         * 按钮样式：default-默认样式，custom_img-自定义图片
         */
        private String buttonStyle;
        /**
         * 按钮图片
         */
        private Long buttonPictureId;
        /**
         * 按钮图片
         */
        private String buttonPictureUrl;
    }


    /**
     * 活动基本信息
     */
    @Data
    public static class ActivityInfo {
        /**
         * 活动id
         */
        private Long activityId;

        /**
         * 活动名
         */
        private String activityName;

        /**
         * 活动描述
         */
        private String activityDescription;

        /**
         * 活动开始时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date activityStartTime;

        /**
         * 活动结束时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date activityEndTime;

        /**
         * 下发时间
         */
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        private Date applyTime;
        /**
         * 活动标题
         */
        private String activityTitle;
        /**
         * 任务
         */
        private ActivityTaskApiInfo activityTaskInfo;
        /**
         * 是否报名
         */
        private Integer isApply;
    }

    /**
     * 活动任务信息
     */
    @Data
    public static class ActivityTaskApiInfo {
        private Long activityId;
        /**
         * 活动任务id
         */
        private Long activityTaskId;
        /**
         * 奖励配置id，1：现金券，2：券包，3：金币，4：服务保障金
         */
        private Long rewardConfigId;
        /**
         * 批次id
         */
        private Long taskExecuteBatchId;
        /**
         * 任务配置id
         */
        private Long taskConfigId;

        /**
         * 任务标识
         */
        private String taskSymbol;
        /**
         * 当前任务状态，0：未完成，1：已完成
         */
        private Integer taskExecuteState;

        /**
         * 奖励发放状态	0:未发放，1：已发放
         */
        private Integer rewardGiveState;
        /**
         * 按钮状态：1：未领取（已经完成任务），2：已领奖，3：已失效（期限内未完成任务） 4：未完成任务
         */
        private Integer buttonStatus;
        /**
         * 进度条进度
         */
        private BigDecimal progressState;
        /**
         * 还差次数
         */
        private Integer needRewardNum;
        /**
         * 还差次数文案
         */
        private String needRewardNumText;
        /**
         * 待领奖数
         */
        private Integer waitRewardNum;
        /**
         * 单个活动任务待领奖数
         */
        private Integer waitRewardNumByTask;
        /**
         * 待领奖数-文案
         */
        private String waitRewardNumText;
        /**
         * 已领奖-文案
         */
        private String receivedAwardText;
        /**
         * 展示文案-文案
         */
        private String rewardText;
        /**
         * 还差金额
         */
        private BigDecimal needRewardNumAmount;
        /**
         * 奖励类型 rewardType; 1:现金券，2：券包 3：金币，4：服务保障金
         */
        private Long rewardType;
        /**
         * 奖励扩展id,现金券模板id或券包id
         */
        private Long rewardExtraId;
        /**
         * 奖励类型
         */
        private String rewardSymbol;
        /**
         * 奖励价值
         */
        private BigDecimal rewardValue;
    }

}
