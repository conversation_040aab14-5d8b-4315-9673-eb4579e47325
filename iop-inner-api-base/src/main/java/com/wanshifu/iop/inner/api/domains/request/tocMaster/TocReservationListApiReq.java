package com.wanshifu.iop.inner.api.domains.request.tocMaster;


import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TocReservationListApiReq extends CommonInfo {
//    @Size(min = 1,max = 20)
    private List<Long> orderIdList;

    @NotNull
    @ValueIn("reserve_customer,serve_sign")
    private String taskSymbol;
}
