package com.wanshifu.iop.inner.api.domains.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2025-04-22 21:03:49
 * @Description:
 **/
@Data
public class RewardDetailInfoBo {
    /**
     * voucher:优惠券
     */
    private String rewardType;
    /**
     * 优惠券类型：money_off-满减,discount-折扣
     */
    private String rewardExtraType;
    /**
     * 优惠券id
     */
    private Long rewardExtraId;
    /**
     * 优惠券名称
     */
    private String rewardExtraName;
    /**
     * 最低门槛，例如 满100元
     */
    private BigDecimal minTradeAmount;
    /**
     * 优惠券最大面额
     */
    private BigDecimal rewardMaxValue;
    /**
     * 折扣系数：9.5折，折扣券才有--暂时用不到，家庭没有折扣券
     */
    private BigDecimal discount;
    /**
     * 奖励数量：优惠券张数
     */
    private Integer rewardNum;
}
