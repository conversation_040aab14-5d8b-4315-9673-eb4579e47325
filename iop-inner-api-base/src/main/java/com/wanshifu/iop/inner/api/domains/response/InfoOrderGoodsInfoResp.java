package com.wanshifu.iop.inner.api.domains.response;

import lombok.Data;

@Data
public class InfoOrderGoodsInfoResp {

    /**
     * 商品封面图片
     */
    private String goodsImageUrl;

//    private String goodsImageAid;

    /**
     * 商品标题
     */
    private String goodsTitle;
    /**
     * 商品描述
     */
    private String goodsDesc;
    /**
     * 商品原链接
     */
    private String goodsOriginalUrl;
    /**
     * 商品成色类型:allNew-全新，almostNew-近乎全新，slightUsed-轻微使用痕迹，obviousUsed-明显使用痕迹
     */
    private String goodsQualityType;
    /**
     * 商品包装类型:unPackaged-未拆包装，packaged_original_stay-已拆有原包装，packaged_original_not_stay-已拆，无原包装
     */
    private String goodsPackType;

    /**
     * 商品查看人数
     */
    private Integer masterViewCount;
}
