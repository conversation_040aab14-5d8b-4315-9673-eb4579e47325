package com.wanshifu.iop.inner.api.domains.request;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2025-05-12 09:45:51
 * @Description:
 **/
@Data
public class GetAdSwitchRqt extends CommonInfo {

    /**
     * 用户分类(master:师傅,customer:家庭,merchant:商家)
     */
    @NotEmpty
    @ValueIn("master,customer,merchant")
    private String userClass;
}
