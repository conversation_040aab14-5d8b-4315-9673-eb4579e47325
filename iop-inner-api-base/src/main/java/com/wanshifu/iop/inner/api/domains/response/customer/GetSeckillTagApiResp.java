package com.wanshifu.iop.inner.api.domains.response.customer;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: lin<PERSON>feng
 * @Date: 2024/12/20 15:00:00
 * @Description:
 */
@Data
public class GetSeckillTagApiResp {
    /**
     * 是否有秒杀价[0:否,1:是]
     */
    private Integer hasSeckillPrice;

    /**
     * 活动id,hasSeckillPrice=1才有值
     */
    private Long activityId;

    /**
     * 活动任务id,hasSeckillPrice=1才有值
     */
    private Long activityTaskId;

    /**
     * 场次id,hasSeckillPrice=1才有值
     */
    private Long seckillSessionId;

    /**
     * 该场次结束时间(时间戳),hasSeckillPrice=1才有值
     */
    private Long sessionEndTime;

    /**
     * 系统当前时间(时间戳),hasSeckillPrice=1才有值
     */
    private Long currentTime;

    /**
     * 秒杀价区间列表(商品原价满足 minPrice<原价<=maxPrice时,取对应seckillPrice为秒杀价),hasSeckillPrice=1才有值
     */
    private List<SeckillPriceRangeBo> seckillPriceRangeList;

    @Data
    public static class SeckillPriceRangeBo{

        /**
         * 区间最小值
         */
        private BigDecimal minPrice;

        /**
         * 区间最大值
         */
        private BigDecimal maxPrice;

        /**
         * 区间秒杀价
         */
        private BigDecimal seckillPrice;
    }
}
