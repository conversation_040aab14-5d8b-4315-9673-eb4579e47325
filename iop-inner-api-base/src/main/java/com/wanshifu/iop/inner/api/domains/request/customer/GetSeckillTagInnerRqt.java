package com.wanshifu.iop.inner.api.domains.request.customer;

import com.wanshifu.framework.core.validation.annotation.ValueIn;
import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2024-12-30 10:00:54
 * @Description:
 **/
@Data
public class GetSeckillTagInnerRqt extends CommonInfo {
    /**
     * 用户分类(master:师傅,customer:家庭,merchant:商家)
     */
    @NotBlank
    @ValueIn("master,customer,merchant")
    private String userClass;

    /**
     * 服务id
     */
    @NotNull
    private Long serveId;

    /**
     * 三级地址id
     */
    private String thirdDivisionId;

    /**
     * 下单模式[normal:直接指派,open:公开抛单,definite_price:一口价,advance_payment:预付款]
     */
    @NotNull
    private String appointMethod;

    /**
     * 订单类型集合
     * */
    private List<String> orderTypeList;

    /**
     * 活动id
     */
    private Long seckillActivityId;

    /**
     * 活动任务id
     */
    private Long seckillActivityTaskId;

    /**
     * 渠道码
     */
    private String channelCode;
}
