package com.wanshifu.iop.inner.api.domains.enums;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-04-23 10:13:04
 * @Description:奖励来源类型[free_voucher_activity:领券活动,vip_equity:会员权益]
 **/
public enum RewardSourceTypeEnum {

    FREE_VOUCHER_ACTIVITY("free_voucher_activity", "领券活动"),
    FREE_VOUCHER_GET_ONE_ACTIVITY("free_voucher_get_one_activity", "领券活动(单张领取)"),
    VIP_EQUITY("vip_equity", "会员权益");
    public final String type;
    public final String cn;

    RewardSourceTypeEnum(String type, String cn) {
        this.type = type;
        this.cn = cn;
    }
}
