package com.wanshifu.iop.inner.api.domains.request;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MerchantActivityDetailMultiStatusAdApiRqt extends CommonInfo{
    /**
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    @NotNull(message = "用户类型不能为空")
    private String userClass;

    private Integer isH5 = 0;

}
