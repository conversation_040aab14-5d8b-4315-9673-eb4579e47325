package com.wanshifu.iop.inner.api.domains.request.tocMaster;

import com.wanshifu.iop.inner.api.domains.request.CommonInfo;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: k<PERSON>ianyun
 * @time:2023/8/30 0030 20:01
 * @Desc:订单报价详情入参
 */
@Data
public class TocOfferInfoActivityApiReq extends CommonInfo {

    @NotNull
    private Long orderId;

    /**
     * 是否一口价报抢结合-报价[0:否,1:是]
     */
    private Integer isDefiniteOffer;
}
