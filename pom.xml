<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wanshifu</groupId>
        <artifactId>wshifu-microservice-parent</artifactId>
        <version>2.18.Jacoco</version>
        <relativePath />
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>iop-inner-api</artifactId>
    <packaging>pom</packaging>
    <version>1.0.25-SNAPSHOT</version>

    <modules>
        <module>iop-inner-api-base</module>
        <module>iop-inner-api-web</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <wanshfiu-framework-version>2.0.10</wanshfiu-framework-version>
        <maven.compiler.plugin.version>3.6.1</maven.compiler.plugin.version>
        <maven.source.plugin.version>3.0.1</maven.source.plugin.version>
        <wshifu.iop.activity.service.api>1.0.83</wshifu.iop.activity.service.api>
        <wshifu-master-inner-api.version>2.0.113</wshifu-master-inner-api.version>
        <wshifu-order-offer-service-api.version>1.84</wshifu-order-offer-service-api.version>
        <base-address-service-api.version>1.0.35</base-address-service-api.version>
        <wshifu-order-config-service-api.version>2.0.66</wshifu-order-config-service-api.version>
        <master-manage-config-service-api.version>1.0.4</master-manage-config-service-api.version>
        <iop-marketing-config-service.version>1.0.14</iop-marketing-config-service.version>
        <iop-ad-service-api.version>1.0.35</iop-ad-service-api.version>
        <wshifu-order-config-service-api.version>2.0.62</wshifu-order-config-service-api.version>
        <wshifu-user-inner-api.version>1.1.63</wshifu-user-inner-api.version>
        <user-service-api-version>1.0.153</user-service-api-version>
        <base-privacy-phone-service.version>1.3.6</base-privacy-phone-service.version>
        <iop-equity-service-api.version>1.0.8</iop-equity-service-api.version>
        <user-order-service-api-version>1.0.260</user-order-service-api-version>
        <payment-service-api-version>1.2.39</payment-service-api-version>
        <bonus-service-api-version>1.0.8</bonus-service-api-version>
        <toc-user-voucher-service-api-version>1.0.76</toc-user-voucher-service-api-version>
        <iop-play-method-service-api-version>1.0.6</iop-play-method-service-api-version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-core</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>
            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-core-spring-boot-starter</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>
            <!--数据库链接配置-->
            <!--<dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-persistence-spring-boot-starter</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>-->
            <!--数据库链接配置-->

            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-redis-spring-boot-starter</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>
            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-rocketmq-spring-boot-starter</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>

            <dependency>
                <groupId>com.wanshifu</groupId>
                <artifactId>wshifu-framework-web-api-core-spring-boot-starter</artifactId>
                <version>${wanshfiu-framework-version}</version>
            </dependency>

            <!-- fastjson 升级 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.69</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <scm>
        <connection>scm:git:http://git.wanshifu.com/intelligeint-operation-platform/iop-inner-api.git</connection>
        <developerConnection>scm:git:http://git.wanshifu.com/intelligeint-operation-platform/iop-inner-api.git</developerConnection>
        <url>scm:git:http://git.wanshifu.com/intelligeint-operation-platform/iop-inner-api.git</url>
        <tag>HEAD</tag>
    </scm>

    <distributionManagement>
        <repository>
            <id>wanshifu-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.wanshifu.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>wanshifu-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.wanshifu.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <!--项目名称-->
        <finalName>iop-inner-api</finalName>

        <plugins>
            <!--跳过单元测试-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!-- 要将源码放上去，�?要加入这个插�? -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.plugin.version}</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.5</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--release版本发布插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules><!--自动更改�?有子模块版本-->
                    <tagNameFormat>iop-inner-api-@{project.version}</tagNameFormat><!--生成的tag名字-->
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.scm</groupId>
                        <artifactId>maven-scm-provider-jgit</artifactId>
                        <version>1.9.5</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!--取消doc生成-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9.1</version>
                <configuration>
                    <additionalparam>-Xdoclint:none</additionalparam>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>